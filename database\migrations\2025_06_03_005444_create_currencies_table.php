<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('currencies', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Saudi Riyal
            $table->string('code', 3); // SAR
            $table->string('symbol', 10); // ر.س
            $table->decimal('exchange_rate', 10, 4)->default(1.0000); // Exchange rate to base currency
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false);
            $table->string('position', 10)->default('before'); // before or after amount
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('currencies');
    }
};
