@extends('layouts.admin')

@section('title', __('Permission Change History'))

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.permissions.advanced.index') }}">{{ __('Permissions') }}</a>
                            </li>
                            <li class="breadcrumb-item active">{{ __('Change History') }}</li>
                        </ol>
                    </nav>
                    <h1 class="h3 mb-0">{{ __('Permission Change History') }}</h1>
                    <p class="text-muted">{{ __('Track all permission changes and modifications') }}</p>
                </div>
                <div>
                    <a href="{{ route('admin.permissions.advanced.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> {{ __('Back') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.permissions.advanced.logs') }}">
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">{{ __('Filter by Role') }}</label>
                                <select name="role_id" class="form-select">
                                    <option value="">{{ __('All Roles') }}</option>
                                    @foreach($roles as $role)
                                        <option value="{{ $role->id }}" {{ request('role_id') == $role->id ? 'selected' : '' }}>
                                            {{ $role->display_name ?? $role->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">{{ __('Filter by Action') }}</label>
                                <select name="action" class="form-select">
                                    <option value="">{{ __('All Actions') }}</option>
                                    @foreach($actions as $action)
                                        <option value="{{ $action }}" {{ request('action') == $action ? 'selected' : '' }}>
                                            {{ ucfirst($action) }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-filter"></i> {{ __('Filter') }}
                                    </button>
                                    <a href="{{ route('admin.permissions.advanced.logs') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times"></i> {{ __('Clear') }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Logs Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ __('Change Log') }}</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ __('Date/Time') }}</th>
                                    <th>{{ __('User') }}</th>
                                    <th>{{ __('Role') }}</th>
                                    <th>{{ __('Action') }}</th>
                                    <th>{{ __('Details') }}</th>
                                    <th>{{ __('IP Address') }}</th>
                                    <th>{{ __('Actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($logs as $log)
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ $log->created_at->format('Y-m-d') }}</strong>
                                            <br>
                                            <small class="text-muted">{{ $log->created_at->format('H:i:s') }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-secondary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ $log->user->name ?? __('Unknown') }}</h6>
                                                <small class="text-muted">{{ $log->user->email ?? '' }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                <i class="fas fa-shield-alt text-white"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ $log->role->display_name ?? $log->role->name ?? __('Deleted Role') }}</h6>
                                                <small class="text-muted">{{ $log->role->name ?? '' }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        @php
                                            $actionColors = [
                                                'granted' => 'success',
                                                'revoked' => 'danger',
                                                'updated' => 'warning',
                                                'copied' => 'info',
                                                'quick_set_applied' => 'primary'
                                            ];
                                            $color = $actionColors[$log->action] ?? 'secondary';
                                        @endphp
                                        <span class="badge bg-{{ $color }}">
                                            {{ ucfirst(str_replace('_', ' ', $log->action)) }}
                                        </span>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ $log->permission_name }}</strong>
                                            @if($log->old_permissions && $log->new_permissions)
                                                <br>
                                                <small class="text-muted">
                                                    {{ count($log->old_permissions) }} → {{ count($log->new_permissions) }} {{ __('permissions') }}
                                                </small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ $log->ip_address }}</small>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline-info"
                                                data-bs-toggle="modal"
                                                data-bs-target="#logDetailsModal{{ $log->id }}">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>

                                <!-- Log Details Modal -->
                                <div class="modal fade" id="logDetailsModal{{ $log->id }}" tabindex="-1">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">{{ __('Change Details') }}</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <h6>{{ __('Basic Information') }}</h6>
                                                        <table class="table table-sm">
                                                            <tr>
                                                                <td><strong>{{ __('Date/Time') }}:</strong></td>
                                                                <td>{{ $log->created_at->format('Y-m-d H:i:s') }}</td>
                                                            </tr>
                                                            <tr>
                                                                <td><strong>{{ __('User') }}:</strong></td>
                                                                <td>{{ $log->user->name ?? __('Unknown') }}</td>
                                                            </tr>
                                                            <tr>
                                                                <td><strong>{{ __('Role') }}:</strong></td>
                                                                <td>{{ $log->role->display_name ?? $log->role->name ?? __('Deleted Role') }}</td>
                                                            </tr>
                                                            <tr>
                                                                <td><strong>{{ __('Action') }}:</strong></td>
                                                                <td>
                                                                    <span class="badge bg-{{ $color }}">
                                                                        {{ ucfirst(str_replace('_', ' ', $log->action)) }}
                                                                    </span>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><strong>{{ __('IP Address') }}:</strong></td>
                                                                <td>{{ $log->ip_address }}</td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <h6>{{ __('Technical Details') }}</h6>
                                                        <table class="table table-sm">
                                                            <tr>
                                                                <td><strong>{{ __('Permission Name') }}:</strong></td>
                                                                <td><code>{{ $log->permission_name }}</code></td>
                                                            </tr>
                                                            <tr>
                                                                <td><strong>{{ __('User Agent') }}:</strong></td>
                                                                <td>
                                                                    <small class="text-muted">
                                                                        {{ Str::limit($log->user_agent, 50) }}
                                                                    </small>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </div>

                                                @if($log->old_permissions && $log->new_permissions)
                                                <hr>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <h6>{{ __('Before') }} ({{ count($log->old_permissions) }})</h6>
                                                        <div class="border rounded p-2" style="max-height: 200px; overflow-y: auto;">
                                                            @foreach($log->old_permissions as $permission)
                                                                <span class="badge bg-light text-dark me-1 mb-1">{{ $permission }}</span>
                                                            @endforeach
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <h6>{{ __('After') }} ({{ count($log->new_permissions) }})</h6>
                                                        <div class="border rounded p-2" style="max-height: 200px; overflow-y: auto;">
                                                            @foreach($log->new_permissions as $permission)
                                                                <span class="badge bg-light text-dark me-1 mb-1">{{ $permission }}</span>
                                                            @endforeach
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Changes Summary -->
                                                <hr>
                                                <div class="row">
                                                    <div class="col-12">
                                                        <h6>{{ __('Changes Summary') }}</h6>
                                                        @php
                                                            $added = array_diff($log->new_permissions, $log->old_permissions);
                                                            $removed = array_diff($log->old_permissions, $log->new_permissions);
                                                        @endphp

                                                        @if(count($added) > 0)
                                                        <div class="mb-2">
                                                            <strong class="text-success">{{ __('Added') }} ({{ count($added) }}):</strong>
                                                            <div>
                                                                @foreach($added as $permission)
                                                                    <span class="badge bg-success me-1 mb-1">+ {{ $permission }}</span>
                                                                @endforeach
                                                            </div>
                                                        </div>
                                                        @endif

                                                        @if(count($removed) > 0)
                                                        <div class="mb-2">
                                                            <strong class="text-danger">{{ __('Removed') }} ({{ count($removed) }}):</strong>
                                                            <div>
                                                                @foreach($removed as $permission)
                                                                    <span class="badge bg-danger me-1 mb-1">- {{ $permission }}</span>
                                                                @endforeach
                                                            </div>
                                                        </div>
                                                        @endif

                                                        @if(count($added) === 0 && count($removed) === 0)
                                                        <p class="text-muted">{{ __('No changes detected') }}</p>
                                                        @endif
                                                    </div>
                                                </div>
                                                @endif
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                                    {{ __('Close') }}
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center py-5">
                                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                                        <h5>{{ __('No change history found') }}</h5>
                                        <p class="text-muted">{{ __('Permission changes will appear here') }}</p>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        {{ $logs->appends(request()->query())->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.avatar-sm {
    width: 32px;
    height: 32px;
}

.table td {
    vertical-align: middle;
}

.badge {
    font-size: 0.75em;
}
</style>
@endpush
