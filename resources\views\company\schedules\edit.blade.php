@extends('layouts.company')

@section('title', __('Edit Schedule'))

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ __('Edit Schedule') }}</h1>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">{{ __('Update schedule information') }}</p>
                </div>
                <a href="{{ route('company.schedules.show', $schedule) }}"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="w-4 h-4 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    {{ __('Back to Schedule') }}
                </a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Edit Schedule Form -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ __('Schedule Information') }}</h3>
            </div>

            <form action="{{ route('company.schedules.update', $schedule) }}" method="POST" class="p-6 space-y-6">
                @csrf
                @method('PUT')

                <!-- Worker Selection -->
                <div>
                    <label for="worker_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {{ __('Worker') }} <span class="text-red-500">*</span>
                    </label>
                    <select name="worker_id" id="worker_id" required
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        <option value="">{{ __('Select Worker') }}</option>
                        @foreach($workers as $worker)
                            <option value="{{ $worker->id }}" {{ (old('worker_id', $schedule->worker_id) == $worker->id) ? 'selected' : '' }}>
                                {{ $worker->name }} - {{ $worker->nationality }}
                            </option>
                        @endforeach
                    </select>
                    @error('worker_id')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Schedule Type -->
                <div>
                    <label for="schedule_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {{ __('Schedule Type') }} <span class="text-red-500">*</span>
                    </label>
                    <select name="schedule_type" id="schedule_type" required
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        <option value="">{{ __('Select Schedule Type') }}</option>
                        <option value="available" {{ old('schedule_type', $schedule->schedule_type) == 'available' ? 'selected' : '' }}>
                            ✅ {{ __('Available') }}
                        </option>
                        <option value="vacation" {{ old('schedule_type', $schedule->schedule_type) == 'vacation' ? 'selected' : '' }}>
                            🏖️ {{ __('Vacation') }}
                        </option>
                        <option value="booked" {{ old('schedule_type', $schedule->schedule_type) == 'booked' ? 'selected' : '' }}>
                            📅 {{ __('Booked') }}
                        </option>
                    </select>
                    @error('schedule_type')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Booking Selection (only for booked type) -->
                <div id="booking_selection" class="{{ old('schedule_type', $schedule->schedule_type) === 'booked' ? '' : 'hidden' }}">
                    <label for="booking_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {{ __('Related Booking') }}
                    </label>
                    <select name="booking_id" id="booking_id"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        <option value="">{{ __('Select Booking') }}</option>
                        @foreach($bookings as $booking)
                            <option value="{{ $booking->id }}" {{ old('booking_id', $schedule->booking_id) == $booking->id ? 'selected' : '' }}>
                                #{{ $booking->id }} - {{ $booking->client->user->name }} ({{ $booking->start_date->format('Y-m-d') }})
                            </option>
                        @endforeach
                    </select>
                    @error('booking_id')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Date Range -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {{ __('Start Date') }} <span class="text-red-500">*</span>
                        </label>
                        <input type="datetime-local" name="start_date" id="start_date" required
                               value="{{ old('start_date', $schedule->start_date->format('Y-m-d\TH:i')) }}"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        @error('start_date')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {{ __('End Date') }} <span class="text-red-500">*</span>
                        </label>
                        <input type="datetime-local" name="end_date" id="end_date" required
                               value="{{ old('end_date', $schedule->end_date->format('Y-m-d\TH:i')) }}"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        @error('end_date')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Duration Display -->
                <div id="duration_display">
                    <div class="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-blue-500 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-sm font-medium text-blue-800 dark:text-blue-200">
                                {{ __('Duration') }}: <span id="duration_text"></span>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Quick Schedule Templates -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">{{ __('Quick Schedule Templates') }}</h4>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <button type="button" onclick="setQuickSchedule('today')"
                                class="quick-template-btn px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition duration-200">
                            {{ __('Today') }}
                        </button>
                        <button type="button" onclick="setQuickSchedule('tomorrow')"
                                class="quick-template-btn px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition duration-200">
                            {{ __('Tomorrow') }}
                        </button>
                        <button type="button" onclick="setQuickSchedule('this_week')"
                                class="quick-template-btn px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium rounded-md transition duration-200">
                            {{ __('This Week') }}
                        </button>
                        <button type="button" onclick="setQuickSchedule('this_month')"
                                class="quick-template-btn px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white text-sm font-medium rounded-md transition duration-200">
                            {{ __('This Month') }}
                        </button>
                    </div>
                    <div class="mt-3 text-xs text-gray-500 dark:text-gray-400 flex items-center">
                        <svg class="w-4 h-4 {{ app()->getLocale() === 'ar' ? 'ml-1' : 'mr-1' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        {{ __('Duration: 31 days') }}
                    </div>
                </div>

                <!-- Notes -->
                <div>
                    <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {{ __('Notes') }}
                    </label>
                    <textarea name="notes" id="notes" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                              placeholder="{{ __('Add any additional notes about this schedule...') }}">{{ old('notes', $schedule->notes) }}</textarea>
                    @error('notes')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Error Messages -->
                @if($errors->has('error'))
                    <div class="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700 rounded-lg p-4">
                        <div class="flex">
                            <svg class="w-5 h-5 text-red-400 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <p class="text-sm text-red-800 dark:text-red-200">{{ $errors->first('error') }}</p>
                        </div>
                    </div>
                @endif

                <!-- Submit Buttons -->
                <div class="flex items-center justify-end space-x-3 rtl:space-x-reverse pt-6 border-t border-gray-200 dark:border-gray-700">
                    <a href="{{ route('company.schedules.show', $schedule) }}"
                       class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        {{ __('Cancel') }}
                    </a>
                    <button type="submit"
                            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        {{ __('Update Schedule') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const scheduleTypeSelect = document.getElementById('schedule_type');
    const bookingSelection = document.getElementById('booking_selection');
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    const durationDisplay = document.getElementById('duration_display');
    const durationText = document.getElementById('duration_text');

    // Show/hide booking selection based on schedule type
    scheduleTypeSelect.addEventListener('change', function() {
        if (this.value === 'booked') {
            bookingSelection.classList.remove('hidden');
        } else {
            bookingSelection.classList.add('hidden');
        }
    });

    // Calculate and display duration
    function updateDuration() {
        const startDate = new Date(startDateInput.value);
        const endDate = new Date(endDateInput.value);

        if (startDate && endDate && endDate > startDate) {
            const diffTime = Math.abs(endDate - startDate);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            const diffHours = Math.ceil(diffTime / (1000 * 60 * 60));

            let durationString = '';
            if (diffDays >= 1) {
                durationString = diffDays + ' {{ __("days") }}';
            } else {
                durationString = diffHours + ' {{ __("hours") }}';
            }

            durationText.textContent = durationString;
            durationDisplay.classList.remove('hidden');
        } else {
            durationDisplay.classList.add('hidden');
        }
    }

    startDateInput.addEventListener('change', updateDuration);
    endDateInput.addEventListener('change', updateDuration);

    // Update end date minimum when start date changes
    startDateInput.addEventListener('change', function() {
        endDateInput.min = this.value;
    });

    // Initial setup
    updateDuration();
});

// Quick Schedule Templates Functions
function setQuickSchedule(template) {
    const now = new Date();
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    const durationInfo = document.querySelector('.mt-3.text-xs');

    let startDate, endDate, duration;

    switch(template) {
        case 'today':
            startDate = new Date(now);
            startDate.setHours(8, 0, 0, 0); // 8:00 AM
            endDate = new Date(now);
            endDate.setHours(18, 0, 0, 0); // 6:00 PM
            duration = '10 {{ __("hours") }}';
            break;

        case 'tomorrow':
            startDate = new Date(now);
            startDate.setDate(now.getDate() + 1);
            startDate.setHours(8, 0, 0, 0);
            endDate = new Date(now);
            endDate.setDate(now.getDate() + 1);
            endDate.setHours(18, 0, 0, 0);
            duration = '10 {{ __("hours") }}';
            break;

        case 'this_week':
            startDate = new Date(now);
            startDate.setHours(8, 0, 0, 0);
            endDate = new Date(now);
            endDate.setDate(now.getDate() + 6); // Next 7 days
            endDate.setHours(18, 0, 0, 0);
            duration = '7 {{ __("days") }}';
            break;

        case 'this_month':
            startDate = new Date(now);
            startDate.setHours(8, 0, 0, 0);
            endDate = new Date(now);
            endDate.setDate(now.getDate() + 30); // Next 31 days
            endDate.setHours(18, 0, 0, 0);
            duration = '31 {{ __("days") }}';
            break;
    }

    // Format dates for datetime-local input
    const formatDateTime = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        return `${year}-${month}-${day}T${hours}:${minutes}`;
    };

    startDateInput.value = formatDateTime(startDate);
    endDateInput.value = formatDateTime(endDate);

    // Update duration info
    if (durationInfo) {
        durationInfo.innerHTML = `
            <svg class="w-4 h-4 {{ app()->getLocale() === 'ar' ? 'ml-1' : 'mr-1' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            {{ __('Duration') }}: ${duration}
        `;
    }

    // Trigger duration update
    const event = new Event('change');
    startDateInput.dispatchEvent(event);
    endDateInput.dispatchEvent(event);

    // Add visual feedback
    const buttons = document.querySelectorAll('.quick-template-btn');
    buttons.forEach(btn => btn.classList.remove('ring-2', 'ring-offset-2', 'ring-blue-500'));
    event.target.classList.add('ring-2', 'ring-offset-2', 'ring-blue-500');
}
</script>
@endsection
