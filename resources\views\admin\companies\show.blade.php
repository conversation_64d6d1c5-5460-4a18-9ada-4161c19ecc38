@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">

    <!-- <PERSON> Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ $company->company_name }}</h1>
                    <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">{{ __('Company Details') }}</p>
                </div>
                <div class="flex space-x-3 rtl:space-x-reverse">
                    <a href="{{ route('admin.companies.index') }}"
                       class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-md transition duration-200">
                        <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        {{ __('Back to Companies') }}
                    </a>
                    @can('companies.edit')
                    <a href="{{ route('admin.companies.edit', $company) }}"
                       class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-200">
                        <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        {{ __('Edit Company') }}
                    </a>
                    @endcan
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Company Overview -->
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                    <div class="px-6 py-6">
                        <div class="flex items-center mb-6">
                            <div class="flex-shrink-0">
                                @if($company->logo)
                                    <img class="h-20 w-20 rounded-lg object-cover"
                                         src="{{ Storage::url($company->logo) }}"
                                         alt="{{ $company->company_name }}">
                                @else
                                    <div class="h-20 w-20 rounded-lg bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                                        <svg class="h-10 w-10 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                        </svg>
                                    </div>
                                @endif
                            </div>
                            <div class="ms-6">
                                <h2 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $company->company_name }}</h2>
                                <div class="flex items-center mt-2">
                                    <div class="flex text-yellow-400">
                                        @for($i = 1; $i <= 5; $i++)
                                            @if($i <= floor($company->rating))
                                                <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20">
                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                                </svg>
                                            @else
                                                <svg class="w-5 h-5 text-gray-300 dark:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                                </svg>
                                            @endif
                                        @endfor
                                    </div>
                                    <span class="ms-2 text-sm text-gray-600 dark:text-gray-400">{{ number_format($company->rating, 1) }} {{ __('out of 5') }}</span>
                                </div>
                                <div class="mt-2">
                                    @if($company->is_active)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                            {{ __('Active') }}
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                            {{ __('Inactive') }}
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>

                        @if($company->description)
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">{{ __('Description') }}</h3>
                            <p class="text-gray-600 dark:text-gray-400 leading-relaxed">{{ $company->description }}</p>
                        </div>
                        @endif

                        <!-- Company Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Company Information') }}</h3>
                                <dl class="space-y-3">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Commercial Register') }}</dt>
                                        <dd class="text-sm text-gray-900 dark:text-white">{{ $company->commercial_register }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Join Date') }}</dt>
                                        <dd class="text-sm text-gray-900 dark:text-white">{{ $company->join_date->format('F d, Y') }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Workers Count') }}</dt>
                                        <dd class="text-sm text-gray-900 dark:text-white">{{ $company->workers_count }}</dd>
                                    </div>
                                </dl>
                            </div>

                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Owner Information') }}</h3>
                                <dl class="space-y-3">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Name') }}</dt>
                                        <dd class="text-sm text-gray-900 dark:text-white">{{ $company->user->name }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Email') }}</dt>
                                        <dd class="text-sm text-gray-900 dark:text-white">{{ $company->user->email }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Phone') }}</dt>
                                        <dd class="text-sm text-gray-900 dark:text-white">{{ $company->user->phone ?? __('Not provided') }}</dd>
                                    </div>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                @if($company->contact_info && (
                    $company->contact_info['phone'] ||
                    $company->contact_info['email'] ||
                    $company->contact_info['address'] ||
                    $company->contact_info['website']
                ))
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg mt-6">
                    <div class="px-6 py-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Contact Information') }}</h3>
                        <dl class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @if($company->contact_info['phone'])
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Phone') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $company->contact_info['phone'] }}</dd>
                            </div>
                            @endif

                            @if($company->contact_info['email'])
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Email') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $company->contact_info['email'] }}</dd>
                            </div>
                            @endif

                            @if($company->contact_info['website'])
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Website') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">
                                    <a href="{{ $company->contact_info['website'] }}" target="_blank" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                        {{ $company->contact_info['website'] }}
                                    </a>
                                </dd>
                            </div>
                            @endif

                            @if($company->contact_info['address'])
                            <div class="md:col-span-2">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Address') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $company->contact_info['address'] }}</dd>
                            </div>
                            @endif
                        </dl>
                    </div>
                </div>
                @endif

                <!-- Bank Information -->
                @if($company->bank_info && (
                    $company->bank_info['bank_name'] ||
                    $company->bank_info['account_number'] ||
                    $company->bank_info['iban']
                ))
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg mt-6">
                    <div class="px-6 py-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Bank Information') }}</h3>
                        <dl class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            @if($company->bank_info['bank_name'])
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Bank Name') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $company->bank_info['bank_name'] }}</dd>
                            </div>
                            @endif

                            @if($company->bank_info['account_number'])
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Account Number') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $company->bank_info['account_number'] }}</dd>
                            </div>
                            @endif

                            @if($company->bank_info['iban'])
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('IBAN') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $company->bank_info['iban'] }}</dd>
                            </div>
                            @endif
                        </dl>
                    </div>
                </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Quick Stats -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                    <div class="px-6 py-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Quick Stats') }}</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('Total Workers') }}</span>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $company->workers_count }}</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('Rating') }}</span>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ number_format($company->rating, 1) }}/5</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('Member Since') }}</span>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $company->join_date->format('M Y') }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                    <div class="px-6 py-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Actions') }}</h3>
                        <div class="space-y-3">
                            @can('companies.edit')
                            <a href="{{ route('admin.companies.edit', $company) }}"
                               class="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-200">
                                <svg class="w-4 h-4 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                {{ __('Edit Company') }}
                            </a>
                            @endcan

                            @can('companies.delete')
                            <form method="POST" action="{{ route('admin.companies.destroy', $company) }}" class="w-full">
                                @csrf
                                @method('DELETE')
                                <button type="submit"
                                        class="w-full inline-flex items-center justify-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-md transition duration-200"
                                        onclick="return confirm('{{ __('Are you sure you want to delete this company?') }}')">
                                    <svg class="w-4 h-4 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                    {{ __('Delete Company') }}
                                </button>
                            </form>
                            @endcan
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
