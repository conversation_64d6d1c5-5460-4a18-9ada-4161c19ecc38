<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Services\NotificationService;

class CreateTestNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:create-test {user_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create test notifications for a user';

    /**
     * Execute the console command.
     */
    public function handle(NotificationService $notificationService)
    {
        $userId = $this->argument('user_id');
        
        if (!$userId) {
            $user = User::first();
            if (!$user) {
                $this->error('No users found in database');
                return 1;
            }
        } else {
            $user = User::find($userId);
            if (!$user) {
                $this->error("User with ID {$userId} not found");
                return 1;
            }
        }

        $this->info("Creating test notifications for user: {$user->name} (ID: {$user->id})");

        // Create different types of test notifications
        $notifications = [
            [
                'type' => 'booking_created',
                'title' => 'حجز جديد تم إنشاؤه',
                'message' => 'تم إنشاء حجز جديد #1001 وهو في انتظار التأكيد.',
                'priority' => 'high',
                'icon' => 'calendar',
                'color' => 'blue',
                'action_url' => '/admin/bookings/1',
                'action_text' => 'عرض الحجز',
            ],
            [
                'type' => 'payment_received',
                'title' => 'تم استلام الدفع',
                'message' => 'تم استلام دفعة بقيمة 500 ريال للحجز #1001.',
                'priority' => 'medium',
                'icon' => 'credit-card',
                'color' => 'green',
                'action_url' => '/admin/bookings/1',
                'action_text' => 'عرض الدفع',
            ],
            [
                'type' => 'rating_received',
                'title' => 'تقييم جديد',
                'message' => 'تم استلام تقييم 5 نجوم من العميل أحمد محمد.',
                'priority' => 'medium',
                'icon' => 'star',
                'color' => 'yellow',
                'action_url' => '/admin/ratings/1',
                'action_text' => 'عرض التقييم',
            ],
            [
                'type' => 'worker_assigned',
                'title' => 'تم تعيين عامل',
                'message' => 'تم تعيين العامل سارة أحمد للحجز #1002.',
                'priority' => 'medium',
                'icon' => 'calendar',
                'color' => 'blue',
                'action_url' => '/admin/bookings/2',
                'action_text' => 'عرض الحجز',
            ],
            [
                'type' => 'system_maintenance',
                'title' => 'صيانة مجدولة',
                'message' => 'صيانة النظام مجدولة من 2:00 ص إلى 4:00 ص. قد تكون بعض الميزات غير متاحة.',
                'priority' => 'urgent',
                'icon' => 'wrench-screwdriver',
                'color' => 'red',
                'expires_at' => now()->addHours(6),
            ],
        ];

        foreach ($notifications as $notificationData) {
            $notificationData['user_id'] = $user->id;
            $notification = $notificationService->create($notificationData);
            $this->info("Created notification: {$notification->title}");
        }

        $this->info("Successfully created " . count($notifications) . " test notifications");
        
        // Show current count
        $unreadCount = $notificationService->getUnreadCount($user);
        $this->info("User now has {$unreadCount} unread notifications");

        return 0;
    }
}
