<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Notification;
use Carbon\Carbon;

class TestNotificationsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🔔 Creating Test Notifications');
        $this->command->info('==============================');

        // Find the test user
        $testUser = User::where('email', '<EMAIL>')->first();

        if (!$testUser) {
            $this->command->error('❌ Test user (<EMAIL>) not found!');
            return;
        }

        // Create test notifications
        $notifications = [
            [
                'user_id' => $testUser->id,
                'type' => 'booking',
                'title' => 'Booking Confirmed',
                'message' => 'Your booking for cleaning service has been confirmed for tomorrow at 10:00 AM.',
                'is_read' => false,
                'created_at' => Carbon::now()->subHours(2),
            ],
            [
                'user_id' => $testUser->id,
                'type' => 'payment',
                'title' => 'Payment Successful',
                'message' => 'Your payment of $200 has been processed successfully.',
                'is_read' => false,
                'created_at' => Carbon::now()->subHours(5),
            ],
            [
                'user_id' => $testUser->id,
                'type' => 'rating',
                'title' => 'Please Rate Your Service',
                'message' => 'How was your recent cleaning service? Please take a moment to rate your experience.',
                'is_read' => true,
                'created_at' => Carbon::now()->subDays(1),
            ],
            [
                'user_id' => $testUser->id,
                'type' => 'booking',
                'title' => 'Service Completed',
                'message' => 'Your cleaning service has been completed. Thank you for choosing our service!',
                'is_read' => true,
                'created_at' => Carbon::now()->subDays(2),
            ],
            [
                'user_id' => $testUser->id,
                'type' => 'general',
                'title' => 'New Features Available',
                'message' => 'We have added new features to improve your experience. Check them out!',
                'is_read' => false,
                'created_at' => Carbon::now()->subDays(3),
            ],
        ];

        foreach ($notifications as $notificationData) {
            Notification::create($notificationData);
            $this->command->info("✅ Created: {$notificationData['title']}");
        }

        $this->command->info('');
        $this->command->info('🎉 Test notifications created successfully!');
        $this->command->info('📊 Summary:');
        $this->command->info("   • User notifications: " . Notification::where('user_id', $testUser->id)->count());
        $this->command->info("   • Total notifications: " . Notification::count());
        $this->command->info("   • Unread notifications: " . Notification::where('user_id', $testUser->id)->where('is_read', false)->count());
        $this->command->info('');
        $this->command->info('🔗 Test the notifications:');
        $this->command->info('   • <NAME_EMAIL>');
        $this->command->info('   • Visit /client/notifications');
    }
}
