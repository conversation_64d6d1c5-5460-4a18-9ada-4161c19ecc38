<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use Illuminate\Http\Request;

class NotificationController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            $user = auth()->user();
            if (!$user || (!$user->hasRole('client') && !$user->hasRole('user'))) {
                abort(403, 'Access denied. Client or User role required.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of user's notifications.
     */
    public function index()
    {
        $user = auth()->user();

        // Get notifications for this user only
        $notifications = Notification::where('user_id', $user->id)
                                   ->orderBy('created_at', 'desc')
                                   ->paginate(15);

        return view('client.notifications.index', compact('notifications'));
    }

    /**
     * Display the specified notification.
     */
    public function show(Notification $notification)
    {
        $user = auth()->user();

        // Ensure user can only view their own notifications or global ones
        if ($notification->user_id !== null && $notification->user_id !== $user->id) {
            abort(403);
        }

        // Mark as read
        if ($notification->user_id === $user->id && !$notification->is_read) {
            $notification->update(['is_read' => true]);
        }

        return view('client.notifications.show', compact('notification'));
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(Notification $notification)
    {
        $user = auth()->user();

        // Ensure user can only mark their own notifications
        if ($notification->user_id !== $user->id) {
            abort(403);
        }

        $notification->update(['is_read' => true]);

        return response()->json(['success' => true]);
    }

    /**
     * Mark all notifications as read
     */
    public function markAllAsRead()
    {
        $user = auth()->user();

        Notification::where('user_id', $user->id)
                   ->where('is_read', false)
                   ->update(['is_read' => true]);

        return response()->json(['success' => true]);
    }

    /**
     * Get unread notifications count
     */
    public function getUnreadCount()
    {
        $user = auth()->user();

        $count = Notification::where('user_id', $user->id)
                            ->where('is_read', false)
                            ->count();

        return response()->json(['count' => $count]);
    }

    /**
     * Remove the specified notification.
     */
    public function destroy(Notification $notification)
    {
        $user = auth()->user();

        // Ensure user can only delete their own notifications
        if ($notification->user_id !== $user->id) {
            abort(403);
        }

        $notification->delete();

        return redirect()->route('client.notifications.index')
                        ->with('success', __('Notification deleted successfully.'));
    }
}
