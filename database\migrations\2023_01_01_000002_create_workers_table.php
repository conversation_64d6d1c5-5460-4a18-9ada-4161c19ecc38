<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('workers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained()->cascadeOnDelete();
            $table->string('name');
            $table->string('email')->unique();
            $table->string('phone')->unique();
            $table->string('password');
            $table->string('photo')->nullable();
            $table->enum('gender', ['male', 'female']);
            $table->integer('age');
            $table->string('nationality'); // Keep for backward compatibility
            $table->foreignId('nationality_id')->nullable()->constrained('nationalities'); // Added nationality_id
            $table->json('skills')->nullable();
            $table->decimal('monthly_rate', 10, 2);
            $table->text('bio')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamp('email_verified_at')->nullable();
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('workers');
    }
};
