<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique(); // default_currency, app_name, etc.
            $table->text('value')->nullable(); // Setting value
            $table->string('type')->default('string'); // string, boolean, integer, json
            $table->string('group')->default('general'); // general, currency, payment, etc.
            $table->text('description')->nullable(); // Description for admin
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
