<?php

namespace App\Http\Controllers;

use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Permission;

class PermissionsController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:permissions.view')->only(['index', 'show']);
        $this->middleware('permission:permissions.create')->only(['create', 'store']);
        $this->middleware('permission:permissions.edit')->only(['edit', 'update']);
        $this->middleware('permission:permissions.delete')->only(['destroy']);
    }

    public function index(Request $request)
    {
        $query = Permission::with('roles');

        // البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where('name', 'like', "%{$search}%");
        }

        // ترتيب
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $permissions = $query->paginate(15)->withQueryString();

        return view('permissions.index', compact('permissions'));
    }

    public function create()
    {
        return view('permissions.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255', 'unique:permissions'],
            'guard_name' => ['required', 'string', 'max:255'],
        ]);

        $permission = Permission::create([
            'name' => $request->name,
            'guard_name' => $request->guard_name,
        ]);

        // تسجيل النشاط
        ActivityLog::log('permission.created', "تم إنشاء الصلاحية: {$permission->name}", $permission);

        return redirect()->route('permissions.index')->with('success', 'تم إنشاء الصلاحية بنجاح');
    }

    public function show(Permission $permission)
    {
        return view('permissions.show', compact('permission'));
    }

    public function edit(Permission $permission)
    {
        return view('permissions.edit', compact('permission'));
    }

    public function update(Request $request, Permission $permission)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255', 'unique:permissions,name,'.$permission->id],
            'guard_name' => ['required', 'string', 'max:255'],
        ]);

        $permission->update([
            'name' => $request->name,
            'guard_name' => $request->guard_name,
        ]);

        // تسجيل النشاط
        ActivityLog::log('permission.updated', "تم تحديث الصلاحية: {$permission->name}", $permission);

        return redirect()->route('permissions.index')->with('success', 'تم تحديث الصلاحية بنجاح');
    }

    public function destroy(Permission $permission)
    {
        // التحقق من عدم وجود أدوار مرتبطة بهذه الصلاحية
        if ($permission->roles()->count() > 0) {
            return redirect()->route('permissions.index')->with('error', 'لا يمكن حذف الصلاحية لأنها مرتبطة بأدوار');
        }

        // تسجيل النشاط قبل الحذف
        ActivityLog::log('permission.deleted', "تم حذف الصلاحية: {$permission->name}", $permission);

        $permission->delete();

        return redirect()->route('permissions.index')->with('success', 'تم حذف الصلاحية بنجاح');
    }
}
