<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Traits\CreatesClientProfile;
use Illuminate\Http\Request;

class PaymentController extends Controller
{
    use CreatesClientProfile;

    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            $user = auth()->user();
            if (!$user || (!$user->hasRole('client') && !$user->hasRole('user'))) {
                abort(403, 'Access denied. Client or User role required.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of client payments.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $client = $this->ensureClientProfile($user);

        if (!$client) {
            return redirect()->route('client.dashboard')->with('error', __('Client profile not found.'));
        }

        $query = Booking::with(['worker', 'company'])
                       ->where('client_id', $client->id)
                       ->whereNotNull('transaction_id');

        // Filter by payment status
        if ($request->filled('status')) {
            $query->where('payment_status', $request->status);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('transaction_id', 'like', "%{$search}%")
                  ->orWhere('id', 'like', "%{$search}%")
                  ->orWhereHas('worker', function ($workerQuery) use ($search) {
                      $workerQuery->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('company', function ($companyQuery) use ($search) {
                      $companyQuery->where('company_name', 'like', "%{$search}%");
                  });
            });
        }

        $payments = $query->orderBy('updated_at', 'desc')->paginate(10);

        // Calculate statistics
        $stats = [
            'total_paid' => Booking::where('client_id', $client->id)
                                  ->where('payment_status', 'paid')
                                  ->sum('total_amount'),
            'total_pending' => Booking::where('client_id', $client->id)
                                    ->where('payment_status', 'pending')
                                    ->sum('total_amount'),
            'total_transactions' => Booking::where('client_id', $client->id)
                                          ->whereNotNull('transaction_id')
                                          ->count(),
        ];

        return view('client.payments.index', compact('payments', 'stats'));
    }

    /**
     * Display payment receipt.
     */
    public function receipt($transactionId)
    {
        $user = auth()->user();
        $client = $this->ensureClientProfile($user);

        if (!$client) {
            return redirect()->route('client.dashboard')->with('error', __('Client profile not found.'));
        }

        $booking = Booking::with(['worker', 'company', 'client.user'])
                         ->where('client_id', $client->id)
                         ->where('transaction_id', $transactionId)
                         ->first();

        if (!$booking) {
            abort(404, __('Payment receipt not found.'));
        }

        return view('client.payments.receipt', compact('booking'));
    }

    /**
     * Download payment receipt as PDF.
     */
    public function downloadReceipt($transactionId)
    {
        $user = auth()->user();
        $client = $this->ensureClientProfile($user);

        if (!$client) {
            return redirect()->route('client.dashboard')->with('error', __('Client profile not found.'));
        }

        $booking = Booking::with(['worker', 'company', 'client.user'])
                         ->where('client_id', $client->id)
                         ->where('transaction_id', $transactionId)
                         ->first();

        if (!$booking) {
            abort(404, __('Payment receipt not found.'));
        }

        // Return PDF-optimized view for printing
        return view('client.payments.receipt-pdf', compact('booking'));
    }

    /**
     * Show payment details.
     */
    public function show($bookingId)
    {
        $user = auth()->user();
        $client = $this->ensureClientProfile($user);

        if (!$client) {
            return redirect()->route('client.dashboard')->with('error', __('Client profile not found.'));
        }

        $booking = Booking::with(['worker', 'company'])
                         ->where('client_id', $client->id)
                         ->where('id', $bookingId)
                         ->first();

        if (!$booking) {
            abort(404, __('Payment not found.'));
        }

        return view('client.payments.show', compact('booking'));
    }
}
