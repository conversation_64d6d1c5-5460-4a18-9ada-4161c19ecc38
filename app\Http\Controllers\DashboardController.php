<?php

namespace App\Http\Controllers;

use App\Models\ActivityLog;
use App\Models\User;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class DashboardController extends Controller
{
    public function index()
    {
        // إحصائيات عامة
        $stats = [
            'total_users' => User::count(),
            'active_users' => User::where('status', true)->count(),
            'inactive_users' => User::where('status', false)->count(),
            'total_roles' => Role::count(),
            'total_permissions' => Permission::count(),
        ];

        // المستخدمون الجدد (آخر 7 أيام)
        $newUsers = User::where('created_at', '>=', now()->subDays(7))
                       ->orderBy('created_at', 'desc')
                       ->take(5)
                       ->get();

        // آخر الأنشطة
        $recentActivities = ActivityLog::with('user')
                                     ->latest()
                                     ->take(10)
                                     ->get();

        // إحصائيات المستخدمين حسب الأدوار
        $usersByRole = Role::withCount('users')->get();

        // إحصائيات تسجيل الدخول (آخر 30 يوم)
        $loginStats = User::whereNotNull('last_login_at')
                         ->where('last_login_at', '>=', now()->subDays(30))
                         ->selectRaw('DATE(last_login_at) as date, COUNT(*) as count')
                         ->groupBy('date')
                         ->orderBy('date')
                         ->get();

        return view('dashboard', compact(
            'stats',
            'newUsers',
            'recentActivities',
            'usersByRole',
            'loginStats'
        ));
    }

    public function getChartData(Request $request)
    {
        $type = $request->get('type', 'users');
        $period = $request->get('period', '7'); // أيام

        switch ($type) {
            case 'users':
                return $this->getUsersChartData($period);
            case 'activities':
                return $this->getActivitiesChartData($period);
            default:
                return response()->json(['error' => 'Invalid chart type'], 400);
        }
    }

    private function getUsersChartData($period)
    {
        $data = User::selectRaw('DATE(created_at) as date, COUNT(*) as count')
                   ->where('created_at', '>=', now()->subDays($period))
                   ->groupBy('date')
                   ->orderBy('date')
                   ->get();

        return response()->json([
            'labels' => $data->pluck('date'),
            'data' => $data->pluck('count'),
        ]);
    }

    private function getActivitiesChartData($period)
    {
        $data = ActivityLog::selectRaw('DATE(created_at) as date, COUNT(*) as count')
                          ->where('created_at', '>=', now()->subDays($period))
                          ->groupBy('date')
                          ->orderBy('date')
                          ->get();

        return response()->json([
            'labels' => $data->pluck('date'),
            'data' => $data->pluck('count'),
        ]);
    }
}
