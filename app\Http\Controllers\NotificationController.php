<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class NotificationController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->middleware('auth');
        // Allow all authenticated users to view their own notifications
        // $this->middleware('permission:notifications.view')->only(['index', 'show']);
        $this->middleware('permission:notifications.create')->only(['create', 'store']);
        $this->middleware('permission:notifications.edit')->only(['edit', 'update']);
        $this->middleware('permission:notifications.delete')->only(['destroy']);
        $this->notificationService = $notificationService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        // Determine the correct query based on user role and route
        if ($request->route()->getPrefix() === 'company' && $user->hasRole('company')) {
            // For company users, show only company-related notifications
            $query = Notification::where(function($q) use ($user) {
                $q->where('user_id', $user->id)
                  ->orWhere('type', 'company')
                  ->orWhere('recipient_type', 'company')
                  ->orWhere('company_id', $user->company_id ?? null);
            });
        } elseif ($request->route()->getPrefix() === 'client' && $user->hasAnyRole(['client', 'user'])) {
            // For client users, show only client-related notifications
            $query = Notification::where(function($q) use ($user) {
                $q->where('user_id', $user->id)
                  ->orWhere('type', 'client')
                  ->orWhere('recipient_type', 'client');
            });
        } else {
            // For admin users, show notifications for the current user or all if admin
            if ($user->hasAnyRole(['admin', 'editor', 'super-admin'])) {
                $query = Notification::query(); // Admin can see all notifications
            } else {
                $query = Notification::where('user_id', $user->id);
            }
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('message', 'like', "%{$search}%");
            });
        }

        // Filter by read status
        if ($request->filled('status')) {
            if ($request->status === 'read') {
                $query->read();
            } elseif ($request->status === 'unread') {
                $query->unread();
            }
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->byType($request->type);
        }

        // Filter by priority
        if ($request->filled('priority')) {
            $query->byPriority($request->priority);
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $notifications = $query->notExpired()->paginate(15)->withQueryString();

        // Determine the correct view based on route prefix
        $viewPrefix = $request->route()->getPrefix() ?? 'admin';
        $viewName = $viewPrefix . '.notifications.index';

        return view($viewName, compact('notifications'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.notifications.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'type' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'user_id' => 'required|exists:users,id',
            'priority' => 'required|in:low,medium,high,urgent',
            'action_url' => 'nullable|url',
            'action_text' => 'nullable|string|max:255',
            'expires_at' => 'nullable|date|after:now',
        ]);

        $this->notificationService->create($validated);

        return redirect()->route('admin.notifications.index')
                        ->with('success', __('Notification created successfully.'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Notification $notification)
    {
        // Mark as read when viewed
        $this->notificationService->markAsRead($notification);

        return view('admin.notifications.show', compact('notification'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Notification $notification)
    {
        return view('admin.notifications.edit', compact('notification'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Notification $notification)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'priority' => 'required|in:low,medium,high,urgent',
            'action_url' => 'nullable|url',
            'action_text' => 'nullable|string|max:255',
            'expires_at' => 'nullable|date|after:now',
        ]);

        $notification->update($validated);

        return redirect()->route('admin.notifications.index')
                        ->with('success', __('Notification updated successfully.'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Notification $notification)
    {
        $notification->delete();

        return redirect()->route('admin.notifications.index')
                        ->with('success', __('Notification deleted successfully.'));
    }

    /**
     * Mark notification as read (API)
     */
    public function markAsRead(Notification $notification): JsonResponse
    {
        $this->notificationService->markAsRead($notification);

        return response()->json([
            'success' => true,
            'message' => __('Notification marked as read.')
        ]);
    }

    /**
     * Mark notification as unread (API)
     */
    public function markAsUnread(Notification $notification): JsonResponse
    {
        $notification->markAsUnread();

        return response()->json([
            'success' => true,
            'message' => __('Notification marked as unread.')
        ]);
    }

    /**
     * Mark notification as read (Web)
     */
    public function markAsReadWeb(Request $request, Notification $notification)
    {
        $this->notificationService->markAsRead($notification);

        // Determine redirect route based on current route prefix
        $prefix = $request->route()->getPrefix() ?? 'admin';
        $redirectRoute = $prefix . '.notifications.index';

        return redirect()->route($redirectRoute)->with('success', __('تم تحديد الإشعار كمقروء.'));
    }

    /**
     * Mark notification as unread (Web)
     */
    public function markAsUnreadWeb(Request $request, Notification $notification)
    {
        $this->notificationService->markAsUnread($notification);

        // Determine redirect route based on current route prefix
        $prefix = $request->route()->getPrefix() ?? 'admin';
        $redirectRoute = $prefix . '.notifications.index';

        return redirect()->route($redirectRoute)->with('success', __('تم تحديد الإشعار كغير مقروء.'));
    }

    /**
     * Mark all notifications as read (API)
     */
    public function markAllAsRead(): JsonResponse
    {
        $this->notificationService->markAllAsRead(Auth::user());

        return response()->json([
            'success' => true,
            'message' => __('All notifications marked as read.')
        ]);
    }

    /**
     * Mark all notifications as read (Web)
     */
    public function markAllAsReadWeb(Request $request)
    {
        $this->notificationService->markAllAsRead(Auth::user());

        // Determine redirect route based on current route prefix
        $prefix = $request->route()->getPrefix() ?? 'admin';
        $redirectRoute = $prefix . '.notifications.index';

        return redirect()->route($redirectRoute)->with('success', __('تم تحديد جميع الإشعارات كمقروءة.'));
    }

    /**
     * Get unread notifications count
     */
    public function getUnreadCount(): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user) {
                Log::info('No authenticated user found for notifications count');
                return response()->json([
                    'count' => 0,
                    'message' => 'User not authenticated'
                ]);
            }

            Log::info('Getting notifications count for user', [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'user_email' => $user->email
            ]);

            // Direct query to debug
            $directCount = \App\Models\Notification::where('user_id', $user->id)
                                                  ->where('is_read', false)
                                                  ->count();

            Log::info('Direct count query result', ['direct_count' => $directCount]);

            $count = $this->notificationService->getUnreadCount($user);

            Log::info('Service count result', ['service_count' => $count, 'user_id' => $user->id]);

            return response()->json([
                'count' => $count,
                'direct_count' => $directCount,
                'user_id' => $user->id,
                'user_name' => $user->name
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting notifications count', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'count' => 0,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get recent notifications for dropdown
     */
    public function getRecent(): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json(['notifications' => []]);
            }

            $notifications = $this->notificationService->getRecent($user, 5);

            return response()->json([
                'notifications' => $notifications->map(function ($notification) {
                    return [
                        'id' => $notification->id,
                        'title' => $notification->title,
                        'message' => $notification->message,
                        'type' => $notification->type,
                        'is_read' => $notification->is_read,
                        'created_at' => $notification->created_at->diffForHumans(),
                        'action_url' => $notification->action_url,
                        'icon' => $notification->icon,
                        'color' => $notification->color,
                        'priority_color' => $notification->priority_color,
                    ];
                })
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'notifications' => [],
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
