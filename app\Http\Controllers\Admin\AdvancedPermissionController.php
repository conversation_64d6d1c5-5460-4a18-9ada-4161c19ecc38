<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PermissionGroup;
use App\Models\PermissionLog;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Auth;


class AdvancedPermissionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('permission:permissions.view')->only(['index', 'show']);
        $this->middleware('permission:permissions.edit')->only(['edit', 'update']);
        $this->middleware('permission:permissions.create')->only(['create', 'store']);
        $this->middleware('permission:permissions.delete')->only(['destroy']);
    }

    /**
     * عرض قائمة الأدوار مع إحصائيات
     */
    public function index(Request $request)
    {
        $search = $request->get('search');

        $roles = Role::with(['permissions', 'users'])
            ->when($search, function ($query, $search) {
                return $query->where('name', 'like', "%{$search}%")
                           ->orWhere('display_name', 'like', "%{$search}%");
            })
            ->paginate(10);

        $stats = [
            'total_roles' => Role::count(),
            'total_permissions' => Permission::count(),
            'total_groups' => 0,
            'recent_changes' => 0
        ];

        return view('admin.permissions.advanced.index', compact('roles', 'stats', 'search'));
    }

    /**
     * عرض تفاصيل دور معين مع صلاحياته
     */
    public function show(Role $role)
    {
        $role->load(['permissions', 'users']);
        $groups = PermissionGroup::with('permissions')->ordered()->get();

        // تجميع الصلاحيات حسب المجموعات
        $rolePermissions = $role->permissions->pluck('id')->toArray();



        return view('admin.permissions.advanced.show', compact('role', 'groups', 'rolePermissions'));
    }

    /**
     * تحرير صلاحيات دور معين
     */
    public function edit(Role $role)
    {
        $role->load('permissions');
        $groups = PermissionGroup::with('permissions')->ordered()->get();
        $rolePermissions = $role->permissions->pluck('id')->toArray();

        return view('admin.permissions.advanced.edit', compact('role', 'groups', 'rolePermissions'));
    }

    /**
     * تحديث صلاحيات الدور
     */
    public function update(Request $request, Role $role)
    {
        $request->validate([
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,id'
        ]);

        $oldPermissions = $role->permissions->pluck('name')->toArray();

        // تحديث الصلاحيات
        $permissions = Permission::whereIn('id', $request->permissions ?? [])->get();
        $role->syncPermissions($permissions);

        $newPermissions = $role->fresh()->permissions->pluck('name')->toArray();

        // تسجيل التغيير
        PermissionLog::create([
            'user_id' => Auth::id(),
            'role_id' => $role->id,
            'action' => 'updated',
            'permission_name' => 'bulk_update',
            'old_permissions' => $oldPermissions,
            'new_permissions' => $newPermissions,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent()
        ]);

        return redirect()->route('admin.permissions.advanced.show', $role)
                        ->with('success', 'تم تحديث الصلاحيات بنجاح!');
    }

    /**
     * نسخ صلاحيات من دور إلى آخر
     */
    public function copyPermissions(Request $request)
    {
        $request->validate([
            'from_role' => 'required|exists:roles,id',
            'to_role' => 'required|exists:roles,id|different:from_role'
        ]);

        $fromRole = Role::findOrFail($request->from_role);
        $toRole = Role::findOrFail($request->to_role);

        $oldPermissions = $toRole->permissions->pluck('name')->toArray();

        // نسخ الصلاحيات
        $toRole->syncPermissions($fromRole->permissions);

        $newPermissions = $toRole->fresh()->permissions->pluck('name')->toArray();

        // تسجيل التغيير
        PermissionLog::create([
            'user_id' => Auth::id(),
            'role_id' => $toRole->id,
            'action' => 'copied',
            'permission_name' => "copied_from_{$fromRole->name}",
            'old_permissions' => $oldPermissions,
            'new_permissions' => $newPermissions,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent()
        ]);

        return redirect()->back()->with('success', __('Permissions copied successfully'));
    }

    /**
     * عرض سجل التغييرات
     */
    public function logs(Request $request)
    {
        $logs = PermissionLog::with(['user', 'role'])
            ->when($request->role_id, function ($query, $roleId) {
                return $query->where('role_id', $roleId);
            })
            ->when($request->action, function ($query, $action) {
                return $query->where('action', $action);
            })
            ->latest()
            ->paginate(20);

        $roles = Role::all();
        $actions = ['granted', 'revoked', 'updated', 'copied'];

        return view('admin.permissions.advanced.logs', compact('logs', 'roles', 'actions'));
    }

    /**
     * إحصائيات الصلاحيات
     */
    public function statistics()
    {
        $stats = [
            'roles_count' => Role::count(),
            'permissions_count' => Permission::count(),
            'groups_count' => 0,
            'logs_count' => 0,
            'recent_changes' => 0,
            'most_active_roles' => collect(),
            'permissions_by_group' => collect()
        ];

        return view('admin.permissions.advanced.statistics', compact('stats'));
    }



    /**
     * تطبيق مجموعة صلاحيات سريعة
     */
    public function applyQuickSet(Request $request, Role $role)
    {
        $request->validate([
            'quick_set' => 'required|in:admin,editor,viewer,custom'
        ]);

        $quickSets = [
            'admin' => Permission::all(),
            'editor' => Permission::where('name', 'not like', '%.delete')->get(),
            'viewer' => Permission::where('name', 'like', '%.view')->get(),
            'custom' => collect()
        ];

        $permissions = $quickSets[$request->quick_set];
        $oldPermissions = $role->permissions->pluck('name')->toArray();

        $role->syncPermissions($permissions);

        $newPermissions = $role->fresh()->permissions->pluck('name')->toArray();

        // تسجيل التغيير
        PermissionLog::create([
            'user_id' => Auth::id(),
            'role_id' => $role->id,
            'action' => 'quick_set_applied',
            'permission_name' => $request->quick_set,
            'old_permissions' => $oldPermissions,
            'new_permissions' => $newPermissions,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent()
        ]);

        return redirect()->back()->with('success', __('Quick permission set applied successfully'));
    }
}
