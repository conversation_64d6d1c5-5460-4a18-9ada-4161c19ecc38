<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RedirectBasedOnRole
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (auth()->check()) {
            $user = auth()->user();

            // إذا كان المستخدم من نوع شركة ويحاول الوصول للوحة الأدمن أو العميل
            if ($user->hasRole('company') && ($request->is('admin*') || $request->is('client*'))) {
                return redirect()->route('company.dashboard');
            }

            // إذا كان المستخدم عادي ويحاول الوصول للوحة الأدمن أو الشركة
            if ($user->hasRole('user') && ($request->is('admin*') || $request->is('company*'))) {
                return redirect()->route('client.dashboard');
            }

            // إذا كان المستخدم أدمن ويحاول الوصول لداشبورد الشركة أو العميل
            if ($user->hasAnyRole(['admin', 'editor', 'super-admin']) && ($request->is('company*') || $request->is('client*'))) {
                return redirect()->route('dashboard');
            }
        }

        return $next($request);
    }
}
