<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Spatie\Permission\Models\Permission;

class GiveUserPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:give-permissions {user_id=1}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Give all permissions to a user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->argument('user_id');
        $user = User::find($userId);

        if (!$user) {
            $this->error("User with ID {$userId} not found!");
            return 1;
        }

        $this->info("Giving permissions to user: {$user->name} ({$user->email})");

        // إعطاء جميع الصلاحيات
        $permissions = Permission::all();
        $this->info("Total permissions available: " . $permissions->count());

        $user->givePermissionTo($permissions);

        // إعطاء دور Super Admin
        $user->assignRole('Super Admin');

        $this->info("User now has " . $user->getAllPermissions()->count() . " permissions");
        $this->info("User roles: " . $user->roles->pluck('name')->join(', '));

        $this->success("All permissions granted successfully!");

        return 0;
    }
}
