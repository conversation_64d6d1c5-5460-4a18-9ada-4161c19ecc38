<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Schedule extends Model
{
    protected $fillable = [
        'worker_id',
        'start_date',
        'end_date',
        'schedule_type',
        'booking_id',
        'notes',
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
    ];

    public function worker(): Belong<PERSON>T<PERSON>
    {
        return $this->belongsTo(Worker::class);
    }

    public function booking(): BelongsTo
    {
        return $this->belongsTo(Booking::class);
    }

    public function getScheduleTypeTextAttribute(): string
    {
        return match($this->schedule_type) {
            'available' => __('Available'),
            'booked' => __('Booked'),
            'vacation' => __('Vacation'),
            default => __('Unknown'),
        };
    }
}
