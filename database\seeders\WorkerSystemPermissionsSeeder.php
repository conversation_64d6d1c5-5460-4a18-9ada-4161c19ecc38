<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class WorkerSystemPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $modules = [
            [
                'name' => 'clients',
                'display_name' => 'Client Management',
                'description' => 'Manage clients and their profiles',
                'icon' => 'user-group',
                'sort_order' => 12,
            ],
            [
                'name' => 'workers',
                'display_name' => 'Worker Management',
                'description' => 'Manage workers and their profiles',
                'icon' => 'users',
                'sort_order' => 13,
            ],
            [
                'name' => 'bookings',
                'display_name' => 'Booking Management',
                'description' => 'Manage bookings and reservations',
                'icon' => 'calendar',
                'sort_order' => 14,
            ],
            [
                'name' => 'schedules',
                'display_name' => 'Schedule Management',
                'description' => 'Manage worker schedules',
                'icon' => 'clock',
                'sort_order' => 15,
            ],
            [
                'name' => 'ratings',
                'display_name' => 'Rating Management',
                'description' => 'Manage ratings and reviews',
                'icon' => 'star',
                'sort_order' => 16,
            ],
        ];

        foreach ($modules as $module) {
            // Check if permission group already exists
            $existingGroup = DB::table('permission_groups')->where('name', $module['name'])->first();

            if ($existingGroup) {
                $groupId = $existingGroup->id;
            } else {
                // Add permission group
                $groupId = DB::table('permission_groups')->insertGetId([
                    'name' => $module['name'],
                    'display_name' => $module['display_name'],
                    'description' => $module['description'],
                    'icon' => $module['icon'],
                    'sort_order' => $module['sort_order'],
                    'is_protected' => false,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            // Add permissions for each module
            $permissionNames = [
                $module['name'] . '.view',
                $module['name'] . '.create',
                $module['name'] . '.edit',
                $module['name'] . '.delete'
            ];

            foreach ($permissionNames as $permissionName) {
                $existingPermission = DB::table('permissions')->where('name', $permissionName)->first();

                if (!$existingPermission) {
                    DB::table('permissions')->insert([
                        'name' => $permissionName,
                        'group_id' => $groupId,
                        'guard_name' => 'web',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }
    }
}
