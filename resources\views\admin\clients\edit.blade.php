@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Page Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                    {{ __('Edit Client') }}: {{ $client->user->name }}
                </h1>
                <div class="flex space-x-3">
                    <a href="{{ route('admin.clients.index') }}" 
                       class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-md transition duration-200">
                        <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        {{ __('Back to Clients') }}
                    </a>
                    <a href="{{ route('admin.clients.show', $client) }}" 
                       class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-200">
                        <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        {{ __('View Client') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6">
                <form method="POST" action="{{ route('admin.clients.update', $client) }}">
                    @csrf
                    @method('PUT')

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- User Selection -->
                        <div class="md:col-span-2">
                            <label for="user_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Select User') }} <span class="text-red-500">*</span>
                            </label>
                            <select name="user_id" 
                                    id="user_id" 
                                    required
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('user_id') border-red-500 @enderror">
                                <option value="">{{ __('Choose a user') }}</option>
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}" {{ old('user_id', $client->user_id) == $user->id ? 'selected' : '' }}>
                                        {{ $user->name }} ({{ $user->email }})
                                    </option>
                                @endforeach
                            </select>
                            @error('user_id')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Children Count -->
                        <div>
                            <label for="children_count" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Number of Children') }} <span class="text-red-500">*</span>
                            </label>
                            <input type="number" 
                                   name="children_count" 
                                   id="children_count" 
                                   value="{{ old('children_count', $client->children_count) }}" 
                                   min="0"
                                   required
                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('children_count') border-red-500 @enderror">
                            @error('children_count')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- People Count -->
                        <div>
                            <label for="people_count" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Number of People') }} <span class="text-red-500">*</span>
                            </label>
                            <input type="number" 
                                   name="people_count" 
                                   id="people_count" 
                                   value="{{ old('people_count', $client->people_count) }}" 
                                   min="1"
                                   required
                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('people_count') border-red-500 @enderror">
                            @error('people_count')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Home Size -->
                        <div>
                            <label for="home_size" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Home Size') }}
                            </label>
                            <select name="home_size" 
                                    id="home_size"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('home_size') border-red-500 @enderror">
                                <option value="">{{ __('Select home size') }}</option>
                                <option value="small" {{ old('home_size', $client->home_size) == 'small' ? 'selected' : '' }}>{{ __('Small') }}</option>
                                <option value="medium" {{ old('home_size', $client->home_size) == 'medium' ? 'selected' : '' }}>{{ __('Medium') }}</option>
                                <option value="large" {{ old('home_size', $client->home_size) == 'large' ? 'selected' : '' }}>{{ __('Large') }}</option>
                                <option value="villa" {{ old('home_size', $client->home_size) == 'villa' ? 'selected' : '' }}>{{ __('Villa') }}</option>
                            </select>
                            @error('home_size')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Floors Count -->
                        <div>
                            <label for="floors_count" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Number of Floors') }} <span class="text-red-500">*</span>
                            </label>
                            <input type="number" 
                                   name="floors_count" 
                                   id="floors_count" 
                                   value="{{ old('floors_count', $client->floors_count) }}" 
                                   min="1"
                                   required
                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('floors_count') border-red-500 @enderror">
                            @error('floors_count')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Has Yard -->
                        <div class="md:col-span-2">
                            <div class="flex items-center">
                                <!-- Hidden input to ensure a value is always sent -->
                                <input type="hidden" name="has_yard" value="0">
                                <input type="checkbox" 
                                       name="has_yard" 
                                       id="has_yard" 
                                       value="1"
                                       {{ old('has_yard', $client->has_yard) ? 'checked' : '' }}
                                       class="rounded border-gray-300 dark:border-gray-600 dark:bg-gray-700 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <label for="has_yard" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                    {{ __('Has Yard') }}
                                </label>
                            </div>
                            @error('has_yard')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Additional Info -->
                        <div class="md:col-span-2">
                            <label for="additional_info" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Additional Information') }}
                            </label>
                            <textarea name="additional_info" 
                                      id="additional_info" 
                                      rows="4"
                                      class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('additional_info') border-red-500 @enderror">{{ old('additional_info', $client->additional_info) }}</textarea>
                            @error('additional_info')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Is Active -->
                        <div class="md:col-span-2">
                            <div class="flex items-center">
                                <!-- Hidden input to ensure a value is always sent -->
                                <input type="hidden" name="is_active" value="0">
                                <input type="checkbox" 
                                       name="is_active" 
                                       id="is_active" 
                                       value="1"
                                       {{ old('is_active', $client->is_active) ? 'checked' : '' }}
                                       class="rounded border-gray-300 dark:border-gray-600 dark:bg-gray-700 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <label for="is_active" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                    {{ __('Active Client') }}
                                </label>
                            </div>
                            @error('is_active')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="flex items-center justify-end mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                        <a href="{{ route('admin.clients.show', $client) }}" 
                           class="bg-gray-500 hover:bg-gray-700 dark:bg-gray-600 dark:hover:bg-gray-800 text-white font-bold py-2 px-4 rounded-lg mr-3 transition">
                            {{ __('Cancel') }}
                        </a>
                        <button type="submit" 
                                class="bg-blue-500 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white font-bold py-2 px-4 rounded-lg transition">
                            <svg class="w-4 h-4 inline-block mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            {{ __('Update Client') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
