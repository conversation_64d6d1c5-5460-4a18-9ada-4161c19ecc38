<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ __('Payment Receipt') }} - {{ $booking->transaction_id }}</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background: white;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .status-paid {
            background: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .info-item {
            margin-bottom: 10px;
        }
        
        .info-label {
            font-weight: bold;
            color: #666;
            font-size: 14px;
        }
        
        .info-value {
            color: #333;
            font-size: 16px;
            margin-top: 2px;
        }
        
        .amount-highlight {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }
        
        .payment-summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
        }
        
        .summary-row.total {
            border-top: 2px solid #667eea;
            padding-top: 15px;
            margin-top: 15px;
            font-weight: bold;
            font-size: 18px;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 14px;
        }
        
        .transaction-id {
            font-family: 'Courier New', monospace;
            background: #f1f1f1;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 14px;
        }
        
        @media print {
            body {
                padding: 0;
            }
            
            .container {
                border: none;
                box-shadow: none;
            }
            
            .no-print {
                display: none;
            }
        }
        
        /* RTL Support */
        [dir="rtl"] .summary-row {
            direction: rtl;
        }
        
        [dir="rtl"] .info-grid {
            direction: rtl;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>{{ __('Payment Receipt') }}</h1>
            <p>{{ __('Official receipt for your service booking') }}</p>
            <div class="status-badge status-{{ $booking->payment_status }}">
                {{ ucfirst($booking->payment_status) }}
            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Customer & Service Info -->
            <div class="section">
                <div class="info-grid">
                    <div>
                        <h3 class="section-title">{{ __('Customer Information') }}</h3>
                        <div class="info-item">
                            <div class="info-label">{{ __('Name') }}</div>
                            <div class="info-value">{{ $booking->client->user->name }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">{{ __('Email') }}</div>
                            <div class="info-value">{{ $booking->client->user->email }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">{{ __('Phone') }}</div>
                            <div class="info-value">{{ $booking->client->phone ?? __('Not provided') }}</div>
                        </div>
                    </div>

                    <div>
                        <h3 class="section-title">{{ __('Service Information') }}</h3>
                        <div class="info-item">
                            <div class="info-label">{{ __('Company') }}</div>
                            <div class="info-value">{{ $booking->company->company_name ?? __('Not specified') }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">{{ __('Worker') }}</div>
                            <div class="info-value">{{ $booking->worker->name ?? __('Not specified') }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">{{ __('Service Type') }}</div>
                            <div class="info-value">{{ ucfirst($booking->booking_type) }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Booking Details -->
            <div class="section">
                <h3 class="section-title">{{ __('Booking Details') }}</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">{{ __('Booking ID') }}</div>
                        <div class="info-value">#{{ $booking->id }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">{{ __('Service Date') }}</div>
                        <div class="info-value">
                            {{ $booking->start_date ? $booking->start_date->format('F j, Y \a\t g:i A') : __('Not specified') }}
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">{{ __('Duration') }}</div>
                        <div class="info-value">
                            @if($booking->start_date && $booking->end_date)
                                {{ $booking->start_date->diffInHours($booking->end_date) }} {{ __('hours') }}
                            @else
                                {{ __('Not specified') }}
                            @endif
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">{{ __('Status') }}</div>
                        <div class="info-value">{{ ucfirst($booking->status) }}</div>
                    </div>
                </div>
            </div>

            <!-- Payment Summary -->
            <div class="section">
                <h3 class="section-title">{{ __('Payment Summary') }}</h3>
                <div class="payment-summary">
                    <div class="summary-row">
                        <span>{{ __('Service Amount') }}</span>
                        <span>${{ number_format($booking->total_amount * 0.95, 2) }}</span>
                    </div>
                    <div class="summary-row">
                        <span>{{ __('Service Fee') }} (5%)</span>
                        <span>${{ number_format($booking->total_amount * 0.05, 2) }}</span>
                    </div>
                    <div class="summary-row total">
                        <span>{{ __('Total Amount') }}</span>
                        <span class="amount-highlight">${{ number_format($booking->total_amount, 2) }}</span>
                    </div>
                </div>
            </div>

            <!-- Payment Information -->
            <div class="section">
                <h3 class="section-title">{{ __('Payment Information') }}</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">{{ __('Transaction ID') }}</div>
                        <div class="info-value transaction-id">{{ $booking->transaction_id }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">{{ __('Payment Method') }}</div>
                        <div class="info-value">{{ ucfirst(str_replace('_', ' ', $booking->payment_method ?? 'Not specified')) }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">{{ __('Payment Date') }}</div>
                        <div class="info-value">{{ $booking->updated_at->format('F j, Y \a\t g:i A') }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">{{ __('Receipt Date') }}</div>
                        <div class="info-value">{{ now()->format('F j, Y \a\t g:i A') }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>{{ __('Thank you for using our services!') }}</p>
            <p>{{ __('This is an official receipt generated on') }} {{ now()->format('F j, Y \a\t g:i A') }}</p>
        </div>
    </div>

    <script>
        // Auto-print when page loads (for PDF generation)
        window.onload = function() {
            window.print();
        }
    </script>
</body>
</html>
