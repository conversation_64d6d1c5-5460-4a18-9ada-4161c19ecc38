<?php

namespace App\Traits;

use App\Models\Client;

trait CreatesClientProfile
{
    /**
     * إنشاء ملف عميل تلقائي للمستخدمين العاديين إذا لم يكن موجود
     */
    protected function ensureClientProfile($user)
    {
        $client = $user->client;
        
        // إنشاء ملف عميل تلقائي للمستخدمين العاديين إذا لم يكن موجود
        if (!$client && $user->hasRole('user')) {
            $client = Client::create([
                'user_id' => $user->id,
                'children_count' => 0,
                'home_size' => 'medium',
                'floors_count' => 1,
                'people_count' => 1,
                'has_yard' => false,
                'additional_info' => 'Auto-created client profile for user role',
                'rating' => 0,
                'is_active' => true,
            ]);
            
            // تحديث العلاقة في الكائن
            $user->setRelation('client', $client);
        }
        
        return $client;
    }
}
