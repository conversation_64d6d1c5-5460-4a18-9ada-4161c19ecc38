@extends('layouts.company')

@section('title', __('Booking Details'))

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <!-- <PERSON> Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ __('Booking Details') }}</h1>
                    <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">{{ __('View booking information and status') }}</p>
                </div>
                <div class="flex {{ app()->getLocale() === 'ar' ? 'space-x-reverse space-x-3' : 'space-x-3' }}">
                    <a href="{{ route('company.bookings.index') }}"
                       class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-md transition duration-200">
                        <svg class="w-5 h-5 {{ app()->getLocale() === 'ar' ? 'ms-2' : 'me-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="{{ app()->getLocale() === 'ar' ? 'M14 5l7 7m0 0l-7 7m7-7H3' : 'M10 19l-7-7m0 0l7-7m-7 7h18' }}"></path>
                        </svg>
                        {{ __('Back to Bookings') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Booking Status Card -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">{{ __('Booking') }} #{{ $booking->id }}</h2>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                        @if($booking->status === 'pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                        @elseif($booking->status === 'confirmed') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                        @elseif($booking->status === 'completed') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                        @else bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 @endif">
                        {{ __(ucfirst($booking->status)) }}
                    </span>
                </div>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Start Date') }}</h3>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $booking->start_date->format('M d, Y H:i') }}</p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('End Date') }}</h3>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $booking->end_date->format('M d, Y H:i') }}</p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Duration') }}</h3>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">
                            {{ $booking->start_date->diffInDays($booking->end_date) }} {{ __('days') }}
                        </p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Total Amount') }}</h3>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">${{ number_format($booking->total_amount, 2) }}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Client Information -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ __('Client Information') }}</h3>
                </div>
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                                <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="{{ app()->getLocale() === 'ar' ? 'mr-4' : 'ml-4' }}">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ $booking->client->user->name }}</h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $booking->client->user->email }}</p>
                            @if($booking->client->user->phone)
                                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $booking->client->user->phone }}</p>
                            @endif
                        </div>
                    </div>
                    
                    @if($booking->client->home_size || $booking->client->family_members || $booking->client->children_count)
                        <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                            <h5 class="text-sm font-medium text-gray-900 dark:text-white mb-2">{{ __('Home Info') }}</h5>
                            <div class="space-y-1">
                                @if($booking->client->home_size)
                                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ __('Size') }}: {{ __(ucfirst($booking->client->home_size)) }}</p>
                                @endif
                                @if($booking->client->family_members)
                                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ __('Family') }}: {{ $booking->client->family_members }} {{ __('people') }}</p>
                                @endif
                                @if($booking->client->children_count)
                                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ __('Children') }}: {{ $booking->client->children_count }}</p>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Worker Information -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ __('Worker Information') }}</h3>
                </div>
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            @if($booking->worker->photo)
                                <img class="w-12 h-12 rounded-full object-cover" src="{{ Storage::url($booking->worker->photo) }}" alt="{{ $booking->worker->name }}">
                            @else
                                <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                            @endif
                        </div>
                        <div class="{{ app()->getLocale() === 'ar' ? 'mr-4' : 'ml-4' }}">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ $booking->worker->name }}</h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ __(ucfirst($booking->worker->category)) }}</p>
                            @if($booking->worker->nationality)
                                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $booking->worker->nationality }}</p>
                            @endif
                        </div>
                    </div>
                    
                    <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <h5 class="text-sm font-medium text-gray-900 dark:text-white">{{ __('Experience') }}</h5>
                                <p class="text-sm text-gray-600 dark:text-gray-400">{{ $booking->worker->experience_years }} {{ __('years') }}</p>
                            </div>
                            <div>
                                <h5 class="text-sm font-medium text-gray-900 dark:text-white">{{ __('Hourly Rate') }}</h5>
                                <p class="text-sm text-gray-600 dark:text-gray-400">${{ number_format($booking->worker->hourly_rate, 2) }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Information -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mt-6">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ __('Payment Information') }}</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Payment Method') }}</h4>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ __(ucfirst($booking->payment_method)) }}</p>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Payment Status') }}</h4>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            @if($booking->payment_status === 'paid') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                            @elseif($booking->payment_status === 'pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                            @else bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 @endif">
                            {{ __(ucfirst($booking->payment_status)) }}
                        </span>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Total Amount') }}</h4>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">${{ number_format($booking->total_amount, 2) }}</p>
                    </div>
                </div>
                
                @if($booking->transaction_id)
                    <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Transaction ID') }}</h4>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $booking->transaction_id }}</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Notes -->
        @if($booking->notes)
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg mt-6">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ __('Notes') }}</h3>
                </div>
                <div class="p-6">
                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ $booking->notes }}</p>
                </div>
            </div>
        @endif

        <!-- Rejection Reason -->
        @if($booking->status === 'cancelled' && $booking->rejection_reason)
            <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg mt-6">
                <div class="px-6 py-4 border-b border-red-200 dark:border-red-800">
                    <h3 class="text-lg font-medium text-red-900 dark:text-red-200">{{ __('Rejection Reason') }}</h3>
                </div>
                <div class="p-6">
                    <p class="text-sm text-red-700 dark:text-red-300">{{ $booking->rejection_reason }}</p>
                </div>
            </div>
        @endif

        <!-- Action Buttons -->
        @if($booking->status === 'pending')
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg mt-6">
                <div class="p-6">
                    <div class="flex {{ app()->getLocale() === 'ar' ? 'space-x-reverse space-x-4' : 'space-x-4' }}">
                        <form method="POST" action="{{ route('company.bookings.approve', $booking) }}" class="inline">
                            @csrf
                            <button type="submit"
                                    onclick="return confirm('{{ __('Are you sure you want to approve this booking?') }}')"
                                    class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-md transition duration-200">
                                <svg class="w-5 h-5 {{ app()->getLocale() === 'ar' ? 'ms-2' : 'me-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                {{ __('Approve Booking') }}
                            </button>
                        </form>
                        
                        <button type="button"
                                onclick="showRejectModal({{ $booking->id }})"
                                class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-md transition duration-200">
                            <svg class="w-5 h-5 {{ app()->getLocale() === 'ar' ? 'ms-2' : 'me-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            {{ __('Reject Booking') }}
                        </button>
                    </div>
                </div>
            </div>
        @elseif($booking->status === 'confirmed')
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg mt-6">
                <div class="p-6">
                    <form method="POST" action="{{ route('company.bookings.complete', $booking) }}" class="inline">
                        @csrf
                        <button type="submit"
                                onclick="return confirm('{{ __('Mark this booking as completed?') }}')"
                                class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-200">
                            <svg class="w-5 h-5 {{ app()->getLocale() === 'ar' ? 'ms-2' : 'me-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {{ __('Mark as Completed') }}
                        </button>
                    </form>
                </div>
            </div>
        @endif

        <!-- Timestamps -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mt-6">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ __('Timeline') }}</h3>
            </div>
            <div class="p-6">
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('Created At') }}</span>
                        <span class="text-sm text-gray-900 dark:text-white">{{ $booking->created_at->format('M d, Y H:i') }}</span>
                    </div>
                    @if($booking->updated_at != $booking->created_at)
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('Last Updated') }}</span>
                            <span class="text-sm text-gray-900 dark:text-white">{{ $booking->updated_at->format('M d, Y H:i') }}</span>
                        </div>
                    @endif
                    @if($booking->completed_at)
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('Completed At') }}</span>
                            <span class="text-sm text-gray-900 dark:text-white">{{ $booking->completed_at->format('M d, Y H:i') }}</span>
                        </div>
                    @endif
                    @if($booking->rejected_at)
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('Rejected At') }}</span>
                            <span class="text-sm text-gray-900 dark:text-white">{{ $booking->rejected_at->format('M d, Y H:i') }}</span>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reject Modal -->
<div id="rejectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ __('Reject Booking') }}</h3>
            <form id="rejectForm" method="POST" class="mt-4">
                @csrf
                <div class="mb-4">
                    <label for="rejection_reason" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Rejection Reason') }}</label>
                    <textarea name="rejection_reason" id="rejection_reason" rows="3" required
                              class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
                              placeholder="{{ __('Please provide a reason for rejection...') }}"></textarea>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="hideRejectModal()"
                            class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 rounded-md">
                        {{ __('Cancel') }}
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md">
                        {{ __('Reject Booking') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showRejectModal(bookingId) {
    document.getElementById('rejectForm').action = `/company/bookings/${bookingId}/reject`;
    document.getElementById('rejectModal').classList.remove('hidden');
}

function hideRejectModal() {
    document.getElementById('rejectModal').classList.add('hidden');
    document.getElementById('rejection_reason').value = '';
}
</script>
@endsection
