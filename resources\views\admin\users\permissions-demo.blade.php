@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ __('Client Permissions Control Demo') }}</h1>
            <p class="mt-2 text-gray-600 dark:text-gray-400">{{ __('Control what clients can see and access') }}</p>
        </div>

        <!-- Permission Control Examples -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Ratings Permission -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    <svg class="w-5 h-5 inline mr-2 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    {{ __('Ratings Permission Control') }}
                </h3>
                
                <div class="space-y-4">
                    <div class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                        <h4 class="font-medium text-green-800 dark:text-green-200 mb-2">✅ {{ __('With ratings.view Permission') }}</h4>
                        <ul class="text-sm text-green-700 dark:text-green-300 space-y-1">
                            <li>• {{ __('Client sees "Ratings" link in navigation') }}</li>
                            <li>• {{ __('Can access /client/ratings page') }}</li>
                            <li>• {{ __('Can view their own ratings') }}</li>
                            <li>• {{ __('Can create new ratings') }}</li>
                            <li>• {{ __('Can edit existing ratings') }}</li>
                        </ul>
                    </div>
                    
                    <div class="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                        <h4 class="font-medium text-red-800 dark:text-red-200 mb-2">❌ {{ __('Without ratings.view Permission') }}</h4>
                        <ul class="text-sm text-red-700 dark:text-red-300 space-y-1">
                            <li>• {{ __('No "Ratings" link in navigation') }}</li>
                            <li>• {{ __('403 Forbidden when accessing /client/ratings') }}</li>
                            <li>• {{ __('Cannot view ratings page') }}</li>
                            <li>• {{ __('Cannot create or edit ratings') }}</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Notifications Permission -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    <svg class="w-5 h-5 inline mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2z"></path>
                    </svg>
                    {{ __('Notifications Permission Control') }}
                </h3>
                
                <div class="space-y-4">
                    <div class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                        <h4 class="font-medium text-green-800 dark:text-green-200 mb-2">✅ {{ __('With notifications.view Permission') }}</h4>
                        <ul class="text-sm text-green-700 dark:text-green-300 space-y-1">
                            <li>• {{ __('Client sees "Notifications" link in navigation') }}</li>
                            <li>• {{ __('Can access /client/notifications page') }}</li>
                            <li>• {{ __('Can view their notifications') }}</li>
                            <li>• {{ __('Can mark notifications as read') }}</li>
                            <li>• {{ __('Can delete notifications') }}</li>
                        </ul>
                    </div>
                    
                    <div class="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                        <h4 class="font-medium text-red-800 dark:text-red-200 mb-2">❌ {{ __('Without notifications.view Permission') }}</h4>
                        <ul class="text-sm text-red-700 dark:text-red-300 space-y-1">
                            <li>• {{ __('No "Notifications" link in navigation') }}</li>
                            <li>• {{ __('403 Forbidden when accessing /client/notifications') }}</li>
                            <li>• {{ __('Cannot view notifications page') }}</li>
                            <li>• {{ __('Cannot manage notifications') }}</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- How to Control Permissions -->
        <div class="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                <svg class="w-5 h-5 inline mr-2 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                {{ __('How to Control Client Permissions') }}
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-medium text-gray-900 dark:text-white mb-3">{{ __('Step-by-Step Guide') }}</h4>
                    <ol class="list-decimal list-inside space-y-2 text-sm text-gray-600 dark:text-gray-400">
                        <li>{{ __('Go to Admin → Users') }}</li>
                        <li>{{ __('Find the client you want to control') }}</li>
                        <li>{{ __('Click "Edit" on the user') }}</li>
                        <li>{{ __('Scroll to "Permissions" section') }}</li>
                        <li>{{ __('Check/Uncheck desired permissions') }}</li>
                        <li>{{ __('Save changes') }}</li>
                    </ol>
                </div>
                
                <div>
                    <h4 class="font-medium text-gray-900 dark:text-white mb-3">{{ __('Available Permissions') }}</h4>
                    <div class="space-y-2 text-sm">
                        <div class="flex items-center">
                            <input type="checkbox" checked disabled class="mr-2">
                            <span class="text-gray-600 dark:text-gray-400">ratings.view</span>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" checked disabled class="mr-2">
                            <span class="text-gray-600 dark:text-gray-400">ratings.create</span>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" checked disabled class="mr-2">
                            <span class="text-gray-600 dark:text-gray-400">notifications.view</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test User Example -->
        <div class="mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 p-6">
            <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-200 mb-4">
                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                {{ __('Current Test User Status') }}
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <p class="text-sm text-blue-800 dark:text-blue-300 mb-2">
                        <strong>{{ __('User') }}:</strong> <EMAIL>
                    </p>
                    <p class="text-sm text-blue-800 dark:text-blue-300">
                        <strong>{{ __('Current Permissions') }}:</strong>
                    </p>
                    <ul class="text-sm text-blue-700 dark:text-blue-400 mt-1 space-y-1">
                        <li>✅ ratings.view</li>
                        <li>✅ ratings.create</li>
                        <li>✅ notifications.view</li>
                        <li>✅ notifications.create</li>
                    </ul>
                </div>
                
                <div>
                    <p class="text-sm text-blue-800 dark:text-blue-300 mb-2">
                        <strong>{{ __('What they can access') }}:</strong>
                    </p>
                    <ul class="text-sm text-blue-700 dark:text-blue-400 space-y-1">
                        <li>✅ /client/ratings</li>
                        <li>✅ /client/notifications</li>
                        <li>✅ Navigation links visible</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="mt-8 flex justify-center space-x-4">
            <a href="{{ route('admin.users.index') }}" 
               class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                {{ __('Manage Users & Permissions') }}
            </a>
            
            <a href="{{ route('admin.test-permissions') }}" 
               class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                {{ __('Test Current User Permissions') }}
            </a>
        </div>
    </div>
</div>
@endsection
