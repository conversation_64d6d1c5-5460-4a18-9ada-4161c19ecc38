<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use App\Models\User;

class FixPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🔧 Fixing permissions for admin users...');

        // إنشاء الصلاحيات المطلوبة إذا لم تكن موجودة
        $permissions = [
            'permissions.view',
            'permissions.create',
            'permissions.edit',
            'permissions.delete'
        ];

        foreach ($permissions as $permissionName) {
            Permission::firstOrCreate([
                'name' => $permissionName,
                'guard_name' => 'web'
            ]);
            $this->command->info("✅ Permission created/found: {$permissionName}");
        }

        // إعطاء جميع الصلاحيات لدور admin
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $adminRole->givePermissionTo($permissions);
            $this->command->info("✅ Permissions assigned to admin role");
        }

        // إعطاء جميع الصلاحيات لدور super-admin
        $superAdminRole = Role::where('name', 'super-admin')->first();
        if ($superAdminRole) {
            $superAdminRole->givePermissionTo($permissions);
            $this->command->info("✅ Permissions assigned to super-admin role");
        }

        // إعطاء الصلاحيات للمستخدم الأول (test user)
        $user = User::find(1);
        if ($user) {
            // إعطاء دور admin إذا لم يكن لديه
            if (!$user->hasRole('admin')) {
                $user->assignRole('admin');
                $this->command->info("✅ Admin role assigned to user: {$user->name}");
            }

            // إعطاء الصلاحيات مباشرة
            $user->givePermissionTo($permissions);
            $this->command->info("✅ Permissions assigned directly to user: {$user->name}");
        }

        $this->command->info('🎉 Permissions fixed successfully!');
    }
}
