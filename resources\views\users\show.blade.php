@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Page Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                عرض المستخدم: {{ $user->name }}
            </h1>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="py-12">
                <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- معلومات المستخدم الأساسية -->
                        <div class="lg:col-span-2">
                            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                                <div class="p-6">
                                    <div class="flex items-center space-x-6 rtl:space-x-reverse mb-6">
                                        <img class="h-24 w-24 rounded-full border-2 border-gray-300 dark:border-gray-600" src="{{ $user->avatar_url }}" alt="{{ $user->name }}">
                                        <div>
                                            <h3 class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $user->name }}</h3>
                                            <p class="text-gray-600 dark:text-gray-400">{{ $user->email }}</p>
                                            @if($user->phone)
                                                <p class="text-gray-600 dark:text-gray-400">{{ $user->phone }}</p>
                                            @endif
                                            <div class="mt-2">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $user->status ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300' }}">
                                                    {{ $user->status ? 'نشط' : 'غير نشط' }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
        
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">معلومات الحساب</h4>
                                            <dl class="space-y-2">
                                                <div>
                                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">تاريخ الإنشاء</dt>
                                                    <dd class="text-sm text-gray-900 dark:text-gray-100">{{ $user->created_at->format('Y-m-d H:i') }}</dd>
                                                </div>
                                                <div>
                                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">آخر تحديث</dt>
                                                    <dd class="text-sm text-gray-900 dark:text-gray-100">{{ $user->updated_at->format('Y-m-d H:i') }}</dd>
                                                </div>
                                                <div>
                                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">آخر دخول</dt>
                                                    <dd class="text-sm text-gray-900 dark:text-gray-100">{{ $user->updated_at->diffForHumans() }}</dd>
                                                </div>
                                                <div>
                                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">حالة التحقق من البريد</dt>
                                                    <dd class="text-sm text-gray-900 dark:text-gray-100">
                                                        @if($user->email_verified_at)
                                                            <span class="text-green-600 dark:text-green-400">تم التحقق</span>
                                                        @else
                                                            <span class="text-red-600 dark:text-red-400">لم يتم التحقق</span>
                                                        @endif
                                                    </dd>
                                                </div>
                                            </dl>
                                        </div>
        
                                        <div>
                                            <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">الأدوار والصلاحيات</h4>
                                            <div class="space-y-3">
                                                <div>
                                                    <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الأدوار</h5>
                                                    <div class="flex flex-wrap gap-2">
                                                        @forelse($user->roles as $role)
                                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                                                                {{ $role->name }}
                                                            </span>
                                                        @empty
                                                            <span class="text-gray-500 dark:text-gray-400 text-sm">لا توجد أدوار مخصصة</span>
                                                        @endforelse
                                                    </div>
                                                </div>
        
                                                <div>
                                                    <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الصلاحيات</h5>
                                                    <div class="max-h-40 overflow-y-auto">
                                                        <div class="flex flex-wrap gap-1">
                                                            @php
                                                                $permissions = $user->getAllPermissions();
                                                            @endphp
                                                            @forelse($permissions as $permission)
                                                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-200">
                                                                    {{ $permission->name }}
                                                                </span>
                                                            @empty
                                                                <span class="text-gray-500 dark:text-gray-400 text-sm">لا توجد صلاحيات</span>
                                                            @endforelse
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
        
                        <!-- الأنشطة الحديثة -->
                        <div>
                            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                                <div class="p-6">
                                    <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">الأنشطة الحديثة</h4>
                                    <div class="space-y-3">
                                        @forelse($activities as $activity)
                                            <div class="flex items-start space-x-3 rtl:space-x-reverse">
                                                <div class="flex-shrink-0">
                                                    <div class="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full mt-2"></div>
                                                </div>
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm text-gray-900 dark:text-gray-100">{{ $activity->description }}</p>
                                                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ $activity->created_at->diffForHumans() }}</p>
                                                </div>
                                            </div>
                                        @empty
                                            <p class="text-gray-500 dark:text-gray-400 text-center py-4">لا توجد أنشطة حديثة</p>
                                        @endforelse
                                    </div>
                                </div>
                            </div>
        
                            <!-- إجراءات سريعة -->
                            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mt-6">
                                <div class="p-6">
                                    <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">إجراءات سريعة</h4>
                                    <div class="space-y-3">
                                        @can('users.edit')
                                            <form action="{{ route('users.toggle-status', $user) }}" method="POST">
                                                @csrf
                                                @method('PATCH')
                                                <button type="submit" class="w-full text-left px-4 py-2 text-sm {{ $user->status ? 'text-orange-700 dark:text-orange-300 bg-orange-50 dark:bg-orange-900/30 hover:bg-orange-100 dark:hover:bg-orange-900/50' : 'text-green-700 dark:text-green-300 bg-green-50 dark:bg-green-900/30 hover:bg-green-100 dark:hover:bg-green-900/50' }} rounded-lg transition">
                                                    {{ $user->status ? 'إلغاء تفعيل الحساب' : 'تفعيل الحساب' }}
                                                </button>
                                            </form>
                                        @endcan
        
                                        @can('users.delete')
                                            @if($user->id !== auth()->id())
                                                <form action="{{ route('users.destroy', $user) }}" method="POST"
                                                      onsubmit="return confirm('هل أنت متأكد من حذف هذا المستخدم؟')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="w-full text-left px-4 py-2 text-sm text-red-700 dark:text-red-300 bg-red-50 dark:bg-red-900/30 hover:bg-red-100 dark:hover:bg-red-900/50 rounded-lg transition">
                                                        حذف المستخدم
                                                    </button>
                                                </form>
                                            @endif
                                        @endcan
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </div>
</div>
@endsection
