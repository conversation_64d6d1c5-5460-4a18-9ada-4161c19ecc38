<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Notification;
use Carbon\Carbon;

class ImprovedNotificationsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🔔 Creating Improved Notifications with Better Data Format');
        $this->command->info('==========================================================');

        // Find the test user
        $testUser = User::where('email', '<EMAIL>')->first();

        if (!$testUser) {
            $this->command->error('❌ Test user (<EMAIL>) not found!');
            return;
        }

        // Clear existing notifications for clean test
        Notification::where('user_id', $testUser->id)->delete();
        $this->command->info('🗑️ Cleared existing notifications');

        // Create improved notifications with better formatted data
        $notifications = [
            [
                'user_id' => $testUser->id,
                'type' => 'booking',
                'title' => 'Booking Confirmed',
                'message' => 'Your cleaning service booking has been confirmed. Our worker will arrive at the scheduled time.',
                'data' => [
                    'booking_id' => 'BK-2024-001',
                    'worker_name' => 'أحمد محمد',
                    'company_name' => 'شركة بريدة للاستقدام',
                    'service_type' => 'House Cleaning',
                    'booking_date' => '2024-06-20 10:00:00',
                    'duration' => '4 hours',
                    'total_amount' => 200.00,
                    'payment_status' => 'Paid',
                    'contact_phone' => '+966501234567'
                ],
                'priority' => 'high',
                'is_read' => false,
                'action_url' => '/client/bookings/10',
                'action_text' => 'View Booking Details',
                'created_at' => Carbon::now()->subHours(1),
            ],
            [
                'user_id' => $testUser->id,
                'type' => 'payment',
                'title' => 'Payment Processed Successfully',
                'message' => 'Your payment has been processed successfully. Thank you for your business!',
                'data' => [
                    'transaction_id' => 'TXN-2024-789',
                    'amount' => 200.00,
                    'payment_method' => 'Credit Card',
                    'card_last_four' => '****1234',
                    'processing_fee' => 5.00,
                    'net_amount' => 195.00,
                    'processed_at' => '2024-06-19 14:30:00',
                    'receipt_number' => 'RCP-2024-456'
                ],
                'priority' => 'medium',
                'is_read' => false,
                'action_url' => '/client/payments/receipt/TXN-' . time() . '-001',
                'action_text' => 'Download Receipt',
                'created_at' => Carbon::now()->subHours(3),
            ],
            [
                'user_id' => $testUser->id,
                'type' => 'rating',
                'title' => 'Please Rate Your Recent Service',
                'message' => 'How was your cleaning service experience? Your feedback helps us improve our services.',
                'data' => [
                    'booking_id' => 'BK-2024-002',
                    'worker_name' => 'فاطمة أحمد',
                    'service_completed_date' => '2024-06-18 16:00:00',
                    'service_duration' => '3 hours',
                    'service_type' => 'Deep Cleaning',
                    'rating_deadline' => '2024-06-25 23:59:59'
                ],
                'priority' => 'low',
                'is_read' => true,
                'action_url' => '/client/ratings/create?booking_id=12',
                'action_text' => 'Rate Service',
                'created_at' => Carbon::now()->subDays(1),
                'read_at' => Carbon::now()->subHours(12),
            ],
            [
                'user_id' => $testUser->id,
                'type' => 'booking',
                'title' => 'Service Completed',
                'message' => 'Your cleaning service has been completed successfully. We hope you are satisfied with our service.',
                'data' => [
                    'booking_id' => 'BK-2024-003',
                    'worker_name' => 'مريم سالم',
                    'company_name' => 'شركة الرياض للخدمات المنزلية',
                    'start_time' => '2024-06-17 09:00:00',
                    'end_time' => '2024-06-17 13:00:00',
                    'actual_duration' => '4 hours',
                    'services_performed' => [
                        'General Cleaning',
                        'Kitchen Deep Clean',
                        'Bathroom Sanitization',
                        'Floor Mopping'
                    ],
                    'quality_score' => 4.8,
                    'notes' => 'Excellent service, very thorough cleaning'
                ],
                'priority' => 'medium',
                'is_read' => true,
                'action_url' => '/client/bookings/12',
                'action_text' => 'View Details',
                'created_at' => Carbon::now()->subDays(2),
                'read_at' => Carbon::now()->subDays(1),
            ],
            [
                'user_id' => $testUser->id,
                'type' => 'general',
                'title' => 'New Features Available',
                'message' => 'We have added exciting new features to improve your experience. Check them out!',
                'data' => [
                    'feature_list' => [
                        'Real-time booking tracking',
                        'In-app messaging with workers',
                        'Photo verification of completed work',
                        'Flexible rescheduling options'
                    ],
                    'release_date' => '2024-06-15 00:00:00',
                    'version' => '2.1.0',
                    'update_required' => false,
                    'learn_more_url' => '/features/whats-new'
                ],
                'priority' => 'low',
                'is_read' => false,
                'action_url' => '/features/whats-new',
                'action_text' => 'Explore Features',
                'created_at' => Carbon::now()->subDays(3),
            ],
        ];

        foreach ($notifications as $notificationData) {
            Notification::create($notificationData);
            $this->command->info("✅ Created: {$notificationData['title']}");
        }

        $this->command->info('');
        $this->command->info('🎉 Improved notifications created successfully!');
        $this->command->info('📊 Summary:');
        $this->command->info("   • Total notifications: " . Notification::where('user_id', $testUser->id)->count());
        $this->command->info("   • Unread notifications: " . Notification::where('user_id', $testUser->id)->where('is_read', false)->count());
        $this->command->info('');
        $this->command->info('🔗 Test the improved notifications:');
        $this->command->info('   • <NAME_EMAIL>');
        $this->command->info('   • Visit /client/notifications');
        $this->command->info('   • Click on any notification to see improved formatting');
    }
}
