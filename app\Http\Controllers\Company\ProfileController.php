<?php

namespace App\Http\Controllers\Company;

use App\Http\Controllers\Controller;
use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules;

class ProfileController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (!auth()->user()->hasRole('company')) {
                abort(403, 'Access denied. Company role required.');
            }
            return $next($request);
        });
    }

    /**
     * Show the form for editing company profile.
     */
    public function edit()
    {
        $user = auth()->user();
        $company = $user->company;

        if (!$company) {
            return redirect()->route('company.dashboard')->with('error', __('Company not found.'));
        }

        return view('company.profile.edit', compact('user', 'company'));
    }

    /**
     * Update company profile.
     */
    public function update(Request $request)
    {
        $user = auth()->user();
        $company = $user->company;

        if (!$company) {
            return redirect()->route('company.dashboard')->with('error', __('Company not found.'));
        }

        $validated = $request->validate([
            // User information
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'required|string|max:20',
            'national_id' => 'required|string|max:20|unique:users,national_id,' . $user->id,
            
            // Company information
            'company_name' => 'required|string|max:255',
            'commercial_registration' => 'required|string|max:50|unique:companies,commercial_registration,' . $company->id,
            'tax_number' => 'nullable|string|max:50',
            'establishment_year' => 'nullable|integer|min:1900|max:' . date('Y'),
            'address' => 'required|string',
            'description' => 'nullable|string',
            
            // Documents (optional updates)
            'commercial_registration_file' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048',
            'national_id_file' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048',
            'company_logo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            
            // Password (optional)
            'current_password' => 'nullable|required_with:password',
            'password' => 'nullable|confirmed|min:8',
        ]);

        try {
            // Update user information
            $userData = [
                'name' => $validated['name'],
                'email' => $validated['email'],
                'phone' => $validated['phone'],
                'national_id' => $validated['national_id'],
            ];

            // Update password if provided
            if ($request->filled('password')) {
                if (!Hash::check($request->current_password, $user->password)) {
                    return back()->withErrors(['current_password' => __('Current password is incorrect.')]);
                }
                $userData['password'] = Hash::make($validated['password']);
            }

            $user->update($userData);

            // Update company information
            $companyData = [
                'company_name' => $validated['company_name'],
                'commercial_registration' => $validated['commercial_registration'],
                'tax_number' => $validated['tax_number'],
                'establishment_year' => $validated['establishment_year'],
                'address' => $validated['address'],
                'description' => $validated['description'],
            ];

            // Handle file uploads
            if ($request->hasFile('commercial_registration_file')) {
                // Delete old file
                if ($company->commercial_registration_file) {
                    Storage::disk('public')->delete($company->commercial_registration_file);
                }
                $companyData['commercial_registration_file'] = $request->file('commercial_registration_file')
                    ->store('company-documents/commercial-registrations', 'public');
            }

            if ($request->hasFile('national_id_file')) {
                // Delete old file
                if ($company->national_id_file) {
                    Storage::disk('public')->delete($company->national_id_file);
                }
                $companyData['national_id_file'] = $request->file('national_id_file')
                    ->store('company-documents/national-ids', 'public');
            }

            if ($request->hasFile('company_logo')) {
                // Delete old logo
                if ($company->company_logo) {
                    Storage::disk('public')->delete($company->company_logo);
                }
                $companyData['company_logo'] = $request->file('company_logo')
                    ->store('company-logos', 'public');
            }

            $company->update($companyData);

            return redirect()->route('company.profile.edit')
                           ->with('success', __('Profile updated successfully.'));

        } catch (\Exception $e) {
            return back()->withInput()->withErrors(['error' => __('Failed to update profile. Please try again.')]);
        }
    }

    /**
     * Show company statistics and analytics.
     */
    public function analytics()
    {
        $company = auth()->user()->company;

        if (!$company) {
            return redirect()->route('company.dashboard')->with('error', __('Company not found.'));
        }

        // Get comprehensive statistics
        $stats = [
            // Workers
            'total_workers' => $company->workers()->count(),
            'active_workers' => $company->workers()->where('is_active', true)->count(),
            'inactive_workers' => $company->workers()->where('is_active', false)->count(),
            
            // Bookings
            'total_bookings' => $company->bookings()->count(),
            'pending_bookings' => $company->bookings()->where('status', 'pending')->count(),
            'confirmed_bookings' => $company->bookings()->where('status', 'confirmed')->count(),
            'completed_bookings' => $company->bookings()->where('status', 'completed')->count(),
            'cancelled_bookings' => $company->bookings()->where('status', 'cancelled')->count(),
            
            // Revenue
            'total_revenue' => $company->bookings()->where('status', 'completed')->sum('total_amount'),
            'this_month_revenue' => $company->bookings()
                ->where('status', 'completed')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('total_amount'),
            
            // Ratings
            'company_average_rating' => $company->ratings()->avg('star_rating'),
            'company_total_ratings' => $company->ratings()->count(),
            'workers_average_rating' => $company->workers()
                ->join('ratings', 'workers.id', '=', 'ratings.rated_id')
                ->where('ratings.rating_type', 'worker')
                ->avg('ratings.star_rating'),
        ];

        // Top performing workers
        $topWorkers = $company->workers()
            ->withCount(['bookings as completed_bookings' => function($query) {
                $query->where('status', 'completed');
            }])
            ->withAvg(['ratings as average_rating' => function($query) {
                $query->where('rating_type', 'worker');
            }], 'star_rating')
            ->orderBy('completed_bookings', 'desc')
            ->limit(5)
            ->get();

        // Recent activities
        $recentBookings = $company->bookings()
            ->with(['client.user', 'worker'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        $recentRatings = $company->allRatings()
            ->with(['rater', 'booking'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return view('company.profile.analytics', compact(
            'company', 
            'stats', 
            'topWorkers', 
            'recentBookings', 
            'recentRatings'
        ));
    }

    /**
     * Delete company account.
     */
    public function destroy(Request $request)
    {
        $user = auth()->user();
        $company = $user->company;

        $request->validate([
            'password' => 'required',
            'confirmation' => 'required|in:DELETE',
        ]);

        if (!Hash::check($request->password, $user->password)) {
            return back()->withErrors(['password' => __('Password is incorrect.')]);
        }

        try {
            // Check for active bookings
            $activeBookings = $company->bookings()
                ->whereIn('status', ['pending', 'confirmed'])
                ->count();

            if ($activeBookings > 0) {
                return back()->withErrors(['error' => __('Cannot delete account with active bookings.')]);
            }

            // Delete company files
            if ($company->commercial_registration_file) {
                Storage::disk('public')->delete($company->commercial_registration_file);
            }
            if ($company->national_id_file) {
                Storage::disk('public')->delete($company->national_id_file);
            }
            if ($company->company_logo) {
                Storage::disk('public')->delete($company->company_logo);
            }

            // Delete worker files
            foreach ($company->workers as $worker) {
                if ($worker->photo) {
                    Storage::disk('public')->delete($worker->photo);
                }
                if ($worker->passport_photo) {
                    Storage::disk('public')->delete($worker->passport_photo);
                }
                if ($worker->cv_file) {
                    Storage::disk('public')->delete($worker->cv_file);
                }
            }

            // Delete company and related data (cascade will handle most)
            $company->delete();
            
            // Delete user account
            $user->delete();

            // Logout and redirect
            auth()->logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            return redirect()->route('home')->with('success', __('Account deleted successfully.'));

        } catch (\Exception $e) {
            return back()->withErrors(['error' => __('Failed to delete account. Please try again.')]);
        }
    }
}
