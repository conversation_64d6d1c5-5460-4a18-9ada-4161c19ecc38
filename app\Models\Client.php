<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Client extends Model
{
    protected $fillable = [
        'user_id',
        'children_count',
        'home_size',
        'floors_count',
        'people_count',
        'has_yard',
        'additional_info',
        'rating',
        'is_active',
    ];

    protected $casts = [
        'children_count' => 'integer',
        'floors_count' => 'integer',
        'people_count' => 'integer',
        'has_yard' => 'boolean',
        'rating' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }

    public function getHomeSizeTextAttribute(): string
    {
        return match($this->home_size) {
            'small' => __('Small'),
            'medium' => __('Medium'),
            'large' => __('Large'),
            'villa' => __('Villa'),
            default => __('Not specified'),
        };
    }
}
