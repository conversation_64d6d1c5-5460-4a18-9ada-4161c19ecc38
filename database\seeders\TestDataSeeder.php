<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Client;
use App\Models\Company;
use App\Models\Worker;
use App\Models\Booking;
use App\Models\Rating;
use Carbon\Carbon;

class TestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🎯 Creating Test Data for Ratings System');
        $this->command->info('==========================================');

        // Find the test user
        $testUser = User::where('email', '<EMAIL>')->first();

        if (!$testUser) {
            $this->command->error('❌ Test user (<EMAIL>) not found!');
            return;
        }

        // Create client profile if doesn't exist
        $client = Client::where('user_id', $testUser->id)->first();
        if (!$client) {
            $client = Client::create([
                'user_id' => $testUser->id,
                'children_count' => 2,
                'home_size' => 'medium',
                'floors_count' => 2,
                'people_count' => 4,
                'has_yard' => true,
                'additional_info' => 'Test client profile for ratings system',
                'rating' => 4.5,
                'is_active' => true,
            ]);
            $this->command->info("✅ Created client profile for {$testUser->name}");
        } else {
            $this->command->info("ℹ️ Client profile already exists for {$testUser->name}");
        }

        // Create a test company if doesn't exist
        $company = Company::first();
        if (!$company) {
            $companyUser = User::create([
                'name' => 'Test Company Owner',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'status' => true,
            ]);

            $company = Company::create([
                'user_id' => $companyUser->id,
                'company_name' => 'Test Cleaning Company',
                'description' => 'A test company for the ratings system',
                'commercial_register' => 'CR123456789',
                'contact_info' => json_encode(['phone' => '+966501234567']),
                'rating' => 4.2,
                'workers_count' => 5,
                'join_date' => Carbon::now()->subMonths(6),
                'is_active' => true,
            ]);
            $this->command->info("✅ Created test company: {$company->company_name}");
        } else {
            $this->command->info("ℹ️ Using existing company: {$company->company_name}");
        }

        // Create a test worker if doesn't exist
        $worker = Worker::where('company_id', $company->id)->first();
        if (!$worker) {
            $worker = Worker::create([
                'company_id' => $company->id,
                'name' => 'Test Worker',
                'age' => 28,
                'nationality' => 'Filipino',
                'category' => 'housemaid',
                'skills' => ['cleaning', 'cooking'],
                'languages' => ['English', 'Arabic'],
                'experience' => ['domestic_work' => '3 years'],
                'hourly_rate' => 25.00,
                'daily_rate' => 200.00,
                'monthly_rate' => 1500.00,
                'yearly_rate' => 18000.00,
                'rating' => 4.3,
                'status' => 'available',
                'added_date' => Carbon::now()->subMonths(3),
                'is_active' => true,
                'description' => 'Experienced housemaid with excellent cleaning skills',
            ]);
            $this->command->info("✅ Created test worker: {$worker->name}");
        } else {
            $this->command->info("ℹ️ Using existing worker: {$worker->name}");
        }

        // Create test bookings if don't exist
        $existingBookings = Booking::where('client_id', $client->id)->count();
        if ($existingBookings < 3) {
            for ($i = 1; $i <= 3; $i++) {
                $booking = Booking::create([
                    'client_id' => $client->id,
                    'worker_id' => $worker->id,
                    'company_id' => $company->id,
                    'booking_type' => 'daily',
                    'start_date' => Carbon::now()->subDays(30 - ($i * 7)),
                    'end_date' => Carbon::now()->subDays(30 - ($i * 7))->addHours(8),
                    'total_amount' => 200.00,
                    'payment_status' => 'paid',
                    'payment_method' => 'credit_card',
                    'transaction_id' => 'TXN' . str_pad($i, 6, '0', STR_PAD_LEFT),
                    'status' => 'completed',
                    'notes' => "Test booking #{$i} for ratings system",
                ]);

                // Create rating for this booking
                $rating = Rating::create([
                    'rating_type' => 'worker',
                    'rater_id' => $testUser->id,
                    'rated_id' => $worker->id,
                    'booking_id' => $booking->id,
                    'star_rating' => 4 + ($i % 2), // Alternates between 4 and 5
                    'comment' => "Great service! Booking #{$i} was completed professionally.",
                    'rating_date' => Carbon::now()->subDays(25 - ($i * 7)),
                ]);

                $this->command->info("✅ Created booking #{$i} with rating");
            }
        } else {
            $this->command->info("ℹ️ Test bookings already exist");
        }

        $this->command->info('');
        $this->command->info('🎉 Test data creation completed!');
        $this->command->info('📊 Summary:');
        $this->command->info("   • User: {$testUser->name} ({$testUser->email})");
        $this->command->info("   • Client ID: {$client->id}");
        $this->command->info("   • Company: {$company->company_name}");
        $this->command->info("   • Worker: {$worker->name}");
        $this->command->info("   • Bookings: " . Booking::where('client_id', $client->id)->count());
        $this->command->info("   • Ratings: " . Rating::where('rater_id', $testUser->id)->count());
        $this->command->info('');
        $this->command->info('🔗 Now you can test:');
        $this->command->info('   • <NAME_EMAIL>');
        $this->command->info('   • Visit /client/ratings');
        $this->command->info('   • Visit /client/notifications');
    }
}
