@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Page Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                    {{ __('Create New Rating') }}
                </h1>
                <a href="{{ route('admin.ratings.index') }}"
                   class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-md transition duration-200">
                    <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    {{ __('Back to Ratings') }}
                </a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Help Section -->
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 rounded-lg p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                📝 {{ __('How to Create a Rating') }}
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div class="bg-white dark:bg-gray-800 p-4 rounded-lg">
                    <h3 class="font-medium text-blue-600 dark:text-blue-400 mb-2">1️⃣ {{ __('Choose Rating Type') }}</h3>
                    <p class="text-gray-600 dark:text-gray-400">{{ __('Select who you want to rate: Worker, Company, or Client') }}</p>
                </div>
                <div class="bg-white dark:bg-gray-800 p-4 rounded-lg">
                    <h3 class="font-medium text-green-600 dark:text-green-400 mb-2">2️⃣ {{ __('Select Participants') }}</h3>
                    <p class="text-gray-600 dark:text-gray-400">{{ __('Choose who is giving the rating and who is receiving it') }}</p>
                </div>
                <div class="bg-white dark:bg-gray-800 p-4 rounded-lg">
                    <h3 class="font-medium text-purple-600 dark:text-purple-400 mb-2">3️⃣ {{ __('Link to Booking') }}</h3>
                    <p class="text-gray-600 dark:text-gray-400">{{ __('Select the completed booking this rating is based on') }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6">
                <form method="POST" action="{{ route('admin.ratings.store') }}" enctype="multipart/form-data">
                    @csrf

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Rating Type -->
                        <div class="md:col-span-2">
                            <label for="rating_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Rating Type') }} <span class="text-red-500">*</span>
                            </label>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">{{ __('Who are you rating?') }}</p>
                            <select name="rating_type"
                                    id="rating_type"
                                    required
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('rating_type') border-red-500 @enderror">
                                <option value="">{{ __('Select who you want to rate') }}</option>
                                <option value="worker" {{ old('rating_type', request('rating_type')) == 'worker' ? 'selected' : '' }}>
                                    🏠 {{ __('Worker') }} - {{ __('Rate a domestic worker') }}
                                </option>
                                <option value="company" {{ old('rating_type', request('rating_type')) == 'company' ? 'selected' : '' }}>
                                    🏢 {{ __('Company') }} - {{ __('Rate a service company') }}
                                </option>
                                <option value="client" {{ old('rating_type', request('rating_type')) == 'client' ? 'selected' : '' }}>
                                    👤 {{ __('Client') }} - {{ __('Rate a client') }}
                                </option>
                            </select>
                            @error('rating_type')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Rater (Who is giving the rating) -->
                        <div>
                            <label for="rater_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Who is giving this rating?') }} <span class="text-red-500">*</span>
                            </label>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">{{ __('Select the person who is providing the rating') }}</p>
                            <select name="rater_id"
                                    id="rater_id"
                                    required
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('rater_id') border-red-500 @enderror">
                                <option value="">{{ __('Select the person giving the rating') }}</option>
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}" {{ old('rater_id', request('rater_id')) == $user->id ? 'selected' : '' }}>
                                        👤 {{ $user->name }} ({{ $user->email }})
                                    </option>
                                @endforeach
                            </select>
                            @error('rater_id')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Rated ID (Who is being rated) -->
                        <div>
                            <label for="rated_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Who is being rated?') }} <span class="text-red-500">*</span>
                            </label>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">{{ __('Select the person or entity that will receive this rating') }}</p>

                            <!-- Worker Selection (Hidden by default) -->
                            <div id="worker-selection" class="hidden">
                                <select name="worker_rated_id"
                                        id="worker_rated_id"
                                        class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <option value="">{{ __('Select Worker to Rate') }}</option>
                                    @php
                                        $workers = \App\Models\Worker::with('company')->where('is_active', true)->get();
                                    @endphp
                                    @foreach($workers as $worker)
                                        <option value="{{ $worker->id }}">
                                            🏠 {{ $worker->name }} ({{ $worker->nationality }}) - {{ $worker->company->company_name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Company Selection (Hidden by default) -->
                            <div id="company-selection" class="hidden">
                                <select name="company_rated_id"
                                        id="company_rated_id"
                                        class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <option value="">{{ __('Select Company to Rate') }}</option>
                                    @php
                                        $companies = \App\Models\Company::where('is_active', true)->get();
                                    @endphp
                                    @foreach($companies as $company)
                                        <option value="{{ $company->id }}">
                                            🏢 {{ $company->company_name }} - {{ $company->user->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Client Selection (Hidden by default) -->
                            <div id="client-selection" class="hidden">
                                <select name="client_rated_id"
                                        id="client_rated_id"
                                        class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <option value="">{{ __('Select Client to Rate') }}</option>
                                    @php
                                        $clients = \App\Models\Client::with('user')->where('is_active', true)->get();
                                    @endphp
                                    @foreach($clients as $client)
                                        <option value="{{ $client->id }}">
                                            👤 {{ $client->user->name }} ({{ $client->user->email }})
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Hidden input for the actual rated_id -->
                            <input type="hidden" name="rated_id" id="rated_id" value="{{ old('rated_id', request('rated_id')) }}">

                            @error('rated_id')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Booking -->
                        <div class="md:col-span-2">
                            <label for="booking_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Related Booking') }} <span class="text-red-500">*</span>
                            </label>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">{{ __('Select the completed booking that this rating is based on') }}</p>
                            <select name="booking_id"
                                    id="booking_id"
                                    required
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('booking_id') border-red-500 @enderror">
                                <option value="">{{ __('Select a completed booking') }}</option>
                                @foreach($bookings as $booking)
                                    <option value="{{ $booking->id }}" {{ old('booking_id', request('booking_id')) == $booking->id ? 'selected' : '' }}>
                                        📅 {{ __('Booking') }} #{{ $booking->id }} -
                                        👤 {{ $booking->client->user->name }} →
                                        🏠 {{ $booking->worker->name }}
                                        ({{ $booking->company->company_name }}) -
                                        💰 ${{ number_format($booking->total_amount, 2) }}
                                    </option>
                                @endforeach
                            </select>
                            @if($bookings->isEmpty())
                                <p class="mt-2 text-sm text-yellow-600 dark:text-yellow-400">
                                    ⚠️ {{ __('No completed bookings found. You need completed bookings to create ratings.') }}
                                </p>
                            @endif
                            @error('booking_id')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Star Rating -->
                        <div>
                            <label for="star_rating" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Star Rating') }} <span class="text-red-500">*</span>
                            </label>
                            <select name="star_rating"
                                    id="star_rating"
                                    required
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('star_rating') border-red-500 @enderror">
                                <option value="">{{ __('Select Rating') }}</option>
                                @for($i = 1; $i <= 5; $i++)
                                    <option value="{{ $i }}" {{ old('star_rating') == $i ? 'selected' : '' }}>
                                        {{ $i }} {{ $i == 1 ? __('Star') : __('Stars') }} -
                                        @for($j = 1; $j <= $i; $j++)★@endfor
                                        @for($k = $i + 1; $k <= 5; $k++)☆@endfor
                                    </option>
                                @endfor
                            </select>
                            @error('star_rating')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Rating Date -->
                        <div>
                            <label for="rating_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Rating Date') }} <span class="text-red-500">*</span>
                            </label>
                            <input type="datetime-local"
                                   name="rating_date"
                                   id="rating_date"
                                   value="{{ old('rating_date', now()->format('Y-m-d\TH:i')) }}"
                                   required
                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('rating_date') border-red-500 @enderror">
                            @error('rating_date')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Criteria Ratings -->
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Detailed Criteria Ratings') }}
                            </label>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                @php
                                    $criteria = [
                                        'quality' => __('Quality of Service'),
                                        'punctuality' => __('Punctuality'),
                                        'communication' => __('Communication'),
                                        'professionalism' => __('Professionalism'),
                                        'cleanliness' => __('Cleanliness'),
                                        'value_for_money' => __('Value for Money')
                                    ];
                                @endphp
                                @foreach($criteria as $key => $label)
                                    <div>
                                        <label for="criteria_{{ $key }}" class="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                                            {{ $label }}
                                        </label>
                                        <select name="criteria_ratings[{{ $key }}]"
                                                id="criteria_{{ $key }}"
                                                class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-600 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                            <option value="">{{ __('Not Rated') }}</option>
                                            @for($i = 1; $i <= 5; $i++)
                                                <option value="{{ $i }}" {{ old("criteria_ratings.{$key}") == $i ? 'selected' : '' }}>
                                                    {{ $i }} {{ $i == 1 ? __('Star') : __('Stars') }}
                                                </option>
                                            @endfor
                                        </select>
                                    </div>
                                @endforeach
                            </div>
                            @error('criteria_ratings')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Comment -->
                        <div class="md:col-span-2">
                            <label for="comment" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Comment') }}
                            </label>
                            <textarea name="comment"
                                      id="comment"
                                      rows="4"
                                      placeholder="{{ __('Share your experience...') }}"
                                      class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('comment') border-red-500 @enderror">{{ old('comment') }}</textarea>
                            @error('comment')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Images -->
                        <div class="md:col-span-2">
                            <label for="images" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Rating Images') }}
                            </label>
                            <input type="file"
                                   name="images[]"
                                   id="images"
                                   multiple
                                   accept="image/*"
                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('images') border-red-500 @enderror">
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ __('You can upload multiple images. Max size: 2MB per image. Supported formats: JPEG, PNG, JPG, GIF') }}</p>
                            @error('images')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="flex items-center justify-end mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                        <a href="{{ route('admin.ratings.index') }}"
                           class="bg-gray-500 hover:bg-gray-700 dark:bg-gray-600 dark:hover:bg-gray-800 text-white font-bold py-2 px-4 rounded-lg mr-3 transition">
                            {{ __('Cancel') }}
                        </a>
                        <button type="submit"
                                class="bg-blue-500 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white font-bold py-2 px-4 rounded-lg transition">
                            <svg class="w-4 h-4 inline-block mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                            </svg>
                            {{ __('Create Rating') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const imagesInput = document.getElementById('images');
    const form = document.querySelector('form');
    const ratingTypeSelect = document.getElementById('rating_type');
    const workerSelection = document.getElementById('worker-selection');
    const companySelection = document.getElementById('company-selection');
    const clientSelection = document.getElementById('client-selection');
    const ratedIdInput = document.getElementById('rated_id');

    // Handle rating type change
    ratingTypeSelect.addEventListener('change', function() {
        // Hide all selections
        workerSelection.classList.add('hidden');
        companySelection.classList.add('hidden');
        clientSelection.classList.add('hidden');

        // Clear rated_id
        ratedIdInput.value = '';

        // Show relevant selection based on rating type
        switch(this.value) {
            case 'worker':
                workerSelection.classList.remove('hidden');
                break;
            case 'company':
                companySelection.classList.remove('hidden');
                break;
            case 'client':
                clientSelection.classList.remove('hidden');
                break;
        }
    });

    // Handle worker selection
    document.getElementById('worker_rated_id').addEventListener('change', function() {
        ratedIdInput.value = this.value;
    });

    // Handle company selection
    document.getElementById('company_rated_id').addEventListener('change', function() {
        ratedIdInput.value = this.value;
    });

    // Handle client selection
    document.getElementById('client_rated_id').addEventListener('change', function() {
        ratedIdInput.value = this.value;
    });

    // Auto-fill fields from URL parameters on page load
    const urlParams = new URLSearchParams(window.location.search);
    const ratingType = urlParams.get('rating_type');
    const ratedId = urlParams.get('rated_id');

    if (ratingType && ratedId) {
        // Trigger the rating type change to show the correct selection
        ratingTypeSelect.value = ratingType;
        ratingTypeSelect.dispatchEvent(new Event('change'));

        // Set the rated_id
        ratedIdInput.value = ratedId;

        // Select the correct option in the appropriate dropdown
        setTimeout(() => {
            switch(ratingType) {
                case 'worker':
                    document.getElementById('worker_rated_id').value = ratedId;
                    break;
                case 'company':
                    document.getElementById('company_rated_id').value = ratedId;
                    break;
                case 'client':
                    document.getElementById('client_rated_id').value = ratedId;
                    break;
            }
        }, 100);
    }

    // Validate images before form submission
    form.addEventListener('submit', function(e) {
        const files = imagesInput.files;

        if (files.length > 0) {
            for (let i = 0; i < files.length; i++) {
                const file = files[i];

                // Check file size (2MB = 2 * 1024 * 1024 bytes)
                if (file.size > 2 * 1024 * 1024) {
                    e.preventDefault();
                    alert('{{ __("Each image must be less than 2MB") }}');
                    return false;
                }

                // Check file type
                const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif'];
                if (!allowedTypes.includes(file.type)) {
                    e.preventDefault();
                    alert('{{ __("Please select valid image files (JPEG, PNG, JPG, GIF)") }}');
                    return false;
                }
            }
        }
    });

    // Clear file input if invalid files are selected
    imagesInput.addEventListener('change', function() {
        const files = this.files;

        if (files.length > 0) {
            for (let i = 0; i < files.length; i++) {
                const file = files[i];

                if (file.size > 2 * 1024 * 1024) {
                    this.value = '';
                    alert('{{ __("Each image must be less than 2MB") }}');
                    return;
                }

                const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif'];
                if (!allowedTypes.includes(file.type)) {
                    this.value = '';
                    alert('{{ __("Please select valid image files (JPEG, PNG, JPG, GIF)") }}');
                    return;
                }
            }
        }
    });
});
</script>

@endsection
