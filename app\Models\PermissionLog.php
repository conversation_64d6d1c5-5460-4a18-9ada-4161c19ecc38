<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Permission\Models\Role;

class PermissionLog extends Model
{
    protected $fillable = [
        'user_id',
        'role_id',
        'action',
        'permission_name',
        'old_permissions',
        'new_permissions',
        'ip_address',
        'user_agent'
    ];

    protected $casts = [
        'old_permissions' => 'array',
        'new_permissions' => 'array',
    ];

    /**
     * Get the user that performed the action.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the role that was modified.
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }

    /**
     * Scope to get recent logs.
     */
    public function scopeRecent($query, $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Scope to get logs by action.
     */
    public function scopeByAction($query, $action)
    {
        return $query->where('action', $action);
    }
}
