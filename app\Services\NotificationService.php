<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    /**
     * Create a new notification
     */
    public function create(array $data): Notification
    {
        return Notification::create([
            'type' => $data['type'],
            'title' => $data['title'],
            'message' => $data['message'],
            'data' => $data['data'] ?? null,
            'user_id' => $data['user_id'],
            'notifiable_type' => $data['notifiable_type'] ?? null,
            'notifiable_id' => $data['notifiable_id'] ?? null,
            'priority' => $data['priority'] ?? 'medium',
            'channel' => $data['channel'] ?? 'database',
            'action_url' => $data['action_url'] ?? null,
            'action_text' => $data['action_text'] ?? null,
            'icon' => $data['icon'] ?? null,
            'color' => $data['color'] ?? null,
            'expires_at' => $data['expires_at'] ?? null,
        ]);
    }

    /**
     * Send notification to user
     */
    public function send(User $user, array $data): Notification
    {
        $data['user_id'] = $user->id;
        $notification = $this->create($data);

        // Send via different channels based on configuration
        if (in_array('email', (array)($data['channels'] ?? []))) {
            $this->sendEmail($notification);
        }

        if (in_array('sms', (array)($data['channels'] ?? []))) {
            $this->sendSMS($notification);
        }

        return $notification;
    }

    /**
     * Send notification to multiple users
     */
    public function sendToMany(array $users, array $data): array
    {
        $notifications = [];

        foreach ($users as $user) {
            $notifications[] = $this->send($user, $data);
        }

        return $notifications;
    }

    /**
     * Create booking-related notifications
     */
    public function notifyBookingCreated($booking): void
    {
        // Notify client
        $this->send($booking->client->user, [
            'type' => 'booking_created',
            'title' => __('Booking Created Successfully'),
            'message' => __('Your booking #:id has been created and is pending confirmation.', ['id' => $booking->id]),
            'notifiable_type' => get_class($booking),
            'notifiable_id' => $booking->id,
            'priority' => 'medium',
            'action_url' => route('admin.bookings.show', $booking),
            'action_text' => __('View Booking'),
            'icon' => 'calendar',
            'color' => 'blue',
            'data' => [
                'booking_id' => $booking->id,
                'total_amount' => $booking->total_amount,
                'worker_name' => $booking->worker->name,
            ],
        ]);

        // Notify company
        $this->send($booking->company->user, [
            'type' => 'booking_created',
            'title' => __('New Booking Received'),
            'message' => __('A new booking #:id has been created for worker :worker.', [
                'id' => $booking->id,
                'worker' => $booking->worker->name
            ]),
            'notifiable_type' => get_class($booking),
            'notifiable_id' => $booking->id,
            'priority' => 'high',
            'action_url' => route('admin.bookings.show', $booking),
            'action_text' => __('View Booking'),
            'icon' => 'briefcase',
            'color' => 'green',
            'data' => [
                'booking_id' => $booking->id,
                'client_name' => $booking->client->user->name,
                'total_amount' => $booking->total_amount,
            ],
        ]);
    }

    /**
     * Create payment-related notifications
     */
    public function notifyPaymentReceived($booking): void
    {
        // Notify client
        $this->send($booking->client->user, [
            'type' => 'payment_received',
            'title' => __('Payment Confirmed'),
            'message' => __('Your payment for booking #:id has been received successfully.', ['id' => $booking->id]),
            'notifiable_type' => get_class($booking),
            'notifiable_id' => $booking->id,
            'priority' => 'medium',
            'action_url' => route('admin.bookings.show', $booking),
            'action_text' => __('View Booking'),
            'icon' => 'credit-card',
            'color' => 'green',
            'data' => [
                'booking_id' => $booking->id,
                'amount' => $booking->total_amount,
            ],
        ]);

        // Notify company
        $this->send($booking->company->user, [
            'type' => 'payment_received',
            'title' => __('Payment Received'),
            'message' => __('Payment received for booking #:id. Amount: $:amount', [
                'id' => $booking->id,
                'amount' => number_format($booking->total_amount, 2)
            ]),
            'notifiable_type' => get_class($booking),
            'notifiable_id' => $booking->id,
            'priority' => 'medium',
            'action_url' => route('admin.bookings.show', $booking),
            'action_text' => __('View Booking'),
            'icon' => 'banknotes',
            'color' => 'green',
        ]);
    }

    /**
     * Create rating-related notifications
     */
    public function notifyRatingReceived($rating): void
    {
        if ($rating->rating_type === 'worker') {
            // Notify company about worker rating
            $worker = \App\Models\Worker::find($rating->rated_id);
            if ($worker) {
                $this->send($worker->company->user, [
                    'type' => 'rating_received',
                    'title' => __('New Rating Received'),
                    'message' => __('Worker :worker received a :stars star rating.', [
                        'worker' => $worker->name,
                        'stars' => $rating->star_rating
                    ]),
                    'notifiable_type' => get_class($rating),
                    'notifiable_id' => $rating->id,
                    'priority' => $rating->star_rating >= 4 ? 'medium' : 'high',
                    'action_url' => route('admin.ratings.show', $rating),
                    'action_text' => __('View Rating'),
                    'icon' => 'star',
                    'color' => $rating->star_rating >= 4 ? 'yellow' : 'red',
                    'data' => [
                        'rating_id' => $rating->id,
                        'star_rating' => $rating->star_rating,
                        'worker_name' => $worker->name,
                    ],
                ]);
            }
        }
    }

    /**
     * Create system notifications
     */
    public function notifySystemMaintenance(array $userIds, $startTime, $endTime): void
    {
        $users = User::whereIn('id', $userIds)->get();

        $this->sendToMany($users, [
            'type' => 'system_maintenance',
            'title' => __('Scheduled Maintenance'),
            'message' => __('System maintenance is scheduled from :start to :end. Some features may be unavailable.', [
                'start' => $startTime,
                'end' => $endTime
            ]),
            'priority' => 'high',
            'icon' => 'wrench-screwdriver',
            'color' => 'yellow',
            'expires_at' => $endTime,
        ]);
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(Notification $notification): void
    {
        $notification->markAsRead();
    }

    /**
     * Mark notification as unread
     */
    public function markAsUnread(Notification $notification): void
    {
        $notification->markAsUnread();
    }

    /**
     * Mark all notifications as read for user
     */
    public function markAllAsRead(User $user): void
    {
        $user->notifications()->unread()->update([
            'is_read' => true,
            'read_at' => now(),
        ]);
    }

    /**
     * Get unread notifications count for user
     */
    public function getUnreadCount(User $user): int
    {
        return $user->notifications()->unread()->notExpired()->count();
    }

    /**
     * Get recent notifications for user
     */
    public function getRecent(User $user, int $limit = 10)
    {
        return $user->notifications()
                   ->notExpired()
                   ->orderBy('created_at', 'desc')
                   ->limit($limit)
                   ->get();
    }

    /**
     * Clean up expired notifications
     */
    public function cleanupExpired(): int
    {
        return Notification::where('expires_at', '<', now())->delete();
    }

    /**
     * Send email notification
     */
    private function sendEmail(Notification $notification): void
    {
        try {
            // Implementation for email sending
            // You can use Laravel's Mail facade here
            Log::info('Email notification sent', ['notification_id' => $notification->id]);
        } catch (\Exception $e) {
            Log::error('Failed to send email notification', [
                'notification_id' => $notification->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send SMS notification
     */
    private function sendSMS(Notification $notification): void
    {
        try {
            // Implementation for SMS sending
            // You can integrate with SMS providers here
            Log::info('SMS notification sent', ['notification_id' => $notification->id]);
        } catch (\Exception $e) {
            Log::error('Failed to send SMS notification', [
                'notification_id' => $notification->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
