@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">

    <!-- <PERSON> Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ __('Schedule Details') }}</h1>
                    <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">{{ __('View schedule information') }}</p>
                </div>
                <div class="flex space-x-3 rtl:space-x-reverse">
                    <a href="{{ route('admin.schedules.index') }}" 
                       class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-md transition duration-200">
                        <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        {{ __('Back to Schedules') }}
                    </a>
                    @can('schedules.edit')
                    <a href="{{ route('admin.schedules.edit', $schedule) }}" 
                       class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-200">
                        <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        {{ __('Edit Schedule') }}
                    </a>
                    @endcan
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Schedule Overview -->
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                    <div class="px-6 py-6">
                        <!-- Schedule Type Badge -->
                        <div class="mb-6">
                            @if($schedule->schedule_type === 'available')
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    <svg class="w-4 h-4 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    {{ $schedule->schedule_type_text }}
                                </span>
                            @elseif($schedule->schedule_type === 'booked')
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                    <svg class="w-4 h-4 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6-4h6"></path>
                                    </svg>
                                    {{ $schedule->schedule_type_text }}
                                </span>
                            @else
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                    <svg class="w-4 h-4 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    {{ $schedule->schedule_type_text }}
                                </span>
                            @endif
                        </div>

                        <!-- Worker Information -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Worker Information') }}</h3>
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-16 w-16">
                                    @if($schedule->worker->image)
                                        <img class="h-16 w-16 rounded-full object-cover" 
                                             src="{{ Storage::url($schedule->worker->image) }}" 
                                             alt="{{ $schedule->worker->name }}">
                                    @else
                                        <div class="h-16 w-16 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
                                            <svg class="h-8 w-8 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                            </svg>
                                        </div>
                                    @endif
                                </div>
                                <div class="ms-6">
                                    <h4 class="text-xl font-bold text-gray-900 dark:text-white">{{ $schedule->worker->name }}</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ $schedule->worker->category_text }}</p>
                                    <p class="text-sm text-gray-500 dark:text-gray-500">{{ $schedule->worker->nationality }} • {{ $schedule->worker->age }} {{ __('years') }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Schedule Details -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Schedule Details') }}</h3>
                            <dl class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Start Date & Time') }}</dt>
                                    <dd class="text-lg font-semibold text-gray-900 dark:text-white">{{ $schedule->start_date->format('F d, Y') }}</dd>
                                    <dd class="text-sm text-gray-600 dark:text-gray-400">{{ $schedule->start_date->format('H:i') }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('End Date & Time') }}</dt>
                                    <dd class="text-lg font-semibold text-gray-900 dark:text-white">{{ $schedule->end_date->format('F d, Y') }}</dd>
                                    <dd class="text-sm text-gray-600 dark:text-gray-400">{{ $schedule->end_date->format('H:i') }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Duration') }}</dt>
                                    <dd class="text-lg font-semibold text-green-600 dark:text-green-400">
                                        {{ $schedule->start_date->diffForHumans($schedule->end_date, true) }}
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Schedule Type') }}</dt>
                                    <dd class="text-lg font-semibold text-gray-900 dark:text-white">{{ $schedule->schedule_type_text }}</dd>
                                </div>
                            </dl>
                        </div>

                        <!-- Related Booking -->
                        @if($schedule->booking)
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Related Booking') }}</h3>
                            <div class="bg-blue-50 dark:bg-blue-900 rounded-lg p-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-blue-800 dark:text-blue-200">{{ __('Booking') }} #{{ $schedule->booking->id }}</h4>
                                        <p class="text-sm text-blue-600 dark:text-blue-300">{{ $schedule->booking->client->user->name }}</p>
                                        <p class="text-xs text-blue-500 dark:text-blue-400">${{ number_format($schedule->booking->total_amount, 2) }}</p>
                                    </div>
                                    @can('bookings.view')
                                    <a href="{{ route('admin.bookings.show', $schedule->booking) }}" 
                                       class="inline-flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition duration-200">
                                        {{ __('View Booking') }}
                                    </a>
                                    @endcan
                                </div>
                            </div>
                        </div>
                        @endif

                        <!-- Notes -->
                        @if($schedule->notes)
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">{{ __('Notes') }}</h3>
                            <p class="text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">{{ $schedule->notes }}</p>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Schedule Timeline -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                    <div class="px-6 py-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Schedule Timeline') }}</h3>
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ms-4">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ __('Starts') }}</p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ $schedule->start_date->format('M d, Y H:i') }}</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ms-4">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ __('Ends') }}</p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ $schedule->end_date->format('M d, Y H:i') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                    <div class="px-6 py-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Quick Actions') }}</h3>
                        <div class="space-y-3">
                            @can('schedules.edit')
                            <a href="{{ route('admin.schedules.edit', $schedule) }}" 
                               class="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-200">
                                <svg class="w-4 h-4 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                {{ __('Edit Schedule') }}
                            </a>
                            @endcan

                            @can('workers.view')
                            <a href="{{ route('admin.workers.show', $schedule->worker) }}" 
                               class="w-full inline-flex items-center justify-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-md transition duration-200">
                                <svg class="w-4 h-4 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                {{ __('View Worker') }}
                            </a>
                            @endcan

                            @can('schedules.delete')
                            <form method="POST" action="{{ route('admin.schedules.destroy', $schedule) }}" class="w-full">
                                @csrf
                                @method('DELETE')
                                <button type="submit" 
                                        class="w-full inline-flex items-center justify-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-md transition duration-200"
                                        onclick="return confirm('{{ __('Are you sure you want to delete this schedule?') }}')">
                                    <svg class="w-4 h-4 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                    {{ __('Delete Schedule') }}
                                </button>
                            </form>
                            @endcan
                        </div>
                    </div>
                </div>

                <!-- Schedule Information -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                    <div class="px-6 py-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Schedule Information') }}</h3>
                        <dl class="space-y-3">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Created At') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $schedule->created_at->format('F d, Y H:i') }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Last Updated') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $schedule->updated_at->format('F d, Y H:i') }}</dd>
                            </div>
                            @if($schedule->booking)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Booking Status') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $schedule->booking->status_text }}</dd>
                            </div>
                            @endif
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
