@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Page Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                إضافة دور جديد
            </h1>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="py-12">
                <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <form method="POST" action="{{ route('roles.store') }}">
                                @csrf
        
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <!-- معلومات الدور -->
                                    <div class="space-y-4">
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">معلومات الدور</h3>
        
                                        <!-- اسم الدور -->
                                        <div>
                                            <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">اسم الدور *</label>
                                            <input type="text" name="name" id="name" value="{{ old('name') }}" required
                                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400"
                                                   placeholder="مثال: محرر، مدير، مستخدم">
                                            @error('name')
                                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                            @enderror
                                        </div>
        
                                        <!-- Guard Name -->
                                        <div>
                                            <label for="guard_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Guard Name *</label>
                                            <select name="guard_name" id="guard_name" required
                                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400">
                                                <option value="web" {{ old('guard_name', 'web') == 'web' ? 'selected' : '' }}>Web</option>
                                                <option value="api" {{ old('guard_name') == 'api' ? 'selected' : '' }}>API</option>
                                            </select>
                                            @error('guard_name')
                                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                            @enderror
                                        </div>
                                    </div>
        
                                    <!-- الصلاحيات -->
                                    <div class="space-y-4">
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">الصلاحيات *</h3>
        
                                        <div class="max-h-96 overflow-y-auto border border-gray-200 dark:border-gray-600 dark:bg-gray-700 rounded-md p-4">
                                            @if($permissions->count() > 0)
                                                @foreach($permissions as $group => $groupPermissions)
                                                    <div class="mb-4">
                                                        <h4 class="text-sm font-medium text-gray-800 dark:text-gray-200 mb-2 capitalize">
                                                            {{ ucfirst($group) }}
                                                        </h4>
                                                        <div class="space-y-2 ml-4">
                                                            @foreach($groupPermissions as $permission)
                                                                <label class="flex items-center">
                                                                    <input type="checkbox" name="permissions[]" value="{{ $permission->name }}"
                                                                           class="rounded border-gray-300 dark:border-gray-600 dark:bg-gray-700 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400"
                                                                           {{ in_array($permission->name, old('permissions', [])) ? 'checked' : '' }}>
                                                                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">{{ $permission->name }}</span>
                                                                </label>
                                                            @endforeach
                                                        </div>
                                                    </div>
                                                @endforeach
                                            @else
                                                <p class="text-gray-500 dark:text-gray-400 text-center py-4">لا توجد صلاحيات متاحة</p>
                                            @endif
                                        </div>
        
                                        @error('permissions')
                                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                        @enderror
        
                                        <!-- أزرار التحديد السريع -->
                                        <div class="flex space-x-2 rtl:space-x-reverse">
                                            <button type="button" onclick="selectAll()" class="text-sm bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 px-3 py-1 rounded transition">
                                                تحديد الكل
                                            </button>
                                            <button type="button" onclick="deselectAll()" class="text-sm bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-300 px-3 py-1 rounded transition">
                                                إلغاء تحديد الكل
                                            </button>
                                        </div>
                                    </div>
                                </div>
        
                                <!-- أزرار التحكم -->
                                <div class="flex items-center justify-end mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                                    <a href="{{ route('roles.index') }}" class="bg-gray-500 hover:bg-gray-700 dark:bg-gray-600 dark:hover:bg-gray-800 text-white font-bold py-2 px-4 rounded-lg mr-3 transition">
                                        إلغاء
                                    </a>
                                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white font-bold py-2 px-4 rounded-lg transition">
                                        <svg class="w-4 h-4 inline-block mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                                        </svg>
                                        إنشاء الدور
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        
            <script>
                function selectAll() {
                    const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
                    checkboxes.forEach(checkbox => checkbox.checked = true);
                }
        
                function deselectAll() {
                    const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
                    checkboxes.forEach(checkbox => checkbox.checked = false);
                }
            </script>
    </div>
</div>
@endsection
