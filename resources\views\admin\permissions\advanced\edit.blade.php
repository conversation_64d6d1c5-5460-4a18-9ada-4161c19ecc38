<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ __('Edit Permissions') }} - {{ $role->name }}</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <style>
    .permission-group {
        transition: all 0.2s ease-in-out;
    }

    .permission-group:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.1);
    }

    .dark .permission-group:hover {
        box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.3);
    }

    .permission-checkbox:checked + label {
        background-color: rgba(34, 197, 94, 0.1);
        border-color: rgb(34, 197, 94);
    }

    .dark .permission-checkbox:checked + label {
        background-color: rgba(34, 197, 94, 0.2);
    }
    </style>
</head>
<body class="font-sans antialiased bg-gray-100 dark:bg-gray-900">
    <div class="min-h-screen">
        <!-- Navigation -->
        @include('layouts.navigation')

        <!-- Page Header -->
        <header class="bg-white dark:bg-gray-800 shadow">
            <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">
                            {{ __('تعديل صلاحيات') }} {{ $role->display_name ?? $role->name }}
                        </h1>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ __('Manage role permissions and access levels') }}</p>
                    </div>
                    <div class="flex gap-3">
                        <a href="{{ route('admin.permissions.advanced.show', $role) }}"
                           class="bg-gray-500 hover:bg-gray-600 dark:bg-gray-600 dark:hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg transition duration-200">
                            <svg class="w-4 h-4 inline-block mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                            </svg>
                            {{ __('العودة') }}
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="py-6">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Breadcrumb -->
                <nav class="flex mb-6" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3 rtl:space-x-reverse">
                        <li class="inline-flex items-center">
                            <a href="{{ route('admin.permissions.advanced.index') }}"
                               class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                                <svg class="w-3 h-3 mr-2.5" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                                </svg>
                                {{ __('الصلاحيات') }}
                            </a>
                        </li>
                        <li class="inline-flex items-center">
                            <svg class="w-3 h-3 text-gray-400 mx-1 rtl:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                            <a href="{{ route('admin.permissions.advanced.show', $role) }}"
                               class="text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                                {{ $role->display_name ?? $role->name }}
                            </a>
                        </li>
                        <li aria-current="page">
                            <div class="flex items-center">
                                <svg class="w-3 h-3 text-gray-400 mx-1 rtl:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                                <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">{{ __('تعديل') }}</span>
                            </div>
                        </li>
                    </ol>
                </nav>

                <!-- Quick Actions -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg border border-gray-200 dark:border-gray-700 mb-6">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">{{ __('إجراءات سريعة') }}</h3>
                    </div>
                    <div class="p-6">
                        <div class="flex flex-wrap gap-3">
                            <button type="button" onclick="selectAll()"
                                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 transition duration-200">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                {{ __('تحديد الكل') }}
                            </button>
                            <button type="button" onclick="selectNone()"
                                    class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600 transition duration-200">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                                {{ __('إلغاء الكل') }}
                            </button>
                            <button type="button" onclick="selectViewOnly()"
                                    class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600 transition duration-200">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                    <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                                </svg>
                                {{ __('عرض فقط') }}
                            </button>
                            <button type="button" onclick="selectEditor()"
                                    class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600 transition duration-200">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                                </svg>
                                {{ __('محرر') }}
                            </button>
                        </div>
                        <div class="mt-4 flex items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">{{ __('المحدد:') }}</span>
                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200" id="selectedCount">0</span>
                        </div>
                    </div>
                </div>

                <!-- Permissions Form -->
                <form method="POST" action="{{ route('admin.permissions.advanced.update', $role) }}" id="permissionsForm">
                    @csrf
                    @method('PUT')

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg border border-gray-200 dark:border-gray-700">
                        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">{{ __('الصلاحيات حسب المجموعات') }}</h3>
                        </div>
                        <div class="p-6">
                            @foreach($groups as $group)
                            <div class="permission-group bg-gray-50 dark:bg-gray-700 rounded-lg p-6 mb-6 border border-gray-200 dark:border-gray-600">
                                <!-- Group Header -->
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center space-x-4 rtl:space-x-reverse">
                                        <div class="flex-shrink-0">
                                            <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                                                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z" clip-rule="evenodd"></path>
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                                {{ $group->display_name }}
                                            </h4>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                                {{ $group->description }}
                                            </p>
                                        </div>
                                    </div>
                                    <div class="flex items-center">
                                        <label class="inline-flex items-center">
                                            <input type="checkbox"
                                                   class="group-checkbox rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 dark:border-gray-600 dark:bg-gray-700"
                                                   id="group_{{ $group->id }}"
                                                   data-group="{{ $group->id }}"
                                                   onchange="toggleGroup({{ $group->id }})">
                                            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">{{ __('تحديد الكل') }}</span>
                                        </label>
                                    </div>
                                </div>

                                <!-- Permissions Grid -->
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
                                    @foreach($group->permissions as $permission)
                                    <div class="relative">
                                        <label class="flex items-center p-3 bg-white dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-600 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-200">
                                            <input type="checkbox"
                                                   class="permission-checkbox rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50 dark:border-gray-600 dark:bg-gray-700"
                                                   name="permissions[]"
                                                   value="{{ $permission->id }}"
                                                   id="permission_{{ $permission->id }}"
                                                   data-group="{{ $group->id }}"
                                                   data-type="{{ explode('.', $permission->name)[1] ?? 'other' }}"
                                                   {{ in_array($permission->id, $rolePermissions) ? 'checked' : '' }}
                                                   onchange="updateCounts()">
                                            <div class="ml-3 flex-1 min-w-0">
                                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                                    {{ ucfirst(str_replace(['.', '_'], ' ', $permission->name)) }}
                                                </span>
                                                @if($permission->guard_name !== 'web')
                                                    <span class="text-xs text-gray-500 dark:text-gray-400 block">({{ $permission->guard_name }})</span>
                                                @endif
                                            </div>
                                        </label>
                                    </div>
                                    @endforeach
                                </div>

                                @if($group->permissions->isEmpty())
                                <div class="text-center py-8">
                                    <svg class="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span class="text-gray-500 dark:text-gray-400">{{ __('لا توجد صلاحيات في هذه المجموعة') }}</span>
                                </div>
                                @endif
                            </div>
                            @endforeach

                            @if($groups->isEmpty())
                            <div class="text-center py-12">
                                <svg class="w-12 h-12 text-yellow-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">{{ __('لا توجد مجموعات صلاحيات') }}</h3>
                                <p class="text-gray-500 dark:text-gray-400">{{ __('يرجى إنشاء مجموعات الصلاحيات أولاً') }}</p>
                            </div>
                            @endif
                        </div>

                        <!-- Form Footer -->
                        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
                            <div class="flex justify-between items-center">
                                <div>
                                    <span class="text-sm text-gray-600 dark:text-gray-400">
                                        {{ __('إجمالي الصلاحيات:') }} {{ $groups->sum(function($group) { return $group->permissions->count(); }) }}
                                    </span>
                                </div>
                                <div class="flex gap-3">
                                    <button type="button" onclick="window.history.back()"
                                            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600 transition duration-200">
                                        {{ __('إلغاء') }}
                                    </button>
                                    <button type="submit" id="saveBtn" form="permissionsForm"
                                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 transition duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                        </svg>
                                        {{ __('حفظ الصلاحيات') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </main>
    </div>

    <script>
    // تحديث العدادات
    function updateCounts() {
        const selectedCount = document.querySelectorAll('.permission-checkbox:checked').length;
        document.getElementById('selectedCount').textContent = selectedCount;

        // تحديث حالة checkboxes المجموعات
        document.querySelectorAll('.group-checkbox').forEach(groupCheckbox => {
            const groupId = groupCheckbox.dataset.group;
            const groupPermissions = document.querySelectorAll(`.permission-checkbox[data-group="${groupId}"]`);
            const checkedPermissions = document.querySelectorAll(`.permission-checkbox[data-group="${groupId}"]:checked`);

            if (checkedPermissions.length === 0) {
                groupCheckbox.checked = false;
                groupCheckbox.indeterminate = false;
            } else if (checkedPermissions.length === groupPermissions.length) {
                groupCheckbox.checked = true;
                groupCheckbox.indeterminate = false;
            } else {
                groupCheckbox.checked = false;
                groupCheckbox.indeterminate = true;
            }
        });
    }

    // تبديل مجموعة كاملة
    function toggleGroup(groupId) {
        const groupCheckbox = document.querySelector(`#group_${groupId}`);
        const groupPermissions = document.querySelectorAll(`.permission-checkbox[data-group="${groupId}"]`);

        groupPermissions.forEach(checkbox => {
            checkbox.checked = groupCheckbox.checked;
        });

        updateCounts();
    }

    // تحديد الكل
    function selectAll() {
        document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
            checkbox.checked = true;
        });
        updateCounts();
    }

    // إلغاء تحديد الكل
    function selectNone() {
        document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });
        updateCounts();
    }

    // تحديد العرض فقط
    function selectViewOnly() {
        document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
            checkbox.checked = checkbox.dataset.type === 'view';
        });
        updateCounts();
    }

    // تحديد المحرر
    function selectEditor() {
        document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
            const type = checkbox.dataset.type;
            checkbox.checked = ['view', 'create', 'edit'].includes(type);
        });
        updateCounts();
    }



    // تهيئة الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        updateCounts();

        // إضافة event listener للزر
        const saveBtn = document.getElementById('saveBtn');
        const form = document.getElementById('permissionsForm');
        let isSubmitting = false; // متغير لتتبع حالة الإرسال

        if (saveBtn && form) {
            saveBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Save button clicked!'); // للتشخيص

                // تعطيل الزر لمنع الإرسال المتكرر
                saveBtn.disabled = true;
                isSubmitting = true; // تعيين حالة الإرسال
                saveBtn.innerHTML = `
                    <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {{ __('جاري الحفظ...') }}
                `;

                // إرسال النموذج
                setTimeout(() => {
                    form.submit();
                }, 100);
            });
        }

        // تأكيد قبل المغادرة إذا كانت هناك تغييرات
        const currentChecked = Array.from(document.querySelectorAll('.permission-checkbox:checked')).map(cb => cb.value);
        const originalChecked = @json($rolePermissions);

        window.addEventListener('beforeunload', function(e) {
            // لا تظهر التحذير إذا كان المستخدم يحفظ البيانات
            if (isSubmitting) {
                return;
            }

            const newChecked = Array.from(document.querySelectorAll('.permission-checkbox:checked')).map(cb => cb.value);

            if (JSON.stringify(newChecked.sort()) !== JSON.stringify(originalChecked.sort())) {
                e.preventDefault();
                e.returnValue = '{{ __("لديك تغييرات غير محفوظة. هل أنت متأكد من المغادرة؟") }}';
            }
        });
    });
    </script>
</body>
</html>
