<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Rating;
use App\Models\Booking;
use Illuminate\Http\Request;

class RatingController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            $user = auth()->user();
            if (!$user || (!$user->hasRole('client') && !$user->hasRole('user'))) {
                abort(403, 'Access denied. Client or User role required.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of user's ratings.
     */
    public function index()
    {
        $user = auth()->user();

        // Get ratings made by this user (using rater_id)
        $ratings = Rating::with(['booking.worker', 'booking.company', 'booking.client'])
                        ->where('rater_id', $user->id)
                        ->orderBy('created_at', 'desc')
                        ->paginate(10);

        return view('client.ratings.index', compact('ratings'));
    }

    /**
     * Show the form for creating a new rating.
     */
    public function create(Request $request)
    {
        $bookingId = $request->get('booking_id');

        if (!$bookingId) {
            return redirect()->route('client.bookings.index')
                           ->with('error', __('Please select a booking to rate.'));
        }

        $booking = Booking::with(['worker', 'company'])
                         ->where('id', $bookingId)
                         ->whereHas('client', function($query) {
                             $query->where('user_id', auth()->id());
                         })
                         ->where('status', 'completed')
                         ->first();

        if (!$booking) {
            return redirect()->route('client.bookings.index')
                           ->with('error', __('Booking not found or not eligible for rating.'));
        }

        // Check if already rated
        $existingRating = Rating::where('booking_id', $booking->id)->first();
        if ($existingRating) {
            return redirect()->route('client.ratings.show', $existingRating)
                           ->with('info', __('You have already rated this booking.'));
        }

        return view('client.ratings.create', compact('booking'));
    }

    /**
     * Store a newly created rating.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'booking_id' => 'required|exists:bookings,id',
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:1000'
        ]);

        $booking = Booking::where('id', $validated['booking_id'])
                         ->whereHas('client', function($query) {
                             $query->where('user_id', auth()->id());
                         })
                         ->where('status', 'completed')
                         ->first();

        if (!$booking) {
            return back()->withErrors(['booking_id' => __('Invalid booking.')]);
        }

        // Check if already rated
        $existingRating = Rating::where('booking_id', $booking->id)->first();
        if ($existingRating) {
            return redirect()->route('client.ratings.show', $existingRating)
                           ->with('info', __('You have already rated this booking.'));
        }

        Rating::create([
            'rating_type' => 'worker', // or 'company' depending on what they're rating
            'rater_id' => auth()->id(),
            'rated_id' => $booking->worker_id, // or company_id
            'booking_id' => $validated['booking_id'],
            'star_rating' => $validated['rating'],
            'comment' => $validated['comment'],
            'rating_date' => now()
        ]);

        return redirect()->route('client.ratings.index')
                        ->with('success', __('Rating submitted successfully.'));
    }

    /**
     * Display the specified rating.
     */
    public function show(Rating $rating)
    {
        // Ensure user can only view their own ratings
        if ($rating->rater_id !== auth()->id()) {
            abort(403);
        }

        $rating->load(['booking.worker', 'booking.company']);

        return view('client.ratings.show', compact('rating'));
    }

    /**
     * Show the form for editing the specified rating.
     */
    public function edit(Rating $rating)
    {
        // Ensure user can only edit their own ratings
        if ($rating->rater_id !== auth()->id()) {
            abort(403);
        }

        $rating->load(['booking.worker', 'booking.company']);

        return view('client.ratings.edit', compact('rating'));
    }

    /**
     * Update the specified rating.
     */
    public function update(Request $request, Rating $rating)
    {
        // Ensure user can only update their own ratings
        if ($rating->rater_id !== auth()->id()) {
            abort(403);
        }

        $validated = $request->validate([
            'star_rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:1000'
        ]);

        $rating->update($validated);

        return redirect()->route('client.ratings.index')
                        ->with('success', __('Rating updated successfully.'));
    }

    /**
     * Remove the specified rating.
     */
    public function destroy(Rating $rating)
    {
        // Ensure user can only delete their own ratings
        if ($rating->rater_id !== auth()->id()) {
            abort(403);
        }

        $rating->delete();

        return redirect()->route('client.ratings.index')
                        ->with('success', __('Rating deleted successfully.'));
    }
}
