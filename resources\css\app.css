@tailwind base;
@tailwind components;
@tailwind utilities;

/* Dark Mode Enhancements */

/* Smooth transitions for dark mode */
*,
*::before,
*::after {
    transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out, border-color 0.2s ease-in-out;
}

/* Body and HTML transitions */
html, body {
    transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out;
}

/* Custom scrollbar for dark mode */
.dark ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.dark ::-webkit-scrollbar-track {
    background: #374151;
    border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb {
    background: #6B7280;
    border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb:hover {
    background: #9CA3AF;
}
