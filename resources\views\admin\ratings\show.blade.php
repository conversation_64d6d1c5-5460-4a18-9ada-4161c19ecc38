@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Page Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                    {{ __('Rating Details') }} #{{ $rating->id }}
                </h1>
                <div class="flex space-x-3">
                    <a href="{{ route('admin.ratings.index') }}"
                       class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-md transition duration-200">
                        <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        {{ __('Back to Ratings') }}
                    </a>
                    @can('ratings.edit')
                    <a href="{{ route('admin.ratings.edit', $rating) }}"
                       class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-200">
                        <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        {{ __('Edit Rating') }}
                    </a>
                    @endcan
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Rating Overview -->
            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <!-- Star Rating -->
                        <div class="text-center mb-6">
                            <div class="text-6xl font-bold text-yellow-500 mb-2">{{ $rating->star_rating }}</div>
                            <div class="flex justify-center mb-2">
                                @for($i = 1; $i <= 5; $i++)
                                    @if($i <= $rating->star_rating)
                                        <svg class="w-8 h-8" fill="#FCD34D" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                    @else
                                        <svg class="w-8 h-8" fill="#D1D5DB" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                    @endif
                                @endfor
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ __('out of 5 stars') }}</p>
                        </div>

                        <!-- Rating Type -->
                        <div class="text-center mb-6">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                @if($rating->rating_type === 'worker') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                @elseif($rating->rating_type === 'company') bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200
                                @else bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 @endif">
                                @if($rating->rating_type === 'worker')
                                    🏠 {{ __('Worker Rating') }}
                                @elseif($rating->rating_type === 'company')
                                    🏢 {{ __('Company Rating') }}
                                @else
                                    👤 {{ __('Client Rating') }}
                                @endif
                            </span>
                        </div>

                        <!-- Rating Date -->
                        <div class="text-center">
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                {{ __('Rated on') }} {{ $rating->rating_date->format('M d, Y \a\t H:i') }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Quick Info -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mt-6">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Quick Info') }}</h3>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('Rating ID') }}</span>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">#{{ $rating->id }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('Booking ID') }}</span>
                                <a href="{{ route('admin.bookings.show', $rating->booking) }}"
                                   class="text-sm font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400">
                                    #{{ $rating->booking_id }}
                                </a>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('Created') }}</span>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $rating->created_at->diffForHumans() }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed Information -->
            <div class="lg:col-span-2">
                <div class="space-y-6">
                    <!-- Participants -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Participants') }}</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Rater -->
                                <div>
                                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">{{ __('Rated By') }}</h4>
                                    <div class="flex items-center space-x-3">
                                        <div class="flex-shrink-0">
                                            <div class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                                                <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                                </svg>
                                            </div>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $rating->rater->name }}</p>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $rating->rater->email }}</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Rated Entity -->
                                <div>
                                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">{{ __('Rated Entity') }}</h4>
                                    <div class="flex items-center space-x-3">
                                        <div class="flex-shrink-0">
                                            <div class="w-10 h-10 rounded-full
                                                @if($rating->rating_type === 'worker') bg-blue-100 dark:bg-blue-900
                                                @elseif($rating->rating_type === 'company') bg-purple-100 dark:bg-purple-900
                                                @else bg-green-100 dark:bg-green-900 @endif
                                                flex items-center justify-center">
                                                @if($rating->rating_type === 'worker')
                                                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                                                    </svg>
                                                @elseif($rating->rating_type === 'company')
                                                    <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 114 0 2 2 0 01-4 0zm8-1a1 1 0 100 2h2a1 1 0 100-2h-2z" clip-rule="evenodd"></path>
                                                    </svg>
                                                @else
                                                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                                    </svg>
                                                @endif
                                            </div>
                                        </div>
                                        <div>
                                            @if($rating->rating_type === 'worker')
                                                @php $worker = \App\Models\Worker::find($rating->rated_id); @endphp
                                                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $worker->name ?? 'Unknown Worker' }}</p>
                                                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $worker->nationality ?? '' }} - {{ $worker->category ?? '' }}</p>
                                            @elseif($rating->rating_type === 'company')
                                                @php $company = \App\Models\Company::find($rating->rated_id); @endphp
                                                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $company->company_name ?? 'Unknown Company' }}</p>
                                                <p class="text-sm text-gray-500 dark:text-gray-400">{{ __('Service Company') }}</p>
                                            @else
                                                @php $client = \App\Models\Client::with('user')->find($rating->rated_id); @endphp
                                                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $client->user->name ?? 'Unknown Client' }}</p>
                                                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $client->user->email ?? '' }}</p>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Criteria Ratings -->
                    @if($rating->criteria_ratings)
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Detailed Criteria Ratings') }}</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                @foreach($rating->criteria_ratings as $criterion => $score)
                                    <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                            {{ __(ucfirst(str_replace('_', ' ', $criterion))) }}
                                        </span>
                                        <div class="flex items-center">
                                            <div class="flex mr-2">
                                                @for($i = 1; $i <= 5; $i++)
                                                    @if($i <= $score)
                                                        <svg class="w-4 h-4" fill="#FCD34D" viewBox="0 0 20 20">
                                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                        </svg>
                                                    @else
                                                        <svg class="w-4 h-4" fill="#D1D5DB" viewBox="0 0 20 20">
                                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                        </svg>
                                                    @endif
                                                @endfor
                                            </div>
                                            <span class="text-sm font-bold text-gray-900 dark:text-white">{{ $score }}/5</span>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Comment -->
                    @if($rating->comment)
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Comment') }}</h3>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <p class="text-gray-700 dark:text-gray-300 leading-relaxed">{{ $rating->comment }}</p>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Images -->
                    @if($rating->images && count($rating->images) > 0)
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Images') }}</h3>
                            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                                @foreach($rating->images as $image)
                                    <div class="aspect-square">
                                        <img src="{{ asset('storage/' . $image) }}"
                                             alt="{{ __('Rating Image') }}"
                                             class="w-full h-full object-cover rounded-lg border border-gray-200 dark:border-gray-600 hover:opacity-75 transition cursor-pointer"
                                             onclick="openImageModal('{{ asset('storage/' . $image) }}')">
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Related Booking -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Related Booking') }}</h3>
                            <div class="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-4">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h4 class="font-medium text-blue-900 dark:text-blue-100">{{ __('Booking') }} #{{ $rating->booking->id }}</h4>
                                        <p class="text-sm text-blue-700 dark:text-blue-300 mt-1">
                                            {{ $rating->booking->client->user->name }} → {{ $rating->booking->worker->name }}
                                        </p>
                                        <p class="text-sm text-blue-600 dark:text-blue-400 mt-1">
                                            {{ $rating->booking->start_date->format('M d, Y') }} - {{ $rating->booking->end_date->format('M d, Y') }}
                                        </p>
                                    </div>
                                    <a href="{{ route('admin.bookings.show', $rating->booking) }}"
                                       class="inline-flex items-center px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition">
                                        {{ __('View Booking') }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div id="imageModal" class="fixed inset-0 bg-black bg-opacity-75 hidden z-50 flex items-center justify-center" onclick="closeImageModal()">
    <div class="max-w-4xl max-h-full p-4">
        <img id="modalImage" src="" alt="" class="max-w-full max-h-full object-contain">
    </div>
</div>

<script>
function openImageModal(imageSrc) {
    document.getElementById('modalImage').src = imageSrc;
    document.getElementById('imageModal').classList.remove('hidden');
}

function closeImageModal() {
    document.getElementById('imageModal').classList.add('hidden');
}
</script>

@endsection
