<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class PermissionsAndRolesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء الصلاحيات
        $permissions = [
            // صلاحيات المستخدمين
            'users.view' => 'عرض المستخدمين',
            'users.create' => 'إنشاء مستخدمين',
            'users.edit' => 'تعديل المستخدمين',
            'users.delete' => 'حذف المستخدمين',

            // صلاحيات الأدوار
            'roles.view' => 'عرض الأدوار',
            'roles.create' => 'إنشاء أدوار',
            'roles.edit' => 'تعديل الأدوار',
            'roles.delete' => 'حذف الأدوار',

            // صلاحيات الصلاحيات
            'permissions.view' => 'عرض الصلاحيات',
            'permissions.create' => 'إنشاء صلاحيات',
            'permissions.edit' => 'تعديل الصلاحيات',
            'permissions.delete' => 'حذف الصلاحيات',

            // صلاحيات النظام
            'system.settings' => 'إعدادات النظام',
            'system.logs' => 'عرض سجلات النظام',
            'system.backup' => 'نسخ احتياطي',

            // صلاحيات التقارير
            'reports.view' => 'عرض التقارير',
            'reports.export' => 'تصدير التقارير',
        ];

        foreach ($permissions as $name => $description) {
            Permission::firstOrCreate(['name' => $name], ['guard_name' => 'web']);
        }

        // إنشاء الأدوار
        $adminRole = Role::firstOrCreate(['name' => 'admin'], ['guard_name' => 'web']);
        $editorRole = Role::firstOrCreate(['name' => 'editor'], ['guard_name' => 'web']);
        $userRole = Role::firstOrCreate(['name' => 'user'], ['guard_name' => 'web']);

        // إعطاء جميع الصلاحيات للمدير
        $adminRole->givePermissionTo(Permission::all());

        // إعطاء صلاحيات محددة للمحرر
        $editorRole->givePermissionTo([
            'users.view',
            'users.create',
            'users.edit',
            'roles.view',
            'permissions.view',
            'reports.view',
        ]);

        // إعطاء صلاحيات أساسية للمستخدم العادي
        $userRole->givePermissionTo([
            'users.view',
        ]);

        // إنشاء مستخدم مدير افتراضي
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'مدير النظام',
                'password' => bcrypt('password'),
                'status' => true,
            ]
        );

        $admin->assignRole('admin');

        // إنشاء مستخدم محرر افتراضي
        $editor = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'محرر النظام',
                'password' => bcrypt('password'),
                'status' => true,
            ]
        );

        $editor->assignRole('editor');

        $this->command->info('تم إنشاء الصلاحيات والأدوار والمستخدمين الافتراضيين بنجاح!');
    }
}
