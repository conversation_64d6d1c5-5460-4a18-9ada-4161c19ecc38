<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Currency;
use Illuminate\Http\Request;

class CurrencyController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('permission:currencies.view')->only(['index', 'show']);
        $this->middleware('permission:currencies.create')->only(['create', 'store']);
        $this->middleware('permission:currencies.edit')->only(['edit', 'update']);
        $this->middleware('permission:currencies.delete')->only(['destroy']);
    }

    /**
     * Display a listing of currencies.
     */
    public function index()
    {
        $currencies = Currency::orderBy('is_default', 'desc')
                             ->orderBy('name')
                             ->paginate(10);

        return view('admin.currencies.index', compact('currencies'));
    }

    /**
     * Show the form for creating a new currency.
     */
    public function create()
    {
        return view('admin.currencies.create');
    }

    /**
     * Store a newly created currency.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|size:3|unique:currencies,code',
            'symbol' => 'required|string|max:10',
            'exchange_rate' => 'required|numeric|min:0.0001',
            'position' => 'required|in:before,after',
            'is_active' => 'boolean',
            'is_default' => 'boolean'
        ]);

        $validated['is_active'] = $request->has('is_active');
        $validated['is_default'] = $request->has('is_default');

        $currency = Currency::create($validated);

        // If this is set as default, remove default from others
        if ($currency->is_default) {
            Currency::where('id', '!=', $currency->id)
                   ->update(['is_default' => false]);
        }

        return redirect()->route('admin.currencies.index')
                        ->with('success', __('Currency created successfully.'));
    }

    /**
     * Display the specified currency.
     */
    public function show(Currency $currency)
    {
        return view('admin.currencies.show', compact('currency'));
    }

    /**
     * Show the form for editing the specified currency.
     */
    public function edit(Currency $currency)
    {
        return view('admin.currencies.edit', compact('currency'));
    }

    /**
     * Update the specified currency.
     */
    public function update(Request $request, Currency $currency)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|size:3|unique:currencies,code,' . $currency->id,
            'symbol' => 'required|string|max:10',
            'exchange_rate' => 'required|numeric|min:0.0001',
            'position' => 'required|in:before,after',
            'is_active' => 'boolean',
            'is_default' => 'boolean'
        ]);

        $validated['is_active'] = $request->has('is_active');
        $validated['is_default'] = $request->has('is_default');

        $currency->update($validated);

        // If this is set as default, remove default from others
        if ($currency->is_default) {
            Currency::where('id', '!=', $currency->id)
                   ->update(['is_default' => false]);
        }

        return redirect()->route('admin.currencies.index')
                        ->with('success', __('Currency updated successfully.'));
    }

    /**
     * Remove the specified currency.
     */
    public function destroy(Currency $currency)
    {
        // Prevent deletion of default currency
        if ($currency->is_default) {
            return back()->withErrors(['error' => __('Cannot delete the default currency.')]);
        }

        $currency->delete();

        return redirect()->route('admin.currencies.index')
                        ->with('success', __('Currency deleted successfully.'));
    }

    /**
     * Set currency as default
     */
    public function setDefault(Currency $currency)
    {
        $currency->setAsDefault();

        return back()->with('success', __('Currency set as default successfully.'));
    }
}
