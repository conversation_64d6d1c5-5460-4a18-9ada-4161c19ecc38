<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     */
    public function create(): View
    {
        return view('auth.login');
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request): RedirectResponse
    {
        $request->authenticate();

        $request->session()->regenerate();

        // Redirect based on user role
        $user = Auth::user();

        // توجيه الشركات إلى لوحة تحكم الشركة
        if ($user && $user->hasRole('company')) {
            return redirect()->intended(route('company.dashboard'));
        }

        // توجيه المستخدمين العاديين إلى لوحة تحكم العميل
        if ($user && $user->hasRole('user')) {
            return redirect()->intended(route('client.dashboard'));
        }

        // توجيه الأدمن والمحررين إلى لوحة تحكم الإدارة
        if ($user && ($user->hasRole('admin') || $user->hasRole('editor') || $user->hasRole('super-admin'))) {
            return redirect()->intended(route('dashboard', absolute: false));
        }

        // التوجيه الافتراضي للأدوار الأخرى
        return redirect()->intended(route('client.dashboard'));
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/');
    }
}
