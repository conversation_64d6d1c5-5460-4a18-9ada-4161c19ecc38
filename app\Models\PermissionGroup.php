<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Permission\Models\Permission;

class PermissionGroup extends Model
{
    protected $fillable = [
        'name',
        'display_name',
        'description',
        'icon',
        'sort_order',
        'is_protected'
    ];

    protected $casts = [
        'is_protected' => 'boolean',
    ];

    /**
     * Get the permissions for this group.
     */
    public function permissions(): HasMany
    {
        return $this->hasMany(Permission::class, 'group_id');
    }

    /**
     * Scope to get only protected groups.
     */
    public function scopeProtected($query)
    {
        return $query->where('is_protected', true);
    }

    /**
     * Scope to get groups ordered by sort_order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }
}
