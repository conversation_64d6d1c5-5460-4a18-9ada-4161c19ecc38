<?php

namespace Database\Seeders;

use App\Models\Nationality;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class NationalitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $nationalities = [
            ['name_en' => 'Filipino', 'name_ar' => 'فلبينية', 'sort_order' => 1],
            ['name_en' => 'Indonesian', 'name_ar' => 'إندونيسية', 'sort_order' => 2],
            ['name_en' => 'Sri Lankan', 'name_ar' => 'سريلانكية', 'sort_order' => 3],
            ['name_en' => 'Indian', 'name_ar' => 'هندية', 'sort_order' => 4],
            ['name_en' => 'Bangladeshi', 'name_ar' => 'بنغلاديشية', 'sort_order' => 5],
            ['name_en' => 'Ethiopian', 'name_ar' => 'إثيوبية', 'sort_order' => 6],
            ['name_en' => 'Kenyan', 'name_ar' => 'كينية', 'sort_order' => 7],
            ['name_en' => 'Other', 'name_ar' => 'أخرى', 'sort_order' => 8],
        ];

        foreach ($nationalities as $nationality) {
            Nationality::create($nationality);
        }
    }
}
