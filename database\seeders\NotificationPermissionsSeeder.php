<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class NotificationPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if permission group already exists
        $existingGroup = DB::table('permission_groups')->where('name', 'notifications')->first();

        if ($existingGroup) {
            $groupId = $existingGroup->id;
        } else {
            // Add permission group
            $groupId = DB::table('permission_groups')->insertGetId([
                'name' => 'notifications',
                'display_name' => 'Notification Management',
                'description' => 'Manage notifications and alerts',
                'icon' => 'bell',
                'sort_order' => 17,
                'is_protected' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // Add permissions for notifications
        $permissionNames = [
            'notifications.view',
            'notifications.create',
            'notifications.edit',
            'notifications.delete'
        ];

        foreach ($permissionNames as $permissionName) {
            $existingPermission = DB::table('permissions')->where('name', $permissionName)->first();

            if (!$existingPermission) {
                DB::table('permissions')->insert([
                    'name' => $permissionName,
                    'group_id' => $groupId,
                    'guard_name' => 'web',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }
}
