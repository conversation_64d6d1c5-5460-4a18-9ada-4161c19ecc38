@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ __('User Permissions Test') }}</h1>
            <p class="mt-2 text-gray-600 dark:text-gray-400">{{ __('Check current user permissions and roles') }}</p>
        </div>

        <!-- User Information -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">{{ __('Current User Information') }}</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ __('Name') }}</p>
                    <p class="font-semibold text-gray-900 dark:text-white">{{ auth()->user()->name }}</p>
                </div>
                
                <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ __('Email') }}</p>
                    <p class="font-semibold text-gray-900 dark:text-white">{{ auth()->user()->email }}</p>
                </div>
            </div>
        </div>

        <!-- User Roles -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">{{ __('User Roles') }}</h3>
            
            @if(auth()->user()->roles->count() > 0)
                <div class="flex flex-wrap gap-2">
                    @foreach(auth()->user()->roles as $role)
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                            {{ $role->name }}
                        </span>
                    @endforeach
                </div>
            @else
                <p class="text-gray-600 dark:text-gray-400">{{ __('No roles assigned') }}</p>
            @endif
        </div>

        <!-- User Permissions -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">{{ __('User Permissions') }}</h3>
            
            @php
                $permissions = auth()->user()->getAllPermissions();
            @endphp
            
            @if($permissions->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    @foreach($permissions as $permission)
                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            {{ $permission->name }}
                        </span>
                    @endforeach
                </div>
            @else
                <p class="text-gray-600 dark:text-gray-400">{{ __('No permissions assigned') }}</p>
            @endif
        </div>

        <!-- Permission Tests -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">{{ __('Permission Tests') }}</h3>
            
            <div class="space-y-3">
                <!-- Ratings Permissions -->
                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <span class="text-gray-700 dark:text-gray-300">ratings.view</span>
                    @if(auth()->user()->can('ratings.view'))
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            ✓ {{ __('Allowed') }}
                        </span>
                    @else
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                            ✗ {{ __('Denied') }}
                        </span>
                    @endif
                </div>

                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <span class="text-gray-700 dark:text-gray-300">ratings.create</span>
                    @if(auth()->user()->can('ratings.create'))
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            ✓ {{ __('Allowed') }}
                        </span>
                    @else
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                            ✗ {{ __('Denied') }}
                        </span>
                    @endif
                </div>

                <!-- Notifications Permissions -->
                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <span class="text-gray-700 dark:text-gray-300">notifications.view</span>
                    @if(auth()->user()->can('notifications.view'))
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            ✓ {{ __('Allowed') }}
                        </span>
                    @else
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                            ✗ {{ __('Denied') }}
                        </span>
                    @endif
                </div>

                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <span class="text-gray-700 dark:text-gray-300">notifications.create</span>
                    @if(auth()->user()->can('notifications.create'))
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            ✓ {{ __('Allowed') }}
                        </span>
                    @else
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                            ✗ {{ __('Denied') }}
                        </span>
                    @endif
                </div>
            </div>
        </div>

        <!-- Test Links -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">{{ __('Test Links') }}</h3>
            
            <div class="space-y-3">
                @can('ratings.view')
                    <a href="{{ route('admin.ratings.index') }}" 
                       class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200">
                        {{ __('Test Ratings Page') }}
                    </a>
                @else
                    <span class="inline-flex items-center px-4 py-2 bg-gray-400 text-white rounded-lg cursor-not-allowed">
                        {{ __('Ratings Page (No Permission)') }}
                    </span>
                @endcan

                @can('notifications.view')
                    <a href="{{ route('admin.notifications.index') }}" 
                       class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200">
                        {{ __('Test Notifications Page') }}
                    </a>
                @else
                    <span class="inline-flex items-center px-4 py-2 bg-gray-400 text-white rounded-lg cursor-not-allowed">
                        {{ __('Notifications Page (No Permission)') }}
                    </span>
                @endcan
            </div>
        </div>

        <!-- Back to Dashboard -->
        <div class="mt-8 text-center">
            <a href="{{ route('dashboard') }}" 
               class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                {{ __('Back to Dashboard') }}
            </a>
        </div>
    </div>
</div>
@endsection
