@extends('layouts.admin')

@section('title', __('Permission Statistics'))

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.permissions.advanced.index') }}">{{ __('Permissions') }}</a>
                            </li>
                            <li class="breadcrumb-item active">{{ __('Statistics') }}</li>
                        </ol>
                    </nav>
                    <h1 class="h3 mb-0">{{ __('Permission Statistics') }}</h1>
                    <p class="text-muted">{{ __('Overview of permissions usage and activity') }}</p>
                </div>
                <div>
                    <a href="{{ route('admin.permissions.advanced.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> {{ __('Back') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Overview Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0">{{ $stats['roles_count'] }}</h3>
                            <p class="mb-0">{{ __('Total Roles') }}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0">{{ $stats['permissions_count'] }}</h3>
                            <p class="mb-0">{{ __('Total Permissions') }}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-key fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0">{{ $stats['groups_count'] }}</h3>
                            <p class="mb-0">{{ __('Permission Groups') }}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-layer-group fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0">{{ $stats['recent_changes'] }}</h3>
                            <p class="mb-0">{{ __('Recent Changes') }}</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Permissions by Group Chart -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ __('Permissions by Group') }}</h5>
                </div>
                <div class="card-body">
                    <canvas id="permissionsByGroupChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- Most Active Roles -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ __('Most Active Roles') }}</h5>
                    <small class="text-muted">{{ __('Based on permission changes') }}</small>
                </div>
                <div class="card-body">
                    @forelse($stats['most_active_roles'] as $roleLog)
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-3">
                                <i class="fas fa-shield-alt text-white"></i>
                            </div>
                            <div>
                                <h6 class="mb-0">{{ $roleLog->role->display_name ?? $roleLog->role->name }}</h6>
                                <small class="text-muted">{{ $roleLog->changes_count }} {{ __('changes') }}</small>
                            </div>
                        </div>
                        <div>
                            <span class="badge bg-info">{{ $roleLog->changes_count }}</span>
                        </div>
                    </div>
                    @empty
                    <div class="text-center py-4">
                        <i class="fas fa-info-circle text-muted fa-2x mb-2"></i>
                        <p class="text-muted">{{ __('No activity recorded yet') }}</p>
                    </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Statistics -->
    <div class="row">
        <!-- Permission Groups Details -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ __('Permission Groups Details') }}</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ __('Group') }}</th>
                                    <th>{{ __('Permissions') }}</th>
                                    <th>{{ __('Usage') }}</th>
                                    <th>{{ __('Status') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($stats['permissions_by_group'] as $group)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-{{ $group->icon ?? 'cog' }} text-primary me-2"></i>
                                            <div>
                                                <h6 class="mb-0">{{ $group->display_name }}</h6>
                                                <small class="text-muted">{{ $group->description }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ $group->permissions_count }}</span>
                                    </td>
                                    <td>
                                        @php
                                            $usage = $group->permissions_count > 0 ?
                                                round(($group->permissions_count / $stats['permissions_count']) * 100) : 0;
                                        @endphp
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar" role="progressbar"
                                                 style="width: {{ $usage }}%"
                                                 aria-valuenow="{{ $usage }}"
                                                 aria-valuemin="0"
                                                 aria-valuemax="100">
                                                {{ $usage }}%
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        @if($group->is_protected)
                                            <span class="badge bg-warning">{{ __('Protected') }}</span>
                                        @else
                                            <span class="badge bg-success">{{ __('Active') }}</span>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ __('Quick Statistics') }}</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{{ __('Total Changes') }}</span>
                            <strong>{{ $stats['logs_count'] }}</strong>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{{ __('Changes (30 days)') }}</span>
                            <strong>{{ $stats['recent_changes'] }}</strong>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{{ __('Avg Permissions/Role') }}</span>
                            <strong>
                                {{ $stats['roles_count'] > 0 ? round($stats['permissions_count'] / $stats['roles_count'], 1) : 0 }}
                            </strong>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{{ __('Protected Groups') }}</span>
                            <strong>{{ $stats['permissions_by_group']->where('is_protected', true)->count() }}</strong>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Health -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">{{ __('System Health') }}</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>{{ __('Permission Coverage') }}</span>
                            <span class="badge bg-success">{{ __('Good') }}</span>
                        </div>
                        <small class="text-muted">{{ __('All groups have permissions') }}</small>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>{{ __('Role Distribution') }}</span>
                            <span class="badge bg-info">{{ __('Balanced') }}</span>
                        </div>
                        <small class="text-muted">{{ __('Roles have varied permissions') }}</small>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>{{ __('Security Status') }}</span>
                            <span class="badge bg-warning">{{ __('Protected') }}</span>
                        </div>
                        <small class="text-muted">{{ __('Core permissions are protected') }}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Permissions by Group Chart
    const ctx = document.getElementById('permissionsByGroupChart').getContext('2d');
    const groupData = @json($stats['permissions_by_group']);

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: groupData.map(group => group.display_name),
            datasets: [{
                data: groupData.map(group => group.permissions_count),
                backgroundColor: [
                    '#007bff',
                    '#28a745',
                    '#17a2b8',
                    '#ffc107',
                    '#dc3545',
                    '#6f42c1',
                    '#fd7e14',
                    '#20c997'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                        }
                    }
                }
            }
        }
    });
});
</script>
@endpush

@push('styles')
<style>
.avatar-sm {
    width: 40px;
    height: 40px;
}

.progress {
    background-color: #e9ecef;
}
</style>
@endpush
