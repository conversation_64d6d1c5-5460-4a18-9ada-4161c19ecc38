<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            تشخيص الصلاحيات
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium mb-4">معلومات المستخدم الحالي</h3>
                    
                    @auth
                        <div class="space-y-4">
                            <div>
                                <strong>الاسم:</strong> {{ Auth::user()->name }}
                            </div>
                            <div>
                                <strong>البريد الإلكتروني:</strong> {{ Auth::user()->email }}
                            </div>
                            <div>
                                <strong>ID:</strong> {{ Auth::user()->id }}
                            </div>
                            
                            <div>
                                <strong>الأدوار:</strong>
                                @if(Auth::user()->roles->count() > 0)
                                    <ul class="list-disc list-inside ml-4">
                                        @foreach(Auth::user()->roles as $role)
                                            <li>{{ $role->name }} ({{ $role->guard_name }})</li>
                                        @endforeach
                                    </ul>
                                @else
                                    <span class="text-red-500">لا توجد أدوار</span>
                                @endif
                            </div>
                            
                            <div>
                                <strong>الصلاحيات المباشرة:</strong>
                                @if(Auth::user()->permissions->count() > 0)
                                    <ul class="list-disc list-inside ml-4">
                                        @foreach(Auth::user()->permissions as $permission)
                                            <li>{{ $permission->name }} ({{ $permission->guard_name }})</li>
                                        @endforeach
                                    </ul>
                                @else
                                    <span class="text-yellow-500">لا توجد صلاحيات مباشرة</span>
                                @endif
                            </div>
                            
                            <div>
                                <strong>جميع الصلاحيات (مباشرة + من الأدوار):</strong>
                                @if(Auth::user()->getAllPermissions()->count() > 0)
                                    <ul class="list-disc list-inside ml-4">
                                        @foreach(Auth::user()->getAllPermissions() as $permission)
                                            <li>{{ $permission->name }} ({{ $permission->guard_name }})</li>
                                        @endforeach
                                    </ul>
                                @else
                                    <span class="text-red-500">لا توجد صلاحيات</span>
                                @endif
                            </div>
                            
                            <div class="border-t pt-4">
                                <h4 class="font-medium mb-2">اختبار الصلاحيات المحددة:</h4>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <strong>users.create:</strong> 
                                        @can('users.create')
                                            <span class="text-green-500">✅ متاح</span>
                                        @else
                                            <span class="text-red-500">❌ غير متاح</span>
                                        @endcan
                                    </div>
                                    <div>
                                        <strong>roles.create:</strong> 
                                        @can('roles.create')
                                            <span class="text-green-500">✅ متاح</span>
                                        @else
                                            <span class="text-red-500">❌ غير متاح</span>
                                        @endcan
                                    </div>
                                    <div>
                                        <strong>permissions.create:</strong> 
                                        @can('permissions.create')
                                            <span class="text-green-500">✅ متاح</span>
                                        @else
                                            <span class="text-red-500">❌ غير متاح</span>
                                        @endcan
                                    </div>
                                    <div>
                                        <strong>users.view:</strong> 
                                        @can('users.view')
                                            <span class="text-green-500">✅ متاح</span>
                                        @else
                                            <span class="text-red-500">❌ غير متاح</span>
                                        @endcan
                                    </div>
                                </div>
                            </div>
                            
                            <div class="border-t pt-4">
                                <h4 class="font-medium mb-2">روابط الاختبار:</h4>
                                <div class="space-y-2">
                                    @can('users.create')
                                        <a href="{{ route('users.create') }}" class="inline-block bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                                            إنشاء مستخدم
                                        </a>
                                    @else
                                        <span class="inline-block bg-gray-400 text-white px-4 py-2 rounded">إنشاء مستخدم (غير متاح)</span>
                                    @endcan
                                    
                                    @can('roles.create')
                                        <a href="{{ route('roles.create') }}" class="inline-block bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                                            إنشاء دور
                                        </a>
                                    @else
                                        <span class="inline-block bg-gray-400 text-white px-4 py-2 rounded">إنشاء دور (غير متاح)</span>
                                    @endcan
                                    
                                    @can('permissions.create')
                                        <a href="{{ route('permissions.create') }}" class="inline-block bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                                            إنشاء صلاحية
                                        </a>
                                    @else
                                        <span class="inline-block bg-gray-400 text-white px-4 py-2 rounded">إنشاء صلاحية (غير متاح)</span>
                                    @endcan
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="text-red-500">
                            المستخدم غير مسجل دخول
                        </div>
                    @endauth
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
