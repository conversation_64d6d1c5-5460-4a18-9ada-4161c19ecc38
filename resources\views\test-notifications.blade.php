<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>اختبار الإشعارات</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 dark:bg-gray-900">
    <div class="container mx-auto p-8">
        <h1 class="text-2xl font-bold mb-6 text-gray-900 dark:text-white">اختبار الإشعارات</h1>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h2 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">معلومات المستخدم الحالي</h2>
            <p class="text-gray-700 dark:text-gray-300">المستخدم: {{ auth()->user()->name ?? 'غير مسجل الدخول' }}</p>
            <p class="text-gray-700 dark:text-gray-300">ID: {{ auth()->user()->id ?? 'N/A' }}</p>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h2 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">اختبار API</h2>
            <div class="space-y-4">
                <button onclick="testUnreadCount()"
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    اختبار عدد الإشعارات غير المقروءة
                </button>

                <button onclick="testRecentNotifications()"
                        class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    اختبار الإشعارات الحديثة
                </button>

                <button onclick="createTestNotifications()"
                        class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                    إنشاء إشعارات تجريبية
                </button>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">النتائج</h2>
            <pre id="results" class="bg-gray-100 dark:bg-gray-700 p-4 rounded text-sm text-gray-800 dark:text-gray-200 overflow-auto max-h-96"></pre>
        </div>
    </div>

    <script>
        function log(message) {
            const results = document.getElementById('results');
            results.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            results.scrollTop = results.scrollHeight;
        }

        function testUnreadCount() {
            log('اختبار عدد الإشعارات غير المقروءة...');

            fetch('/admin/notifications/unread-count', {
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
            })
            .then(response => {
                log('Response status: ' + response.status);
                if (!response.ok) {
                    throw new Error('HTTP ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                log('عدد الإشعارات غير المقروءة: ' + JSON.stringify(data, null, 2));

                // Test the badge update function
                if (data.count > 0) {
                    log('محاولة إظهار الشارة...');
                    testBadgeUpdate(data.count);
                }
            })
            .catch(error => {
                log('خطأ: ' + error.message);
            });
        }

        function testBadgeUpdate(count) {
            // Create a test badge element
            const testBadge = document.createElement('span');
            testBadge.id = 'test-notification-badge';
            testBadge.className = 'inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full hidden';
            testBadge.textContent = '0';

            // Add it to the page
            const container = document.querySelector('.container');
            container.appendChild(testBadge);

            log('تم إنشاء شارة اختبار');

            // Update the badge
            if (count > 0) {
                testBadge.textContent = count > 99 ? '99+' : count;
                testBadge.classList.remove('hidden');
                log('تم إظهار الشارة مع العدد: ' + testBadge.textContent);
            } else {
                testBadge.classList.add('hidden');
                log('تم إخفاء الشارة');
            }
        }

        function testRecentNotifications() {
            log('اختبار الإشعارات الحديثة...');

            fetch('/admin/notifications/recent', {
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
            })
            .then(response => {
                log('Response status: ' + response.status);
                return response.json();
            })
            .then(data => {
                log('الإشعارات الحديثة: ' + JSON.stringify(data, null, 2));
            })
            .catch(error => {
                log('خطأ: ' + error.message);
            });
        }

        function createTestNotifications() {
            log('إنشاء إشعارات تجريبية...');

            // This would typically be done via a separate endpoint
            log('يجب تشغيل: php artisan notifications:create-test');
        }

        // Auto-test on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('تم تحميل الصفحة - بدء الاختبارات التلقائية');
            testUnreadCount();
        });
    </script>
</body>
</html>
