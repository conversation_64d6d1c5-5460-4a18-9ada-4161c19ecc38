@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Page Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                تعديل الدور: {{ $role->name }}
            </h1>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="py-12">
                <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <form method="POST" action="{{ route('roles.update', $role) }}">
                                @csrf
                                @method('PUT')
        
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <!-- معلومات الدور -->
                                    <div class="space-y-4">
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">معلومات الدور</h3>
        
                                        <!-- اسم الدور -->
                                        <div>
                                            <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">اسم الدور *</label>
                                            <input type="text" name="name" id="name" value="{{ old('name', $role->name) }}" required
                                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400"
                                                   placeholder="مثال: محرر، مدير، مستخدم">
                                            @error('name')
                                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                            @enderror
                                        </div>
        
                                        <!-- Guard Name -->
                                        <div>
                                            <label for="guard_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Guard Name *</label>
                                            <select name="guard_name" id="guard_name" required
                                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400">
                                                <option value="web" {{ old('guard_name', $role->guard_name) == 'web' ? 'selected' : '' }}>Web</option>
                                                <option value="api" {{ old('guard_name', $role->guard_name) == 'api' ? 'selected' : '' }}>API</option>
                                            </select>
                                            @error('guard_name')
                                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                            @enderror
                                        </div>
        
                                        <!-- معلومات إضافية -->
                                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">معلومات الدور</h4>
                                            <dl class="space-y-1 text-sm">
                                                <div class="flex justify-between">
                                                    <dt class="text-gray-500 dark:text-gray-400">تاريخ الإنشاء:</dt>
                                                    <dd class="text-gray-900 dark:text-gray-100">{{ $role->created_at->format('Y-m-d H:i') }}</dd>
                                                </div>
                                                <div class="flex justify-between">
                                                    <dt class="text-gray-500 dark:text-gray-400">آخر تحديث:</dt>
                                                    <dd class="text-gray-900 dark:text-gray-100">{{ $role->updated_at->format('Y-m-d H:i') }}</dd>
                                                </div>
                                                <div class="flex justify-between">
                                                    <dt class="text-gray-500 dark:text-gray-400">عدد المستخدمين:</dt>
                                                    <dd class="text-gray-900 dark:text-gray-100">{{ $role->users->count() }} مستخدم</dd>
                                                </div>
                                                <div class="flex justify-between">
                                                    <dt class="text-gray-500 dark:text-gray-400">عدد الصلاحيات الحالية:</dt>
                                                    <dd class="text-gray-900 dark:text-gray-100">{{ $role->permissions->count() }} صلاحية</dd>
                                                </div>
                                            </dl>
                                        </div>
                                    </div>
        
                                    <!-- الصلاحيات -->
                                    <div class="space-y-4">
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">الصلاحيات *</h3>
        
                                        <div class="max-h-96 overflow-y-auto border border-gray-200 dark:border-gray-600 dark:bg-gray-700 rounded-md p-4">
                                            @if($permissions->count() > 0)
                                                @foreach($permissions as $group => $groupPermissions)
                                                    <div class="mb-4">
                                                        <h4 class="text-sm font-medium text-gray-800 dark:text-gray-200 mb-2 capitalize">
                                                            {{ ucfirst($group) }}
                                                        </h4>
                                                        <div class="space-y-2 ml-4">
                                                            @foreach($groupPermissions as $permission)
                                                                <label class="flex items-center">
                                                                    <input type="checkbox" name="permissions[]" value="{{ $permission->name }}"
                                                                           class="rounded border-gray-300 dark:border-gray-600 dark:bg-gray-700 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400"
                                                                           {{ in_array($permission->name, old('permissions', $role->permissions->pluck('name')->toArray())) ? 'checked' : '' }}>
                                                                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">{{ $permission->name }}</span>
                                                                </label>
                                                            @endforeach
                                                        </div>
                                                    </div>
                                                @endforeach
                                            @else
                                                <p class="text-gray-500 dark:text-gray-400 text-center py-4">لا توجد صلاحيات متاحة</p>
                                            @endif
                                        </div>
        
                                        @error('permissions')
                                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                        @enderror
        
                                        <!-- أزرار التحديد السريع -->
                                        <div class="flex space-x-2 rtl:space-x-reverse">
                                            <button type="button" onclick="selectAll()" class="text-sm bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 px-3 py-1 rounded">
                                                تحديد الكل
                                            </button>
                                            <button type="button" onclick="deselectAll()" class="text-sm bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-3 py-1 rounded">
                                                إلغاء تحديد الكل
                                            </button>
                                            <button type="button" onclick="selectCurrentPermissions()" class="text-sm bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 px-3 py-1 rounded">
                                                استعادة الصلاحيات الحالية
                                            </button>
                                        </div>
                                    </div>
                                </div>
        
                                <!-- أزرار التحكم -->
                                <div class="flex items-center justify-end mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                                    <a href="{{ route('roles.index') }}" class="bg-gray-500 hover:bg-gray-700 dark:bg-gray-600 dark:hover:bg-gray-800 text-white font-bold py-2 px-4 rounded-lg mr-3 transition">
                                        إلغاء
                                    </a>
                                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white font-bold py-2 px-4 rounded-lg transition">
                                        <svg class="w-4 h-4 inline-block mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                                        </svg>
                                        تحديث الدور
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        
            <script>
                // الصلاحيات الحالية للدور
                const currentPermissions = @json($role->permissions->pluck('name')->toArray());
        
                function selectAll() {
                    const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
                    checkboxes.forEach(checkbox => checkbox.checked = true);
                }
        
                function deselectAll() {
                    const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
                    checkboxes.forEach(checkbox => checkbox.checked = false);
                }
        
                function selectCurrentPermissions() {
                    const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = currentPermissions.includes(checkbox.value);
                    });
                }
            </script>
    </div>
</div>
@endsection
