<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PermissionGroup;
use Spatie\Permission\Models\Permission;

class PermissionGroupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء مجموعات الصلاحيات
        $groups = [
            [
                'name' => 'users',
                'display_name' => 'User Management',
                'description' => 'Manage user accounts and profiles',
                'icon' => 'users',
                'sort_order' => 1,
                'is_protected' => true
            ],
            [
                'name' => 'roles',
                'display_name' => 'Role Management',
                'description' => 'Manage user roles and permissions',
                'icon' => 'shield',
                'sort_order' => 2,
                'is_protected' => true
            ],
            [
                'name' => 'permissions',
                'display_name' => 'Permission Management',
                'description' => 'Manage system permissions',
                'icon' => 'key',
                'sort_order' => 3,
                'is_protected' => true
            ],
            [
                'name' => 'dashboard',
                'display_name' => 'Dashboard',
                'description' => 'Dashboard and analytics access',
                'icon' => 'chart-bar',
                'sort_order' => 4,
                'is_protected' => true
            ],
            [
                'name' => 'system',
                'display_name' => 'System Settings',
                'description' => 'System configuration and settings',
                'icon' => 'cog',
                'sort_order' => 5,
                'is_protected' => true
            ]
        ];

        foreach ($groups as $groupData) {
            $group = PermissionGroup::create($groupData);

            // ربط الصلاحيات الموجودة بالمجموعات
            $this->assignPermissionsToGroup($group);
        }
    }

    private function assignPermissionsToGroup(PermissionGroup $group)
    {
        $permissions = Permission::where('name', 'like', $group->name . '.%')->get();

        foreach ($permissions as $permission) {
            $permission->update(['group_id' => $group->id]);
        }
    }
}
