{"Welcome": "Welcome", "Dashboard": "Dashboard", "Logout": "Logout", "Profile": "Profile", "Welcome Back": "Welcome Back", "Please sign in to your account": "Please sign in to your account", "Email": "Email", "Password": "Password", "Remember me": "Remember me", "Forgot your password?": "Forgot your password?", "Log in": "Log in", "Don't have an account?": "Don't have an account?", "Sign up": "Sign up", "Create Account": "Create Account", "Join us today": "Join us today", "Full Name": "Full Name", "Phone Number": "Phone Number", "Gender": "Gender", "Select Gender": "Select Gender", "Male": "Male", "Female": "Female", "Confirm Password": "Confirm Password", "Register": "Register", "Already have an account?": "Already have an account?", "Sign in": "Sign in", "Name": "Name", "Phone": "Phone", "Status": "Status", "Role": "Role", "Roles": "Roles", "Actions": "Actions", "Active": "Active", "Inactive": "Inactive", "Edit": "Edit", "Delete": "Delete", "View": "View", "Save": "Save", "Cancel": "Cancel", "Close": "Close", "Search": "Search", "Filter": "Filter", "All": "All", "Yes": "Yes", "No": "No", "Created": "Created", "Updated": "Updated", "Date": "Date", "Time": "Time", "Users": "Users", "User Management": "User Management", "Add New User": "Add New User", "Edit User": "Edit User", "View User": "View User", "User Details": "User Details", "No users found": "No users found", "Role Management": "Role Management", "Add New Role": "Add New Role", "Edit Role": "Edit Role", "View Role": "View Role", "Role Details": "Role Details", "No roles found": "No roles found", "Permissions": "Permissions", "Permission Management": "Permission Management", "Add New Permission": "Add New Permission", "Edit Permission": "Edit Permission", "View Permission": "View Permission", "Permission Details": "Permission Details", "No permissions found": "No permissions found", "Guard Name": "Guard Name", "Description": "Description", "Created At": "Created At", "Updated At": "Updated At", "Last Login": "Last Login", "Never logged in": "Never logged in", "Avatar": "Avatar", "User Count": "User Count", "Permission Count": "Permission Count", "Total Users": "Total Users", "Active Users": "Active Users", "Inactive Users": "Inactive Users", "Total Roles": "Total Roles", "Total Permissions": "Total Permissions", "Recent Activities": "Recent Activities", "New Users": "New Users", "Login Statistics": "Login Statistics", "Users by Role": "Users by Role", "Quick Stats": "Quick Stats", "System Overview": "System Overview", "Welcome to Dashboard": "Welcome to Dashboard", "Profile Information": "Profile Information", "Update your account's profile information and email address.": "Update your account's profile information and email address.", "Your email address is unverified.": "Your email address is unverified.", "Click here to re-send the verification email.": "Click here to re-send the verification email.", "A new verification link has been sent to your email address.": "A new verification link has been sent to your email address.", "Saved.": "Saved.", "Update Password": "Update Password", "Ensure your account is using a long, random password to stay secure.": "Ensure your account is using a long, random password to stay secure.", "Current Password": "Current Password", "New Password": "New Password", "Delete Account": "Delete Account", "Once your account is deleted, all of its resources and data will be permanently deleted. Before deleting your account, please download any data or information that you wish to retain.": "Once your account is deleted, all of its resources and data will be permanently deleted. Before deleting your account, please download any data or information that you wish to retain.", "Are you sure you want to delete your account?": "Are you sure you want to delete your account?", "Once your account is deleted, all of its resources and data will be permanently deleted. Please enter your password to confirm you would like to permanently delete your account.": "Once your account is deleted, all of its resources and data will be permanently deleted. Please enter your password to confirm you would like to permanently delete your account.", "Create": "Create", "Update": "Update", "Back": "Back", "Home": "Home", "Settings": "Settings", "Loading...": "Loading...", "Please wait...": "Please wait...", "Success": "Success", "Error": "Error", "Warning": "Warning", "Info": "Info", "Confirm": "Confirm", "Are you sure?": "Are you sure?", "This action cannot be undone.": "This action cannot be undone.", "Operation completed successfully.": "Operation completed successfully.", "An error occurred.": "An error occurred.", "Invalid input.": "Invalid input.", "Required field.": "Required field.", "Optional": "Optional", "Select an option": "Select an option", "No data available": "No data available", "Show": "Show", "Hide": "<PERSON>de", "Expand": "Expand", "Collapse": "Collapse", "Next": "Next", "Previous": "Previous", "First": "First", "Last": "Last", "Page": "Page", "of": "of", "results": "results", "Showing": "Showing", "to": "to", "entries": "entries", "Basic Information": "Basic Information", "Contact Information": "Contact Information", "Account Settings": "Account <PERSON><PERSON>", "Security": "Security", "Permissions assigned to this role": "Permissions assigned to this role", "Users with this role": "Users with this role", "Role Information": "Role Information", "Permission Information": "Permission Information", "Roles with this permission": "Roles with this permission", "Toggle Status": "Toggle Status", "Activate": "Activate", "Deactivate": "Deactivate", "Assign Role": "Assign Role", "Remove Role": "Remove Role", "Grant Permission": "Grant Permission", "Revoke Permission": "Revoke Permission", "Select All": "Select All", "Deselect All": "Deselect All", "Apply": "Apply", "Reset": "Reset", "Export": "Export", "Import": "Import", "Print": "Print", "Download": "Download", "Upload": "Upload", "Browse": "Browse", "Choose File": "Choose <PERSON>", "No file chosen": "No file chosen", "File uploaded successfully": "File uploaded successfully", "File upload failed": "File upload failed", "Invalid file type": "Invalid file type", "File too large": "File too large", "Maximum file size": "Maximum file size", "Allowed file types": "Allowed file types", "Processing...": "Processing...", "Completed": "Completed", "Failed": "Failed", "Pending": "Pending", "In Progress": "In Progress", "Cancelled": "Cancelled", "Expired": "Expired", "Draft": "Draft", "Published": "Published", "Archived": "Archived", "Deleted": "Deleted", "Restored": "Restored", "Search by name...": "Search by name...", "All Statuses": "All Statuses", "All Roles": "All Roles", "Advanced Filter": "Advanced Filter", "Permission Name": "Permission Name", "Role Count": "Role Count", "Creation Date": "Creation Date", "Role Name": "Role Name", "Users Count": "Users Count", "Permissions Count": "Permissions Count", "State": "State", "Never logged in before": "Never logged in before", "user": "user", "users": "users", "permission": "permission", "permissions": "permissions", "Admin User": "Admin User", "Admin Users": "Admin Users", "role": "role", "roles": "roles", "Quick Links": "Quick Links", "Welcome to the user and role management system. Through this dashboard you can:": "Welcome to the user and role management system. Through this dashboard you can:", "Manage user accounts and their permissions": "Manage user accounts and their permissions", "Create and modify different roles": "Create and modify different roles", "Set permissions for each role": "Set permissions for each role", "Monitor user activity": "Monitor user activity", "Your Account Information": "Your Account Information", "View and manage user accounts": "View and manage user accounts", "Create and modify user roles": "Create and modify user roles", "Define system permissions": "Define system permissions", "No roles assigned": "No roles assigned", "Advanced Permissions": "Advanced Permissions", "Advanced permissions management with detailed control": "Advanced permissions management with detailed control", "New Roles": "New Roles", "Permissions Groups": "Permissions Groups", "All Permissions": "All Permissions", "Search in roles...": "Search in roles...", "Role List": "Role List", "View Details": "View Details", "Edit Permissions": "Edit Permissions", "Quick Settings": "Quick Settings", "Change History": "Change History", "Admin Group": "Admin Group", "Editor Group": "Editor Group", "Viewer Group": "Viewer Group", "Start by creating a new role": "Start by creating a new role", "From Role": "From Role", "To Role": "To Role", "Select source role": "Select source role", "Select target role": "Select target role", "Role details and permissions overview": "Role details and permissions overview", "Permission Templates": "Permission Templates", "Quick Actions": "Quick Actions", "Permissions by Module": "Permissions by <PERSON><PERSON><PERSON>", "No permissions granted in this module": "No permissions granted in this module", "No permission groups found": "No permission groups found", "Please create permission groups first": "Please create permission groups first", "Users with this Role": "Users with this Role", "Joined": "Joined", "View All Users": "View All Users", "Apply Admin Template": "Apply <PERSON><PERSON>", "Apply Editor Template": "Apply Editor Template", "Apply Viewer Template": "Apply Viewer Template", "Copy from Another Role": "<PERSON><PERSON> from Another Role", "Copy Permissions From": "Copy Permissions From", "Source Role": "Source Role", "Select role to copy from": "Select role to copy from", "This will replace all current permissions for this role.": "This will replace all current permissions for this role.", "Copy Permissions": "Copy Permissions", "Apply admin permissions? This will grant all permissions.": "Apply admin permissions? This will grant all permissions.", "Companies": "Companies", "Company Management": "Company Management", "Manage companies and their profiles": "Manage companies and their profiles", "Add New Company": "Add New Company", "Company": "Company", "Owner": "Owner", "Commercial Register": "Commercial Register", "Rating": "Rating", "Workers": "Workers", "Search by name, register, or user...": "Search by name, register, or user...", "Sort By": "Sort By", "Company Name": "Company Name", "Workers Count": "Workers Count", "Total: :count companies": "Total: :count companies", "No companies found": "No companies found", "Get started by creating a new company.": "Get started by creating a new company.", "Are you sure you want to delete this company?": "Are you sure you want to delete this company?", "Company created successfully.": "Company created successfully.", "Company updated successfully.": "Company updated successfully.", "Company deleted successfully.": "Company deleted successfully.", "companies.view": "View Companies", "companies.create": "Create Companies", "companies.edit": "Edit Companies", "companies.delete": "Delete Companies", "Create a new company profile": "Create a new company profile", "Back to Companies": "Back to Companies", "Company Information": "Company Information", "Company Owner": "Company Owner", "Select a user": "Select a user", "Join Date": "Join Date", "Company Logo": "Company Logo", "Supported formats: JPEG, PNG, JPG, GIF. Max size: 2MB": "Supported formats: JPEG, PNG, JPG, GIF. Max size: 2MB", "Website": "Website", "Address": "Address", "Bank Information": "Bank Information", "Bank Name": "Bank Name", "Account Number": "Account Number", "IBAN": "IBAN", "Create Company": "Create Company", "Company Details": "Company Details", "Edit Company": "Edit Company", "out of 5": "out of 5", "Owner Information": "Owner Information", "Not provided": "Not provided", "Total Workers": "Total Workers", "Member Since": "Member Since", "Delete Company": "Delete Company", "Clients": "Clients", "Client Management": "Client Management", "Booking Management": "Booking Management", "Worker Management": "Worker Management", "Schedule Management": "Schedule Management", "Rating Management": "Rating Management", "Small": "Small", "Large": "Large", "Villa": "Villa", "Not specified": "Not specified", "Unavailable": "Unavailable", "Housemaid": "<PERSON>maid", "Nurse": "Nurse", "Nanny": "<PERSON><PERSON>", "Cook": "<PERSON>", "Cleaner": "Cleaner", "Elderly Care": "Elderly Care", "Other": "Other", "Hourly": "Hourly", "Refunded": "Refunded", "Vacation": "Vacation", "Worker": "Worker", "Client": "Client", "Manage clients and their profiles": "Manage clients and their profiles", "Add New Client": "Add New Client", "Search by name or email...": "Search by name or email...", "Home Size": "Home Size", "All Sizes": "All Sizes", "Children Count": "Children Count", "Total: :count clients": "Total: :count clients", "Home Info": "Home Info", "Family": "Family", "floors": "floors", "Has Yard": "Has Yard", "people": "people", "children": "children", "No clients found": "No clients found", "Get started by creating a new client.": "Get started by creating a new client.", "Are you sure you want to delete this client?": "Are you sure you want to delete this client?", "Client created successfully.": "Client created successfully.", "Client updated successfully.": "Client updated successfully.", "Client deleted successfully.": "Client deleted successfully.", "Manage workers and their profiles": "Manage workers and their profiles", "Add New Worker": "Add New Worker", "Search by name, nationality...": "Search by name, nationality...", "All Categories": "All Categories", "All Companies": "All Companies", "Total: :count workers": "Total: :count workers", "Rates": "Rates", "years": "years", "No workers found": "No workers found", "Get started by creating a new worker.": "Get started by creating a new worker.", "Are you sure you want to delete this worker?": "Are you sure you want to delete this worker?", "Worker created successfully.": "Worker created successfully.", "Worker updated successfully.": "Worker updated successfully.", "Worker deleted successfully.": "Worker deleted successfully.", "Bookings": "Bookings", "Schedules": "Schedules", "Ratings": "Ratings", "Booking created successfully.": "Booking created successfully.", "Booking updated successfully.": "Booking updated successfully.", "Booking deleted successfully.": "Booking deleted successfully.", "Schedule created successfully.": "Schedule created successfully.", "Schedule updated successfully.": "Schedule updated successfully.", "Schedule deleted successfully.": "Schedule deleted successfully.", "Rating created successfully.": "Rating created successfully.", "Rating updated successfully.": "Rating updated successfully.", "Rating deleted successfully.": "Rating deleted successfully.", "My Bookings": "My Bookings", "Manage your company bookings and reservations": "Manage your company bookings and reservations", "Back to Dashboard": "Back to Dashboard", "Total": "Total", "Client name, booking ID...": "Client name, booking ID...", "All Status": "All Status", "Date From": "Date From", "Booking": "Booking", "Date & Time": "Date & Time", "Amount": "Amount", "Are you sure you want to approve this booking?": "Are you sure you want to approve this booking?", "No bookings match your current filters.": "No bookings match your current filters.", "Reject Booking": "Reject Booking", "Rejection Reason": "Rejection Reason", "Please provide a reason for rejection...": "Please provide a reason for rejection...", "My Workers": "My Workers", "Manage your company workers": "Manage your company workers", "years experience": "years experience", "Get started by adding your first worker.": "Get started by adding your first worker.", "Add a new worker to your company": "Add a new worker to your company", "Back to Workers": "Back to Workers", "Select Nationality": "Select Nationality", "Filipino": "Filipino", "Indonesian": "Indonesian", "Sri Lankan": "Sri Lankan", "Indian": "Indian", "Bangladeshi": "Bangladeshi", "Ethiopian": "Ethiopian", "Kenyan": "Kenyan", "Select Category": "Select Category", "e.g., Arabic, English, Filipino": "e.g., Arabic, English, Filipino", "List the worker's skills and specialties...": "List the worker's skills and specialties...", "Brief description about the worker...": "Brief description about the worker...", "Pricing Information": "Pricing Information", "Documents and Photos": "Documents and Photos", "Worker Photo": "Worker Photo", "JPEG, PNG, JPG (Max: 2MB)": "JPEG, PNG, JPG (Max: 2MB)", "JPEG, PNG, JPG, PDF (Max: 2MB)": "JPEG, PNG, JPG, PDF (Max: 2MB)", "PDF, DOC, DOCX (Max: 5MB)": "PDF, DOC, DOCX (Max: 5MB)", "Worker is active and available for bookings": "Worker is active and available for bookings", "Company Dashboard": "Company Dashboard", "Welcome back": "Welcome back", "Pending Approval": "Pending Approval", "Review now": "Review now", "Recent Bookings": "Recent Bookings", "Recent Ratings": "Recent Ratings", "My Schedules": "My Schedules", "Manage your company schedules and availability": "Manage your company schedules and availability", "From Date": "From Date", "To Date": "To Date", "Clear": "Clear", "Total: :count schedules": "Total: :count schedules", "No schedules found": "No schedules found", "Get started by creating a new schedule.": "Get started by creating a new schedule.", "Are you sure you want to delete this schedule?": "Are you sure you want to delete this schedule?", "My Ratings": "My Ratings", "View and manage your company ratings and reviews": "View and manage your company ratings and reviews", "Total Ratings": "Total Ratings", "Average Rating": "Average Rating", "Pending Responses": "Pending Responses", "Respond to Rating": "Respond to Rating", "Your Response": "Your Response", "Write your response to this rating...": "Write your response to this rating...", "Send Response": "Send Response", "Response sent successfully.": "Response sent successfully.", "Respond": "Respond", "Your Response:": "Your Response:", "Anonymous": "Anonymous", "Client Dashboard": "Client Dashboard", "Welcome back, :name!": "Welcome back, :name!", "New Booking": "New Booking", "Total Bookings": "Total Bookings", "Total Spent": "Total Spent", "No bookings yet": "No bookings yet", "Start by creating your first booking.": "Start by creating your first booking.", "Available Workers": "Available Workers", "Book": "Book", "No workers available at the moment.": "No workers available at the moment.", "Upcoming Bookings": "Upcoming Bookings", "Book a worker for your needs": "Book a worker for your needs", "How to Book a Worker": "How to Book a Worker", "Choose Worker": "Choose <PERSON>", "Select from available workers based on your needs": "Select from available workers based on your needs", "Set Schedule": "Set Schedule", "Choose your preferred dates and booking type": "Choose your preferred dates and booking type", "Wait for Approval": "Wait for Approval", "Company will review and confirm your booking": "Company will review and confirm your booking", "Estimated Cost": "Estimated Cost", "Final cost may vary based on company rates": "Final cost may vary based on company rates", "Add any special requirements or notes...": "Add any special requirements or notes...", "Booking created successfully. You will be notified once the company responds.": "Booking created successfully. You will be notified once the company responds.", "Manage your booking requests": "Manage your booking requests", "Worker name, company...": "Worker name, company...", "Booking #:id": "Booking #:id", "Booking Pending": "Booking Pending", "Your booking is waiting for company approval.": "Your booking is waiting for company approval.", "Your booking has been confirmed by the company.": "Your booking has been confirmed by the company.", "Your booking has been completed successfully.": "Your booking has been completed successfully.", "This booking has been cancelled.": "This booking has been cancelled.", "Please provide a reason for cancellation...": "Please provide a reason for cancellation...", "Confirm Cancellation": "Confirm Cancellation", "Rate Service": "Rate Service", "Book Again": "Book Again", "Currencies": "Currencies", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "Add Currency": "Add <PERSON>cy", "Create Currency": "Create C<PERSON><PERSON>cy", "Edit Currency": "<PERSON>", "Update Currency": "Update C<PERSON><PERSON>cy", "Currency Name": "Currency Name", "Currency Code": "Currency Code", "Currency Symbol": "Currency Symbol", "Exchange Rate": "Exchange Rate", "Symbol Position": "Symbol Position", "Before amount (ر.س100.00)": "Before amount (ر.س100.00)", "After amount (100.00 ر.س)": "After amount (100.00 ر.س)", "Set Default": "<PERSON>", "Set as default currency": "Set as default currency", "3-letter ISO currency code": "3-letter ISO currency code", "Exchange rate relative to base currency": "Exchange rate relative to base currency", "Manage system currencies and exchange rates": "Manage system currencies and exchange rates", "Get started by creating a new currency.": "Get started by creating a new currency.", "No currencies": "No currencies", "Create a new currency for the system": "Create a new currency for the system", "Update currency information": "Update currency information", "Currency created successfully.": "Currency created successfully.", "Currency updated successfully.": "Currency updated successfully.", "Currency deleted successfully.": "Currency deleted successfully.", "Currency set as default successfully.": "Currency set as default successfully.", "Cannot delete the default currency.": "Cannot delete the default currency.", "Are you sure you want to delete this currency?": "Are you sure you want to delete this currency?", "Note": "Note", "In Arabic language, currency symbol will always appear after the amount regardless of this setting": "In Arabic language, currency symbol will always appear after the amount regardless of this setting", "Currency Display Test": "Currency Display Test", "Test currency formatting in different languages": "Test currency formatting in different languages", "Current Language": "Current Language", "Expected Format": "Expected Format", "Arabic Format": "Arabic Format", "English Format": "English Format", "Currency symbol appears after the amount": "Currency symbol appears after the amount", "Currency symbol appears before the amount": "Currency symbol appears before the amount", "Current Currency Settings": "Current <PERSON><PERSON><PERSON>cy <PERSON>s", "No default currency set": "No default currency set", "Back to Currencies": "Back to Currencies", "User Permissions Test": "User Permissions Test", "Check current user permissions and roles": "Check current user permissions and roles", "Current User Information": "Current User Information", "User Roles": "User Roles", "User Permissions": "User Permissions", "Permission Tests": "Permission Tests", "Allowed": "Allowed", "Denied": "Denied", "Test Links": "Test Links", "Test Ratings Page": "Test Ratings Page", "Test Notifications Page": "Test Notifications Page", "Ratings Page (No Permission)": "Ratings Page (No Permission)", "Notifications Page (No Permission)": "Notifications Page (No Permission)", "Client My Ratings": "My Ratings", "View and manage your service ratings": "View and manage your service ratings", "No ratings yet": "No ratings yet", "You haven't rated any services yet.": "You haven't rated any services yet.", "View Your Bookings": "View Your Bookings", "Stay updated with your bookings and services": "Stay updated with your bookings and services", "New": "New", "You don't have any notifications yet.": "You don't have any notifications yet.", "Are you sure you want to delete this notification?": "Are you sure you want to delete this notification?", "Client Permissions Control Demo": "Client Permissions Control Demo", "Control what clients can see and access": "Control what clients can see and access", "Ratings Permission Control": "Ratings Permission Control", "Notifications Permission Control": "Notifications Permission Control", "With ratings.view Permission": "With ratings.view Permission", "Without ratings.view Permission": "Without ratings.view Permission", "With notifications.view Permission": "With notifications.view Permission", "Without notifications.view Permission": "Without notifications.view Permission", "Client sees \"Ratings\" link in navigation": "Client sees \"Ratings\" link in navigation", "Can access /client/ratings page": "Can access /client/ratings page", "Can view their own ratings": "Can view their own ratings", "Can create new ratings": "Can create new ratings", "Can edit existing ratings": "Can edit existing ratings", "No \"Ratings\" link in navigation": "No \"Ratings\" link in navigation", "403 Forbidden when accessing /client/ratings": "403 Forbidden when accessing /client/ratings", "Cannot view ratings page": "Cannot view ratings page", "Cannot create or edit ratings": "Cannot create or edit ratings", "Client sees \"Notifications\" link in navigation": "Client sees \"Notifications\" link in navigation", "Can access /client/notifications page": "Can access /client/notifications page", "Can view their notifications": "Can view their notifications", "Can mark notifications as read": "Can mark notifications as read", "Can delete notifications": "Can delete notifications", "No \"Notifications\" link in navigation": "No \"Notifications\" link in navigation", "403 Forbidden when accessing /client/notifications": "403 Forbidden when accessing /client/notifications", "Cannot view notifications page": "Cannot view notifications page", "Cannot manage notifications": "Cannot manage notifications", "How to Control Client Permissions": "How to Control Client Permissions", "Step-by-Step Guide": "Step-by-Step Guide", "Go to Admin → Users": "Go to Admin → Users", "Find the client you want to control": "Find the client you want to control", "Click \"Edit\" on the user": "Click \"Edit\" on the user", "Scroll to \"Permissions\" section": "Scroll to \"Permissions\" section", "Check/Uncheck desired permissions": "Check/Uncheck desired permissions", "Save changes": "Save changes", "Available Permissions": "Available Permissions", "Current Test User Status": "Current Test User Status", "Current Permissions": "Current Permissions", "What they can access": "What they can access", "Manage Users & Permissions": "Manage Users & Permissions", "Test Current User Permissions": "Test Current User Permissions", "Manage your account settings and preferences": "Manage your account settings and preferences", "Notification Details": "Notification Details", "View notification information": "View notification information", "Back to Notifications": "Back to Notifications", "Message": "Message", "Additional Information": "Additional Information", "Details": "Details", "Received": "Received", "Read": "Read", "Priority": "Priority", "Expires": "Expires", "Mark as Read": "<PERSON> <PERSON>", "Rating Details": "Rating Details", "View your service rating information": "View your service rating information", "Back to Ratings": "Back to Ratings", "Service Information": "Service Information", "workers": "workers", "Worker information not available": "Worker information not available", "Company information not available": "Company information not available", "Your Rating": "Your Rating", "Overall Rating": "Overall Rating", "Your Comment": "Your Comment", "Rating Type": "Rating Type", "Rating Date": "Rating Date", "Booking Information": "Booking Information", "Booking ID": "Booking ID", "Service Date": "Service Date", "Booking Type": "Booking Type", "Total Amount": "Total Amount", "Payment Status": "Payment Status", "Edit Rating": "Edit Rating", "Delete Rating": "Delete Rating", "Are you sure you want to delete this rating?": "Are you sure you want to delete this rating?", "Rating Policy": "Rating Policy", "To maintain credibility and fairness, ratings cannot be modified once submitted. This ensures authentic feedback for all users.": "To maintain credibility and fairness, ratings cannot be modified once submitted. This ensures authentic feedback for all users.", "Have concerns about this rating?": "Have concerns about this rating?", "Contact Support": "Contact Support", "Create New Rating": "Create New Rating", "Rate your service experience": "Rate your service experience", "Rating for Booking": "Rating for Booking", "Select Booking to Rate": "Select Booking to Rate", "Choose a completed booking": "Choose a completed booking", "Select rating type": "Select rating type", "Rate Worker": "Rate Worker", "Rate Company": "Rate Company", "Star Rating": "Star Rating", "Click on stars to rate": "Click on stars to rate", "Share your experience...": "Share your experience...", "Submit Rating": "Submit Rating", "Payment Receipt": "Payment Receipt", "Transaction ID": "Transaction ID", "Back to Payments": "Back to Payments", "Download PDF": "Download PDF", "Official receipt for your service booking": "Official receipt for your service booking", "Customer Information": "Customer Information", "Service Amount": "Service Amount", "Service Fee": "Service Fee", "Payment Information": "Payment Information", "Payment Date": "Payment Date", "Receipt Date": "Receipt Date", "Thank you for using our services!": "Thank you for using our services!", "This is an official receipt generated on": "This is an official receipt generated on", "My Payments": "My Payments", "View your payment history and receipts": "View your payment history and receipts", "Total Paid": "Total Paid", "Pending Payments": "Pending Payments", "Total Transactions": "Total Transactions", "Search by transaction ID, worker, or company...": "Search by transaction ID, worker, or company...", "All Payment Statuses": "All Payment Statuses", "transactions": "transactions", "Payment History": "Payment History", "View Receipt": "View Receipt", "No payments found": "No payments found", "You haven't made any payments yet.": "You haven't made any payments yet.", "Book a Service": "Book a Service", "Payment receipt not found.": "Payment receipt not found.", "Payment not found.": "Payment not found.", "What's New": "What's New", "Discover the latest features and improvements": "Discover the latest features and improvements", "Version 2.1.0": "Version 2.1.0", "Released on June 15, 2024": "Released on June 15, 2024", "Latest": "Latest", "Real-time Booking Tracking": "Real-time Booking Tracking", "Track your bookings in real-time with live updates on worker location and estimated arrival time.": "Track your bookings in real-time with live updates on worker location and estimated arrival time.", "Live worker location tracking": "Live worker location tracking", "Estimated arrival notifications": "Estimated arrival notifications", "Real-time status updates": "Real-time status updates", "In-app Messaging": "In-app Messaging", "Communicate directly with workers through secure in-app messaging for better coordination.": "Communicate directly with workers through secure in-app messaging for better coordination.", "Secure messaging system": "Secure messaging system", "Photo and file sharing": "Photo and file sharing", "Message history": "Message history", "Photo Verification": "Photo Verification", "Workers can now upload before and after photos to verify completed work quality.": "Workers can now upload before and after photos to verify completed work quality.", "Before/after photo uploads": "Before/after photo uploads", "Work verification system": "Work verification system", "Quality assurance": "Quality assurance", "Flexible Rescheduling": "Flexible Rescheduling", "Enhanced": "Enhanced", "Easily reschedule your bookings with improved flexibility and no additional fees.": "Easily reschedule your bookings with improved flexibility and no additional fees.", "One-click rescheduling": "One-click rescheduling", "No additional fees": "No additional fees", "Automatic notifications": "Automatic notifications", "Improvements & Bug Fixes": "Improvements & Bug Fixes", "Performance": "Performance", "Faster page loading times": "Faster page loading times", "Improved mobile responsiveness": "Improved mobile responsiveness", "Optimized database queries": "Optimized database queries", "User Experience": "User Experience", "Enhanced dark mode support": "Enhanced dark mode support", "Better Arabic language support": "Better Arabic language support", "Improved notification system": "Improved notification system", "Coming Soon": "Coming Soon", "Mobile App": "Mobile App", "iOS & Android apps": "iOS & Android apps", "Analytics Dashboard": "Analytics Dashboard", "Detailed insights": "Detailed insights", "AI Assistant": "AI Assistant", "Smart recommendations": "Smart recommendations", "Go Back": "Go Back", "Not rated": "Not rated", "Booking Timeline": "Booking Timeline", "Booking Created": "Booking Created", "Payment Completed": "Payment Completed", "Service Completed": "Service Completed", "Scheduled Service": "Scheduled Service", "Additional Notes": "Additional Notes", "Please allow popups to download the receipt": "Please allow popups to download the receipt", "Company Payments": "Company Payments", "View your company payment history and earnings": "View your company payment history and earnings", "Total Earned": "Total Earned", "Search by transaction ID, worker, or client...": "Search by transaction ID, worker, or client...", "No payment transactions have been processed yet.": "No payment transactions have been processed yet.", "Modify permission settings and view associated roles": "Modify permission settings and view associated roles", "Example: users.create, posts.edit, admin.access": "Example: users.create, posts.edit, admin.access", "Use \"module.action\" pattern like users.create or posts.edit": "Use \"module.action\" pattern like users.create or posts.edit", "No Associated Roles": "No Associated Roles", "This permission is not assigned to any roles yet.": "This permission is not assigned to any roles yet.", "Are you sure you want to delete this permission?": "Are you sure you want to delete this permission?", "Cannot delete permission (assigned to roles)": "Cannot delete permission (assigned to roles)", "Create New Permission": "Create New Permission", "View permission information and associated roles": "View permission information and associated roles", "Usage Statistics": "Usage Statistics"}