<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\PermissionGroup;

class CompanyRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء مجموعة صلاحيات الشركة
        $companyGroup = PermissionGroup::firstOrCreate([
            'name' => 'company_management',
        ], [
            'display_name' => 'Company Management',
            'description' => 'Permissions for company dashboard and operations',
            'icon' => 'building-office',
            'sort_order' => 12,
            'is_protected' => false,
        ]);

        // إنشاء صلاحيات الشركة
        $companyPermissions = [
            // صلاحيات الداشبورد
            'company.dashboard.view' => 'عرض لوحة تحكم الشركة',
            'company.profile.view' => 'عرض ملف الشركة',
            'company.profile.edit' => 'تعديل ملف الشركة',
            
            // صلاحيات العاملات
            'company.workers.view' => 'عرض العاملات',
            'company.workers.create' => 'إضافة عاملات جديدة',
            'company.workers.edit' => 'تعديل بيانات العاملات',
            'company.workers.delete' => 'حذف العاملات',
            'company.workers.manage' => 'إدارة العاملات',
            
            // صلاحيات الحجوزات
            'company.bookings.view' => 'عرض الحجوزات',
            'company.bookings.approve' => 'الموافقة على الحجوزات',
            'company.bookings.reject' => 'رفض الحجوزات',
            'company.bookings.manage' => 'إدارة الحجوزات',
            
            // صلاحيات الجداول الزمنية
            'company.schedules.view' => 'عرض الجداول الزمنية',
            'company.schedules.create' => 'إنشاء جداول زمنية',
            'company.schedules.edit' => 'تعديل الجداول الزمنية',
            'company.schedules.delete' => 'حذف الجداول الزمنية',
            'company.schedules.manage' => 'إدارة الجداول الزمنية',
            
            // صلاحيات التقييمات
            'company.ratings.view' => 'عرض التقييمات',
            'company.ratings.respond' => 'الرد على التقييمات',
            
            // صلاحيات التقارير
            'company.reports.view' => 'عرض تقارير الشركة',
            'company.reports.export' => 'تصدير تقارير الشركة',
            
            // صلاحيات الإحصائيات
            'company.analytics.view' => 'عرض إحصائيات الشركة',
        ];

        // إنشاء الصلاحيات
        foreach ($companyPermissions as $name => $description) {
            Permission::firstOrCreate([
                'name' => $name,
                'guard_name' => 'web'
            ], [
                'group_id' => $companyGroup->id,
            ]);
        }

        // إنشاء دور الشركة
        $companyRole = Role::firstOrCreate([
            'name' => 'company',
            'guard_name' => 'web'
        ]);

        // إعطاء جميع صلاحيات الشركة لدور الشركة
        $companyRole->givePermissionTo(array_keys($companyPermissions));

        $this->command->info('تم إنشاء دور الشركة والصلاحيات بنجاح!');
    }
}
