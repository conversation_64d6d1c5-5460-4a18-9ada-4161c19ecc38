<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام المتقدم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3>اختبار نظام الصلاحيات المتقدم</h3>
                    </div>
                    <div class="card-body">
                        <h5>البيانات المرسلة:</h5>
                        <pre>{{ json_encode(compact('roles', 'groups', 'stats', 'search'), JSO<PERSON>_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</pre>
                        
                        <hr>
                        
                        <h5>الأدوار:</h5>
                        @if(isset($roles) && $roles->count() > 0)
                            <ul class="list-group">
                                @foreach($roles as $role)
                                    <li class="list-group-item">
                                        <strong>{{ $role->display_name ?? $role->name }}</strong>
                                        <span class="badge bg-info">{{ $role->permissions->count() }} صلاحية</span>
                                    </li>
                                @endforeach
                            </ul>
                        @else
                            <p class="text-muted">لا توجد أدوار</p>
                        @endif
                        
                        <hr>
                        
                        <h5>مجموعات الصلاحيات:</h5>
                        @if(isset($groups) && $groups->count() > 0)
                            <ul class="list-group">
                                @foreach($groups as $group)
                                    <li class="list-group-item">
                                        <strong>{{ $group->display_name }}</strong>
                                        <span class="badge bg-success">{{ $group->permissions->count() }} صلاحية</span>
                                        <br>
                                        <small class="text-muted">{{ $group->description }}</small>
                                    </li>
                                @endforeach
                            </ul>
                        @else
                            <p class="text-muted">لا توجد مجموعات صلاحيات</p>
                        @endif
                        
                        <hr>
                        
                        <h5>الإحصائيات:</h5>
                        @if(isset($stats))
                            <ul class="list-group">
                                @foreach($stats as $key => $value)
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>{{ $key }}</span>
                                        <span class="badge bg-primary">{{ $value }}</span>
                                    </li>
                                @endforeach
                            </ul>
                        @else
                            <p class="text-muted">لا توجد إحصائيات</p>
                        @endif
                        
                        <hr>
                        
                        <div class="d-flex gap-2">
                            <a href="{{ route('admin.permissions.advanced.index') }}" class="btn btn-primary">
                                الصفحة الرئيسية
                            </a>
                            <a href="{{ route('dashboard') }}" class="btn btn-secondary">
                                لوحة التحكم
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
