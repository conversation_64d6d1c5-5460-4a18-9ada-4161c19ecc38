@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">

    <!-- Page Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ __('Edit Schedule') }} #{{ $schedule->id }}</h1>
                    <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">{{ __('Update schedule information') }}</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('admin.schedules.index') }}"
                       class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-md transition duration-200">
                        <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        {{ __('Back to Schedules') }}
                    </a>
                    <a href="{{ route('admin.schedules.show', $schedule) }}"
                       class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-200">
                        <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        {{ __('View Schedule') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div class="px-6 py-6">
                <form method="POST" action="{{ route('admin.schedules.update', $schedule) }}" class="space-y-6">
                    @csrf
                    @method('PUT')

                    <!-- Worker Selection -->
                    <div>
                        <label for="worker_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {{ __('Worker') }} <span class="text-red-500">*</span>
                        </label>
                        <select name="worker_id"
                                id="worker_id"
                                required
                                class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('worker_id') border-red-500 @enderror">
                            <option value="">{{ __('Select Worker') }}</option>
                            @foreach($workers as $worker)
                                <option value="{{ $worker->id }}" {{ old('worker_id', $schedule->worker_id) == $worker->id ? 'selected' : '' }}>
                                    {{ $worker->name }} - {{ $worker->category_text }}
                                </option>
                            @endforeach
                        </select>
                        @error('worker_id')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Schedule Type -->
                    <div>
                        <label for="schedule_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {{ __('Schedule Type') }} <span class="text-red-500">*</span>
                        </label>
                        <select name="schedule_type"
                                id="schedule_type"
                                required
                                class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('schedule_type') border-red-500 @enderror">
                            <option value="">{{ __('Select Schedule Type') }}</option>
                            <option value="available" {{ old('schedule_type', $schedule->schedule_type) === 'available' ? 'selected' : '' }}>{{ __('Available') }}</option>
                            <option value="booked" {{ old('schedule_type', $schedule->schedule_type) === 'booked' ? 'selected' : '' }}>{{ __('Booked') }}</option>
                            <option value="vacation" {{ old('schedule_type', $schedule->schedule_type) === 'vacation' ? 'selected' : '' }}>{{ __('Vacation') }}</option>
                        </select>
                        @error('schedule_type')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Date Range -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Start Date & Time') }} <span class="text-red-500">*</span>
                            </label>
                            <input type="datetime-local"
                                   name="start_date"
                                   id="start_date"
                                   value="{{ old('start_date', $schedule->start_date->format('Y-m-d\TH:i')) }}"
                                   required
                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('start_date') border-red-500 @enderror">
                            @error('start_date')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('End Date & Time') }} <span class="text-red-500">*</span>
                            </label>
                            <input type="datetime-local"
                                   name="end_date"
                                   id="end_date"
                                   value="{{ old('end_date', $schedule->end_date->format('Y-m-d\TH:i')) }}"
                                   required
                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('end_date') border-red-500 @enderror">
                            @error('end_date')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Booking Selection (only for booked schedules) -->
                    <div id="booking_selection" class="{{ $schedule->schedule_type === 'booked' ? '' : 'hidden' }}">
                        <label for="booking_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {{ __('Related Booking') }}
                        </label>
                        <select name="booking_id"
                                id="booking_id"
                                class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('booking_id') border-red-500 @enderror">
                            <option value="">{{ __('Select Booking (Optional)') }}</option>
                            @foreach($bookings as $booking)
                                <option value="{{ $booking->id }}" {{ old('booking_id', $schedule->booking_id) == $booking->id ? 'selected' : '' }}>
                                    #{{ $booking->id }} - {{ $booking->client->user->name }} ({{ $booking->start_date->format('M d, Y') }})
                                </option>
                            @endforeach
                        </select>
                        @error('booking_id')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Notes -->
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {{ __('Notes') }}
                        </label>
                        <textarea name="notes"
                                  id="notes"
                                  rows="4"
                                  placeholder="{{ __('Additional notes about this schedule...') }}"
                                  class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('notes') border-red-500 @enderror">{{ old('notes', $schedule->notes) }}</textarea>
                        @error('notes')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Quick Schedule Templates -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">{{ __('Quick Schedule Templates') }}</h3>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                            <button type="button" onclick="setQuickSchedule('today')"
                                    class="px-3 py-2 text-sm bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-800 dark:text-blue-200 rounded-md transition duration-200">
                                {{ __('Today') }}
                            </button>
                            <button type="button" onclick="setQuickSchedule('tomorrow')"
                                    class="px-3 py-2 text-sm bg-green-100 hover:bg-green-200 dark:bg-green-900 dark:hover:bg-green-800 text-green-800 dark:text-green-200 rounded-md transition duration-200">
                                {{ __('Tomorrow') }}
                            </button>
                            <button type="button" onclick="setQuickSchedule('week')"
                                    class="px-3 py-2 text-sm bg-purple-100 hover:bg-purple-200 dark:bg-purple-900 dark:hover:bg-purple-800 text-purple-800 dark:text-purple-200 rounded-md transition duration-200">
                                {{ __('This Week') }}
                            </button>
                            <button type="button" onclick="setQuickSchedule('month')"
                                    class="px-3 py-2 text-sm bg-yellow-100 hover:bg-yellow-200 dark:bg-yellow-900 dark:hover:bg-yellow-800 text-yellow-800 dark:text-yellow-200 rounded-md transition duration-200">
                                {{ __('This Month') }}
                            </button>
                        </div>
                    </div>

                    <!-- Duration Display -->
                    <div id="duration_display" class="bg-blue-50 dark:bg-blue-900 rounded-lg p-4">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-blue-600 dark:text-blue-400 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-sm font-medium text-blue-800 dark:text-blue-200">
                                {{ __('Duration') }}: <span id="duration_text"></span>
                            </span>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="flex justify-end space-x-3 rtl:space-x-reverse pt-6 border-t border-gray-200 dark:border-gray-700">
                        <a href="{{ route('admin.schedules.show', $schedule) }}"
                           class="inline-flex items-center px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium rounded-md transition duration-200">
                            {{ __('Cancel') }}
                        </a>
                        <button type="submit"
                                class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-200">
                            <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            {{ __('Update Schedule') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const scheduleTypeSelect = document.getElementById('schedule_type');
    const bookingSelection = document.getElementById('booking_selection');
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    const durationDisplay = document.getElementById('duration_display');
    const durationText = document.getElementById('duration_text');

    // Show/hide booking selection based on schedule type
    scheduleTypeSelect.addEventListener('change', function() {
        if (this.value === 'booked') {
            bookingSelection.classList.remove('hidden');
        } else {
            bookingSelection.classList.add('hidden');
        }
    });

    // Calculate and display duration
    function updateDuration() {
        const startDate = new Date(startDateInput.value);
        const endDate = new Date(endDateInput.value);

        if (startDate && endDate && endDate > startDate) {
            const diffTime = Math.abs(endDate - startDate);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            const diffHours = Math.ceil(diffTime / (1000 * 60 * 60));

            let durationString = '';
            if (diffDays >= 1) {
                durationString = `${diffDays} ${diffDays === 1 ? '{{ __("day") }}' : '{{ __("days") }}'}`;
            } else {
                durationString = `${diffHours} ${diffHours === 1 ? '{{ __("hour") }}' : '{{ __("hours") }}'}`;
            }

            durationText.textContent = durationString;
            durationDisplay.classList.remove('hidden');
        } else {
            durationDisplay.classList.add('hidden');
        }
    }

    // Initial duration calculation
    updateDuration();

    startDateInput.addEventListener('change', updateDuration);
    endDateInput.addEventListener('change', updateDuration);
});

function setQuickSchedule(type) {
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    const now = new Date();

    let startDate, endDate;

    switch(type) {
        case 'today':
            startDate = new Date(now);
            startDate.setHours(8, 0, 0, 0);
            endDate = new Date(now);
            endDate.setHours(17, 0, 0, 0);
            break;

        case 'tomorrow':
            startDate = new Date(now);
            startDate.setDate(now.getDate() + 1);
            startDate.setHours(8, 0, 0, 0);
            endDate = new Date(now);
            endDate.setDate(now.getDate() + 1);
            endDate.setHours(17, 0, 0, 0);
            break;

        case 'week':
            startDate = new Date(now);
            startDate.setHours(8, 0, 0, 0);
            endDate = new Date(now);
            endDate.setDate(now.getDate() + 7);
            endDate.setHours(17, 0, 0, 0);
            break;

        case 'month':
            startDate = new Date(now);
            startDate.setHours(8, 0, 0, 0);
            endDate = new Date(now);
            endDate.setMonth(now.getMonth() + 1);
            endDate.setHours(17, 0, 0, 0);
            break;
    }

    startDateInput.value = startDate.toISOString().slice(0, 16);
    endDateInput.value = endDate.toISOString().slice(0, 16);

    // Trigger duration update
    startDateInput.dispatchEvent(new Event('change'));
}
</script>
@endsection
