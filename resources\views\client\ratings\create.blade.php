@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ __('Create New Rating') }}</h1>
                    <p class="mt-2 text-gray-600 dark:text-gray-400">{{ __('Rate your service experience') }}</p>
                </div>
                
                <a href="{{ route('client.ratings.index') }}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                    <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                    {{ __('Back to Ratings') }}
                </a>
            </div>
        </div>

        <!-- Rating Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
            <form action="{{ route('client.ratings.store') }}" method="POST" class="p-8">
                @csrf

                <!-- Booking Selection -->
                @if(request('booking_id'))
                    <input type="hidden" name="booking_id" value="{{ request('booking_id') }}">
                    @php
                        $selectedBooking = \App\Models\Booking::with(['worker', 'company'])->find(request('booking_id'));
                    @endphp
                    
                    @if($selectedBooking)
                        <div class="mb-8 p-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                            <h3 class="text-lg font-medium text-blue-900 dark:text-blue-100 mb-4">{{ __('Rating for Booking') }} #{{ $selectedBooking->id }}</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <dt class="text-sm font-medium text-blue-700 dark:text-blue-300">{{ __('Worker') }}</dt>
                                    <dd class="text-sm text-blue-900 dark:text-blue-100">{{ $selectedBooking->worker->name ?? __('Not specified') }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-blue-700 dark:text-blue-300">{{ __('Company') }}</dt>
                                    <dd class="text-sm text-blue-900 dark:text-blue-100">{{ $selectedBooking->company->company_name ?? __('Not specified') }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-blue-700 dark:text-blue-300">{{ __('Service Date') }}</dt>
                                    <dd class="text-sm text-blue-900 dark:text-blue-100">{{ $selectedBooking->start_date ? $selectedBooking->start_date->format('M d, Y') : __('Not specified') }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-blue-700 dark:text-blue-300">{{ __('Total Amount') }}</dt>
                                    <dd class="text-sm text-blue-900 dark:text-blue-100">${{ number_format($selectedBooking->total_amount ?? 0, 2) }}</dd>
                                </div>
                            </div>
                        </div>
                    @endif
                @else
                    <!-- Booking Selection Dropdown -->
                    <div class="mb-6">
                        <label for="booking_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {{ __('Select Booking to Rate') }}
                        </label>
                        <select name="booking_id" id="booking_id" required
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                            <option value="">{{ __('Choose a completed booking') }}</option>
                            @foreach(\App\Models\Booking::with(['worker', 'company'])->where('client_id', auth()->user()->client->id ?? 0)->where('status', 'completed')->whereDoesntHave('ratings', function($q) { $q->where('rater_id', auth()->id()); })->get() as $booking)
                                <option value="{{ $booking->id }}">
                                    Booking #{{ $booking->id }} - {{ $booking->worker->name ?? 'Unknown' }} ({{ $booking->start_date ? $booking->start_date->format('M d, Y') : 'No date' }})
                                </option>
                            @endforeach
                        </select>
                        @error('booking_id')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>
                @endif

                <!-- Rating Type -->
                <div class="mb-6">
                    <label for="rating_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {{ __('Rating Type') }}
                    </label>
                    <select name="rating_type" id="rating_type" required
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        <option value="">{{ __('Select rating type') }}</option>
                        <option value="worker">{{ __('Rate Worker') }}</option>
                        <option value="company">{{ __('Rate Company') }}</option>
                        <option value="service">{{ __('Rate Service') }}</option>
                    </select>
                    @error('rating_type')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Star Rating -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {{ __('Star Rating') }}
                    </label>
                    <div class="flex items-center space-x-1">
                        @for($i = 1; $i <= 5; $i++)
                            <button type="button" 
                                    class="star-rating text-gray-300 hover:text-yellow-400 focus:outline-none focus:text-yellow-400 transition-colors duration-200"
                                    data-rating="{{ $i }}">
                                <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                            </button>
                        @endfor
                    </div>
                    <input type="hidden" name="star_rating" id="star_rating" required>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ __('Click on stars to rate') }}</p>
                    @error('star_rating')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Comment -->
                <div class="mb-6">
                    <label for="comment" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {{ __('Comment') }} <span class="text-gray-500">({{ __('Optional') }})</span>
                    </label>
                    <textarea name="comment" id="comment" rows="4" 
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                              placeholder="{{ __('Share your experience...') }}">{{ old('comment') }}</textarea>
                    @error('comment')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Rating Date -->
                <div class="mb-8">
                    <label for="rating_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {{ __('Rating Date') }}
                    </label>
                    <input type="datetime-local" name="rating_date" id="rating_date" 
                           value="{{ old('rating_date', now()->format('Y-m-d\TH:i')) }}"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                    @error('rating_date')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Submit Button -->
                <div class="flex items-center justify-end space-x-4">
                    <a href="{{ route('client.ratings.index') }}" 
                       class="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 font-medium transition-colors duration-200">
                        {{ __('Cancel') }}
                    </a>
                    
                    <button type="submit" 
                            class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors duration-200">
                        <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        {{ __('Submit Rating') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const stars = document.querySelectorAll('.star-rating');
    const ratingInput = document.getElementById('star_rating');
    let currentRating = 0;

    stars.forEach((star, index) => {
        star.addEventListener('click', function() {
            currentRating = index + 1;
            ratingInput.value = currentRating;
            updateStars();
        });

        star.addEventListener('mouseenter', function() {
            highlightStars(index + 1);
        });
    });

    document.querySelector('.flex.items-center.space-x-1').addEventListener('mouseleave', function() {
        updateStars();
    });

    function updateStars() {
        stars.forEach((star, index) => {
            if (index < currentRating) {
                star.classList.remove('text-gray-300');
                star.classList.add('text-yellow-400');
            } else {
                star.classList.remove('text-yellow-400');
                star.classList.add('text-gray-300');
            }
        });
    }

    function highlightStars(rating) {
        stars.forEach((star, index) => {
            if (index < rating) {
                star.classList.remove('text-gray-300');
                star.classList.add('text-yellow-400');
            } else {
                star.classList.remove('text-yellow-400');
                star.classList.add('text-gray-300');
            }
        });
    }
});
</script>
@endsection
