<?php

namespace App\Http\Controllers\Company;

use App\Http\Controllers\Controller;
use App\Models\Worker;
use App\Models\Nationality;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class WorkerController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (!auth()->user()->hasRole('company')) {
                abort(403, 'Access denied. Company role required.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of company workers.
     */
    public function index(Request $request)
    {
        $company = auth()->user()->company;

        if (!$company) {
            return redirect()->route('company.dashboard')->with('error', __('Company not found.'));
        }

        $query = Worker::where('company_id', $company->id);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('nationality', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        // Filter by nationality
        if ($request->filled('nationality')) {
            $query->where('nationality', $request->nationality);
        }

        // Sort functionality - default sort
        $query->orderBy('created_at', 'desc');

        $workers = $query->paginate(15);

        // Get categories for filter
        $categories = [
            'housemaid' => __('Housemaid'),
            'nurse' => __('Nurse'),
            'nanny' => __('Nanny'),
            'cook' => __('Cook'),
            'cleaner' => __('Cleaner'),
            'elderly_care' => __('Elderly Care'),
            'driver' => __('Driver'),
            'gardener' => __('Gardener')
        ];

        // Get nationalities for filter
        $nationalities = Nationality::active()->ordered()->get();

        return view('company.workers.index', compact('workers', 'categories', 'nationalities'));
    }

    /**
     * Show the form for creating a new worker.
     */
    public function create()
    {
        $company = auth()->user()->company;

        if (!$company) {
            return redirect()->route('company.dashboard')->with('error', __('Company not found.'));
        }

        // Get nationalities for dropdown
        $nationalities = Nationality::active()->ordered()->get();

        return view('company.workers.create', compact('company', 'nationalities'));
    }

    /**
     * Store a newly created worker.
     */
    public function store(Request $request)
    {
        $company = auth()->user()->company;

        if (!$company) {
            return redirect()->route('company.dashboard')->with('error', __('Company not found.'));
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'nationality' => 'required|string|max:100',
            'age' => 'required|integer|min:18|max:65',
            'category' => 'required|string|max:100',
            'experience_years' => 'required|integer|min:0|max:50',
            'languages' => 'nullable|array',
            'skills' => 'nullable|array',
            'description' => 'nullable|string',
            'hourly_rate' => 'required|numeric|min:0',
            'monthly_rate' => 'required|numeric|min:0',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'passport_photo' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:2048',
            'cv_file' => 'nullable|file|mimes:pdf,doc,docx|max:5120',
            'is_active' => 'boolean',
        ]);

        try {
            // Handle file uploads
            if ($request->hasFile('photo')) {
                $validated['image'] = $request->file('photo')->store('workers/photos', 'public');
            }

            if ($request->hasFile('passport_photo')) {
                $validated['passport_photo'] = $request->file('passport_photo')->store('workers/passports', 'public');
            }

            if ($request->hasFile('cv_file')) {
                $validated['cv_file'] = $request->file('cv_file')->store('workers/cvs', 'public');
            }

            // Handle array fields - ensure they are not empty arrays
            $validated['skills'] = !empty($validated['skills']) ? $validated['skills'] : null;
            $validated['languages'] = !empty($validated['languages']) ? $validated['languages'] : null;

            // Set additional required fields
            $validated['company_id'] = $company->id;
            $validated['is_active'] = $request->has('is_active');
            $validated['daily_rate'] = $validated['hourly_rate'] * 8; // 8 hours per day
            $validated['yearly_rate'] = $validated['monthly_rate'] * 12; // 12 months per year
            $validated['status'] = 'available';
            $validated['added_date'] = now()->toDateString();

            // Handle experience_years - save as array for compatibility
            $experienceYears = $validated['experience_years'] ?? 0;
            $validated['experience'] = [$experienceYears]; // Save as array

            // Remove experience_years as it's not in the database
            unset($validated['experience_years']);

            Worker::create($validated);

            return redirect()->route('company.workers.index')
                           ->with('success', __('Worker added successfully.'));

        } catch (\Exception $e) {
            // Clean up uploaded files if something goes wrong
            if (isset($validated['image'])) {
                Storage::disk('public')->delete($validated['image']);
            }
            if (isset($validated['passport_photo'])) {
                Storage::disk('public')->delete($validated['passport_photo']);
            }
            if (isset($validated['cv_file'])) {
                Storage::disk('public')->delete($validated['cv_file']);
            }

            return back()->withInput()->withErrors(['error' => __('Failed to add worker. Please try again.') . ' Error: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the specified worker.
     */
    public function show(Worker $worker)
    {
        $company = auth()->user()->company;

        if (!$company || $worker->company_id !== $company->id) {
            abort(403, 'Access denied.');
        }

        // Get worker statistics
        $stats = [
            'total_bookings' => $worker->bookings()->count(),
            'completed_bookings' => $worker->bookings()->where('status', 'completed')->count(),
            'average_rating' => $worker->ratings()->avg('star_rating'),
            'total_ratings' => $worker->ratings()->count(),
        ];

        // Get recent bookings
        $recentBookings = $worker->bookings()
                                ->with('client.user')
                                ->orderBy('created_at', 'desc')
                                ->limit(5)
                                ->get();

        // Get recent ratings
        $recentRatings = $worker->ratings()
                               ->with('rater')
                               ->orderBy('created_at', 'desc')
                               ->limit(5)
                               ->get();

        return view('company.workers.show', compact('worker', 'stats', 'recentBookings', 'recentRatings'));
    }

    /**
     * Show the form for editing the specified worker.
     */
    public function edit(Worker $worker)
    {
        $company = auth()->user()->company;

        if (!$company || $worker->company_id !== $company->id) {
            abort(403, 'Access denied.');
        }

        // Get nationalities for dropdown
        $nationalities = Nationality::active()->ordered()->get();

        return view('company.workers.edit', compact('worker', 'company', 'nationalities'));
    }

    /**
     * Update the specified worker.
     */
    public function update(Request $request, Worker $worker)
    {
        $company = auth()->user()->company;

        if (!$company || $worker->company_id !== $company->id) {
            abort(403, 'Access denied.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'nationality' => 'required|string|max:100',
            'age' => 'required|integer|min:18|max:65',
            'category' => 'required|string|max:100',
            'experience_years' => 'required|integer|min:0|max:50',
            'languages' => 'nullable|array',
            'skills' => 'nullable|array',
            'description' => 'nullable|string',
            'hourly_rate' => 'required|numeric|min:0',
            'monthly_rate' => 'required|numeric|min:0',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'passport_photo' => 'nullable|image|mimes:jpeg,png,jpg,pdf|max:2048',
            'cv_file' => 'nullable|file|mimes:pdf,doc,docx|max:5120',
            'is_active' => 'boolean',
        ]);

        try {
            // Handle file uploads
            if ($request->hasFile('photo')) {
                // Delete old photo
                if ($worker->photo) {
                    Storage::disk('public')->delete($worker->photo);
                }
                $validated['photo'] = $request->file('photo')->store('workers/photos', 'public');
            }

            if ($request->hasFile('passport_photo')) {
                // Delete old passport photo
                if ($worker->passport_photo) {
                    Storage::disk('public')->delete($worker->passport_photo);
                }
                $validated['passport_photo'] = $request->file('passport_photo')->store('workers/passports', 'public');
            }

            if ($request->hasFile('cv_file')) {
                // Delete old CV
                if ($worker->cv_file) {
                    Storage::disk('public')->delete($worker->cv_file);
                }
                $validated['cv_file'] = $request->file('cv_file')->store('workers/cvs', 'public');
            }

            // Handle array fields - ensure they are not empty arrays
            $validated['skills'] = !empty($validated['skills']) ? $validated['skills'] : null;
            $validated['languages'] = !empty($validated['languages']) ? $validated['languages'] : null;

            // Handle experience_years - save as array for compatibility
            $experienceYears = $validated['experience_years'] ?? 0;
            $validated['experience'] = [$experienceYears]; // Save as array
            unset($validated['experience_years']); // Remove as it's not in database

            // Handle is_active checkbox
            $validated['is_active'] = $request->has('is_active');

            // Calculate daily and yearly rates
            $validated['daily_rate'] = $validated['hourly_rate'] * 8; // 8 hours per day
            $validated['yearly_rate'] = $validated['monthly_rate'] * 12; // 12 months per year

            $worker->update($validated);

            return redirect()->route('company.workers.show', $worker)
                           ->with('success', __('Worker updated successfully.'));

        } catch (\Exception $e) {
            return back()->withInput()->withErrors(['error' => __('Failed to update worker. Please try again.')]);
        }
    }

    /**
     * Remove the specified worker.
     */
    public function destroy(Worker $worker)
    {
        $company = auth()->user()->company;

        if (!$company || $worker->company_id !== $company->id) {
            abort(403, 'Access denied.');
        }

        try {
            // Check if worker has active bookings
            $activeBookings = $worker->bookings()
                                   ->whereIn('status', ['pending', 'confirmed'])
                                   ->count();

            if ($activeBookings > 0) {
                return back()->withErrors(['error' => __('Cannot delete worker with active bookings.')]);
            }

            // Delete associated files
            if ($worker->photo) {
                Storage::disk('public')->delete($worker->photo);
            }
            if ($worker->passport_photo) {
                Storage::disk('public')->delete($worker->passport_photo);
            }
            if ($worker->cv_file) {
                Storage::disk('public')->delete($worker->cv_file);
            }

            $worker->delete();

            return redirect()->route('company.workers.index')
                           ->with('success', __('Worker deleted successfully.'));

        } catch (\Exception $e) {
            return back()->withErrors(['error' => __('Failed to delete worker. Please try again.')]);
        }
    }
}
