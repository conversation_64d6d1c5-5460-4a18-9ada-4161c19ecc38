<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Client;
use App\Models\Worker;
use App\Models\Company;
use App\Services\NotificationService;
use Illuminate\Http\Request;

class BookingController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->middleware('auth');
        $this->notificationService = $notificationService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('bookings.view');

        $query = Booking::with(['client.user', 'worker', 'company']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->whereHas('client.user', function ($userQuery) use ($search) {
                    $userQuery->where('name', 'like', "%{$search}%")
                             ->orWhere('email', 'like', "%{$search}%");
                })
                ->orWhereHas('worker', function ($workerQuery) use ($search) {
                    $workerQuery->where('name', 'like', "%{$search}%");
                })
                ->orWhereHas('company', function ($companyQuery) use ($search) {
                    $companyQuery->where('company_name', 'like', "%{$search}%");
                });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by payment status
        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        // Filter by booking type
        if ($request->filled('booking_type')) {
            $query->where('booking_type', $request->booking_type);
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $bookings = $query->paginate(15)->withQueryString();

        return view('admin.bookings.index', compact('bookings'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('bookings.create');

        $clients = Client::with('user')->where('is_active', true)->get();
        $workers = Worker::where('status', 'available')->where('is_active', true)->get();
        $companies = Company::where('is_active', true)->get();

        return view('admin.bookings.create', compact('clients', 'workers', 'companies'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('bookings.create');

        $validated = $request->validate([
            'client_id' => 'required|exists:clients,id',
            'worker_id' => 'required|exists:workers,id',
            'company_id' => 'required|exists:companies,id',
            'booking_type' => 'required|in:hourly,daily,monthly,yearly',
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after:start_date',
            'total_amount' => 'required|numeric|min:0',
            'payment_method' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
        ]);

        $validated['payment_status'] = 'pending';
        $validated['status'] = 'pending';

        $booking = Booking::create($validated);
        $booking->load(['client.user', 'worker', 'company.user']);

        // إرسال الإشعارات
        $this->sendBookingNotifications($booking, 'created');

        return redirect()->route('admin.bookings.index')
                        ->with('success', __('Booking created successfully.'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Booking $booking)
    {
        $this->authorize('bookings.view');

        $booking->load(['client.user', 'worker', 'company']);

        return view('admin.bookings.show', compact('booking'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Booking $booking)
    {
        $this->authorize('bookings.edit');

        $clients = Client::with('user')->where('is_active', true)->get();
        $workers = Worker::where('is_active', true)->get();
        $companies = Company::where('is_active', true)->get();

        return view('admin.bookings.edit', compact('booking', 'clients', 'workers', 'companies'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Booking $booking)
    {
        $this->authorize('bookings.edit');

        $validated = $request->validate([
            'client_id' => 'required|exists:clients,id',
            'worker_id' => 'required|exists:workers,id',
            'company_id' => 'required|exists:companies,id',
            'booking_type' => 'required|in:hourly,daily,monthly,yearly',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'total_amount' => 'required|numeric|min:0',
            'payment_status' => 'required|in:pending,paid,failed,refunded',
            'payment_method' => 'nullable|string|max:255',
            'transaction_id' => 'nullable|string|max:255',
            'status' => 'required|in:pending,confirmed,cancelled,completed',
            'notes' => 'nullable|string',
        ]);

        $oldStatus = $booking->status;
        $booking->update($validated);
        $booking->load(['client.user', 'worker', 'company.user']);

        // إرسال الإشعارات إذا تغيرت الحالة
        if ($oldStatus !== $validated['status']) {
            $this->sendBookingNotifications($booking, 'status_changed', $oldStatus);
        }

        return redirect()->route('admin.bookings.index')
                        ->with('success', __('Booking updated successfully.'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Booking $booking)
    {
        $this->authorize('bookings.delete');

        $booking->delete();

        return redirect()->route('admin.bookings.index')
                        ->with('success', __('Booking deleted successfully.'));
    }

    /**
     * إرسال الإشعارات المتعلقة بالحجز
     */
    private function sendBookingNotifications($booking, $action, $oldStatus = null)
    {
        switch ($action) {
            case 'created':
                $this->sendBookingCreatedNotifications($booking);
                break;
            case 'status_changed':
                $this->sendBookingStatusChangedNotifications($booking, $oldStatus);
                break;
        }
    }

    /**
     * إرسال إشعارات إنشاء الحجز
     */
    private function sendBookingCreatedNotifications($booking)
    {
        // إشعار للعميل
        $this->notificationService->send($booking->client->user, [
            'type' => 'booking_created',
            'title' => __('Booking Created Successfully'),
            'message' => __('Your booking #:id has been created and is pending confirmation. Worker: :worker, Amount: $:amount', [
                'id' => $booking->id,
                'worker' => $booking->worker->name,
                'amount' => number_format($booking->total_amount, 2)
            ]),
            'notifiable_type' => get_class($booking),
            'notifiable_id' => $booking->id,
            'priority' => 'medium',
            'action_url' => route('admin.bookings.show', $booking),
            'action_text' => __('View Booking Details'),
            'icon' => 'calendar',
            'color' => 'blue',
            'data' => [
                'booking_id' => $booking->id,
                'total_amount' => $booking->total_amount,
                'worker_name' => $booking->worker->name,
                'start_date' => $booking->start_date->format('Y-m-d H:i'),
                'end_date' => $booking->end_date->format('Y-m-d H:i'),
            ],
        ]);

        // إشعار للشركة
        $this->notificationService->send($booking->company->user, [
            'type' => 'booking_created',
            'title' => __('New Booking Received'),
            'message' => __('A new booking #:id has been created for worker :worker. Client: :client, Amount: $:amount', [
                'id' => $booking->id,
                'worker' => $booking->worker->name,
                'client' => $booking->client->user->name,
                'amount' => number_format($booking->total_amount, 2)
            ]),
            'notifiable_type' => get_class($booking),
            'notifiable_id' => $booking->id,
            'priority' => 'high',
            'action_url' => route('admin.bookings.show', $booking),
            'action_text' => __('View Booking Details'),
            'icon' => 'briefcase',
            'color' => 'green',
            'data' => [
                'booking_id' => $booking->id,
                'client_name' => $booking->client->user->name,
                'worker_name' => $booking->worker->name,
                'total_amount' => $booking->total_amount,
                'start_date' => $booking->start_date->format('Y-m-d H:i'),
                'end_date' => $booking->end_date->format('Y-m-d H:i'),
            ],
        ]);

        // إشعار للعامل (إذا كان له حساب مستخدم)
        // ملاحظة: العمال قد لا يكون لديهم حسابات مستخدمين، لكن إذا كان لديهم فسنرسل لهم إشعار
        // يمكن تطوير هذا لاحقاً لإرسال إشعارات عبر SMS أو طرق أخرى

        // إشعار للأدمن (جميع المستخدمين الذين لديهم صلاحية عرض الحجوزات)
        $adminUsers = \App\Models\User::permission('bookings.view')->get();
        foreach ($adminUsers as $admin) {
            // تجنب إرسال إشعار مكرر للعميل أو الشركة إذا كانوا أدمن أيضاً
            if ($admin->id !== $booking->client->user->id && $admin->id !== $booking->company->user->id) {
                $this->notificationService->send($admin, [
                    'type' => 'booking_created',
                    'title' => __('New Booking in System'),
                    'message' => __('Booking #:id created. Client: :client, Worker: :worker, Amount: $:amount', [
                        'id' => $booking->id,
                        'client' => $booking->client->user->name,
                        'worker' => $booking->worker->name,
                        'amount' => number_format($booking->total_amount, 2)
                    ]),
                    'notifiable_type' => get_class($booking),
                    'notifiable_id' => $booking->id,
                    'priority' => 'medium',
                    'action_url' => route('admin.bookings.show', $booking),
                    'action_text' => __('View Booking Details'),
                    'icon' => 'clipboard-document-list',
                    'color' => 'purple',
                    'data' => [
                        'booking_id' => $booking->id,
                        'client_name' => $booking->client->user->name,
                        'worker_name' => $booking->worker->name,
                        'company_name' => $booking->company->company_name,
                        'total_amount' => $booking->total_amount,
                    ],
                ]);
            }
        }
    }

    /**
     * إرسال إشعارات تغيير حالة الحجز
     */
    private function sendBookingStatusChangedNotifications($booking, $oldStatus)
    {
        $statusMessages = [
            'pending' => __('Pending Confirmation'),
            'confirmed' => __('Confirmed'),
            'cancelled' => __('Cancelled'),
            'completed' => __('Completed'),
        ];

        $newStatusText = $statusMessages[$booking->status] ?? $booking->status;
        $oldStatusText = $statusMessages[$oldStatus] ?? $oldStatus;

        // إشعار للعميل
        $clientMessage = '';
        $clientPriority = 'medium';
        $clientColor = 'blue';

        switch ($booking->status) {
            case 'confirmed':
                $clientMessage = __('Great news! Your booking #:id has been confirmed. Worker :worker will be available as scheduled.', [
                    'id' => $booking->id,
                    'worker' => $booking->worker->name
                ]);
                $clientPriority = 'high';
                $clientColor = 'green';
                break;
            case 'cancelled':
                $clientMessage = __('Your booking #:id has been cancelled. Please contact us if you have any questions.', [
                    'id' => $booking->id
                ]);
                $clientPriority = 'high';
                $clientColor = 'red';
                break;
            case 'completed':
                $clientMessage = __('Your booking #:id has been completed. Thank you for using our service!', [
                    'id' => $booking->id
                ]);
                $clientPriority = 'medium';
                $clientColor = 'blue';
                break;
            default:
                $clientMessage = __('Your booking #:id status has been updated to: :status', [
                    'id' => $booking->id,
                    'status' => $newStatusText
                ]);
        }

        $this->notificationService->send($booking->client->user, [
            'type' => 'booking_' . $booking->status,
            'title' => __('Booking Status Updated'),
            'message' => $clientMessage,
            'notifiable_type' => get_class($booking),
            'notifiable_id' => $booking->id,
            'priority' => $clientPriority,
            'action_url' => route('admin.bookings.show', $booking),
            'action_text' => __('View Booking Details'),
            'icon' => 'bell',
            'color' => $clientColor,
            'data' => [
                'booking_id' => $booking->id,
                'old_status' => $oldStatus,
                'new_status' => $booking->status,
                'worker_name' => $booking->worker->name,
            ],
        ]);

        // إشعار للشركة
        $companyMessage = __('Booking #:id status changed from :old_status to :new_status. Client: :client, Worker: :worker', [
            'id' => $booking->id,
            'old_status' => $oldStatusText,
            'new_status' => $newStatusText,
            'client' => $booking->client->user->name,
            'worker' => $booking->worker->name
        ]);

        $this->notificationService->send($booking->company->user, [
            'type' => 'booking_' . $booking->status,
            'title' => __('Booking Status Updated'),
            'message' => $companyMessage,
            'notifiable_type' => get_class($booking),
            'notifiable_id' => $booking->id,
            'priority' => 'medium',
            'action_url' => route('admin.bookings.show', $booking),
            'action_text' => __('View Booking Details'),
            'icon' => 'arrow-path',
            'color' => 'yellow',
            'data' => [
                'booking_id' => $booking->id,
                'old_status' => $oldStatus,
                'new_status' => $booking->status,
                'client_name' => $booking->client->user->name,
                'worker_name' => $booking->worker->name,
            ],
        ]);

        // إشعار للأدمن (فقط للحالات المهمة)
        if (in_array($booking->status, ['confirmed', 'cancelled', 'completed'])) {
            $adminUsers = \App\Models\User::permission('bookings.view')->get();
            foreach ($adminUsers as $admin) {
                if ($admin->id !== $booking->client->user->id && $admin->id !== $booking->company->user->id) {
                    $this->notificationService->send($admin, [
                        'type' => 'booking_' . $booking->status,
                        'title' => __('Booking Status Changed'),
                        'message' => __('Booking #:id is now :status. Client: :client, Worker: :worker', [
                            'id' => $booking->id,
                            'status' => $newStatusText,
                            'client' => $booking->client->user->name,
                            'worker' => $booking->worker->name
                        ]),
                        'notifiable_type' => get_class($booking),
                        'notifiable_id' => $booking->id,
                        'priority' => 'low',
                        'action_url' => route('admin.bookings.show', $booking),
                        'action_text' => __('View Booking Details'),
                        'icon' => 'information-circle',
                        'color' => 'gray',
                        'data' => [
                            'booking_id' => $booking->id,
                            'old_status' => $oldStatus,
                            'new_status' => $booking->status,
                        ],
                    ]);
                }
            }
        }
    }

    /**
     * اختبار إرسال الإشعارات (للتطوير فقط)
     */
    public function testNotifications(Booking $booking)
    {
        if (app()->environment('local')) {
            $booking->load(['client.user', 'worker', 'company.user']);
            $this->sendBookingNotifications($booking, 'created');

            return response()->json([
                'message' => 'Test notifications sent successfully!',
                'booking_id' => $booking->id
            ]);
        }

        return response()->json(['error' => 'Not available in production'], 403);
    }
}
