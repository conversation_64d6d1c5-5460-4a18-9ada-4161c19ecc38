<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->text('cancellation_reason')->nullable()->after('notes');
            $table->timestamp('cancelled_at')->nullable()->after('cancellation_reason');
            $table->unsignedBigInteger('cancelled_by')->nullable()->after('cancelled_at');
            $table->text('rejection_reason')->nullable()->after('cancelled_by');
            $table->timestamp('rejected_at')->nullable()->after('rejection_reason');
            $table->unsignedBigInteger('rejected_by')->nullable()->after('rejected_at');
            $table->timestamp('confirmed_at')->nullable()->after('rejected_by');
            $table->unsignedBigInteger('confirmed_by')->nullable()->after('confirmed_at');
            
            $table->foreign('cancelled_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('rejected_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('confirmed_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->dropForeign(['cancelled_by']);
            $table->dropForeign(['rejected_by']);
            $table->dropForeign(['confirmed_by']);
            
            $table->dropColumn([
                'cancellation_reason',
                'cancelled_at',
                'cancelled_by',
                'rejection_reason',
                'rejected_at',
                'rejected_by',
                'confirmed_at',
                'confirmed_by'
            ]);
        });
    }
};
