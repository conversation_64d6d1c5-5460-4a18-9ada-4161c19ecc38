@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">

    <!-- Page Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ __('Booking Details') }} #{{ $booking->id }}</h1>
                    <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">{{ __('View booking information and status') }}</p>
                </div>
                <div class="flex space-x-3 rtl:space-x-reverse">
                    <a href="{{ route('admin.bookings.index') }}"
                       class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-md transition duration-200">
                        <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        {{ __('Back to Bookings') }}
                    </a>
                    @can('bookings.edit')
                    <a href="{{ route('admin.bookings.edit', $booking) }}"
                       class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-200">
                        <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        {{ __('Edit Booking') }}
                    </a>
                    @endcan
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Booking Overview -->
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                    <div class="px-6 py-6">
                        <!-- Status Badges -->
                        <div class="flex flex-wrap gap-3 mb-6">
                            @if($booking->status === 'pending')
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                    {{ $booking->status_text }}
                                </span>
                            @elseif($booking->status === 'confirmed')
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                    {{ $booking->status_text }}
                                </span>
                            @elseif($booking->status === 'completed')
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    {{ $booking->status_text }}
                                </span>
                            @else
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                    {{ $booking->status_text }}
                                </span>
                            @endif

                            @if($booking->payment_status === 'paid')
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    {{ __('Payment') }}: {{ $booking->payment_status_text }}
                                </span>
                            @elseif($booking->payment_status === 'pending')
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                    {{ __('Payment') }}: {{ $booking->payment_status_text }}
                                </span>
                            @else
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                    {{ __('Payment') }}: {{ $booking->payment_status_text }}
                                </span>
                            @endif

                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                {{ $booking->booking_type_text }}
                            </span>
                        </div>

                        <!-- Booking Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Client Information') }}</h3>
                                <dl class="space-y-3">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Name') }}</dt>
                                        <dd class="text-sm text-gray-900 dark:text-white">{{ $booking->client->user->name }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Email') }}</dt>
                                        <dd class="text-sm text-gray-900 dark:text-white">{{ $booking->client->user->email }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Phone') }}</dt>
                                        <dd class="text-sm text-gray-900 dark:text-white">{{ $booking->client->user->phone ?? __('Not provided') }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Home Size') }}</dt>
                                        <dd class="text-sm text-gray-900 dark:text-white">{{ $booking->client->home_size_text }}</dd>
                                    </div>
                                </dl>
                            </div>

                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Worker Information') }}</h3>
                                <dl class="space-y-3">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Name') }}</dt>
                                        <dd class="text-sm text-gray-900 dark:text-white">{{ $booking->worker->name }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Category') }}</dt>
                                        <dd class="text-sm text-gray-900 dark:text-white">{{ $booking->worker->category_text }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Nationality') }}</dt>
                                        <dd class="text-sm text-gray-900 dark:text-white">{{ $booking->worker->nationality }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Age') }}</dt>
                                        <dd class="text-sm text-gray-900 dark:text-white">{{ $booking->worker->age }} {{ __('years') }}</dd>
                                    </div>
                                </dl>
                            </div>
                        </div>

                        <!-- Company Information -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Company Information') }}</h3>
                            <dl class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Company Name') }}</dt>
                                    <dd class="text-sm text-gray-900 dark:text-white">{{ $booking->company->company_name }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Commercial Register') }}</dt>
                                    <dd class="text-sm text-gray-900 dark:text-white">{{ $booking->company->commercial_register }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Rating') }}</dt>
                                    <dd class="text-sm text-gray-900 dark:text-white">{{ number_format($booking->company->rating, 1) }}/5</dd>
                                </div>
                            </dl>
                        </div>

                        <!-- Booking Details -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Booking Details') }}</h3>
                            <dl class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Start Date') }}</dt>
                                    <dd class="text-sm text-gray-900 dark:text-white">{{ $booking->start_date->format('F d, Y H:i') }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('End Date') }}</dt>
                                    <dd class="text-sm text-gray-900 dark:text-white">{{ $booking->end_date->format('F d, Y H:i') }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Duration') }}</dt>
                                    <dd class="text-sm text-gray-900 dark:text-white">
                                        {{ $booking->start_date->diffForHumans($booking->end_date, true) }}
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Total Amount') }}</dt>
                                    <dd class="text-lg font-bold text-green-600 dark:text-green-400">${{ number_format($booking->total_amount, 2) }}</dd>
                                </div>
                            </dl>
                        </div>

                        @if($booking->notes)
                        <div class="mt-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">{{ __('Notes') }}</h3>
                            <p class="text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">{{ $booking->notes }}</p>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Payment Information -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                    <div class="px-6 py-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Payment Information') }}</h3>
                        <dl class="space-y-3">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Payment Method') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $booking->payment_method ?? __('Not specified') }}</dd>
                            </div>
                            @if($booking->transaction_id)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Transaction ID') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white font-mono">{{ $booking->transaction_id }}</dd>
                            </div>
                            @endif
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Created At') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $booking->created_at->format('F d, Y H:i') }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Last Updated') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $booking->updated_at->format('F d, Y H:i') }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                    <div class="px-6 py-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Quick Actions') }}</h3>
                        <div class="space-y-3">
                            @can('bookings.edit')
                            <a href="{{ route('admin.bookings.edit', $booking) }}"
                               class="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-200">
                                <svg class="w-4 h-4 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                {{ __('Edit Booking') }}
                            </a>
                            @endcan

                            @can('clients.view')
                            <a href="{{ route('admin.clients.show', $booking->client) }}"
                               class="w-full inline-flex items-center justify-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-md transition duration-200">
                                <svg class="w-4 h-4 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                {{ __('View Client') }}
                            </a>
                            @endcan

                            @can('workers.view')
                            <a href="{{ route('admin.workers.show', $booking->worker) }}"
                               class="w-full inline-flex items-center justify-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-md transition duration-200">
                                <svg class="w-4 h-4 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                {{ __('View Worker') }}
                            </a>
                            @endcan

                            @can('bookings.delete')
                            <form method="POST" action="{{ route('admin.bookings.destroy', $booking) }}" class="w-full">
                                @csrf
                                @method('DELETE')
                                <button type="submit"
                                        class="w-full inline-flex items-center justify-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-md transition duration-200"
                                        onclick="return confirm('{{ __('Are you sure you want to delete this booking?') }}')">
                                    <svg class="w-4 h-4 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                    {{ __('Delete Booking') }}
                                </button>
                            </form>
                            @endcan
                        </div>
                    </div>
                </div>

                <!-- Rating Actions (Only for completed bookings) -->
                @if($booking->status === 'completed')
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                    <div class="px-6 py-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                            <svg class="w-5 h-5 inline-block mr-2 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            {{ __('Rate Your Experience') }}
                        </h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">{{ __('Share your experience to help others make better decisions') }}</p>

                        <div class="space-y-3">
                            @php
                                // Check if user has already rated the worker for this booking
                                $hasRatedWorker = \App\Models\Rating::where('booking_id', $booking->id)
                                    ->where('rater_id', auth()->id())
                                    ->where('rating_type', 'worker')
                                    ->where('rated_id', $booking->worker->id)
                                    ->exists();

                                // Check if user has already rated the company for this booking
                                $hasRatedCompany = \App\Models\Rating::where('booking_id', $booking->id)
                                    ->where('rater_id', auth()->id())
                                    ->where('rating_type', 'company')
                                    ->where('rated_id', $booking->company->id)
                                    ->exists();
                            @endphp

                            <!-- Rate Worker -->
                            @if(!$hasRatedWorker)
                            <a href="{{ route('admin.ratings.create', [
                                'booking_id' => $booking->id,
                                'rating_type' => 'worker',
                                'rated_id' => $booking->worker->id,
                                'rater_id' => auth()->id()
                            ]) }}"
                               class="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-200">
                                <svg class="w-4 h-4 me-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                                {{ __('Rate Worker') }}: {{ $booking->worker->name }}
                            </a>
                            @else
                            <div class="w-full inline-flex items-center justify-center px-4 py-2 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 font-medium rounded-md">
                                <svg class="w-4 h-4 me-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                {{ __('Worker Rated') }} ✓
                            </div>
                            @endif

                            <!-- Rate Company -->
                            @if(!$hasRatedCompany)
                            <a href="{{ route('admin.ratings.create', [
                                'booking_id' => $booking->id,
                                'rating_type' => 'company',
                                'rated_id' => $booking->company->id,
                                'rater_id' => auth()->id()
                            ]) }}"
                               class="w-full inline-flex items-center justify-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-md transition duration-200">
                                <svg class="w-4 h-4 me-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 114 0 2 2 0 01-4 0zm8-1a1 1 0 100 2h2a1 1 0 100-2h-2z" clip-rule="evenodd"></path>
                                </svg>
                                {{ __('Rate Company') }}: {{ $booking->company->company_name }}
                            </a>
                            @else
                            <div class="w-full inline-flex items-center justify-center px-4 py-2 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 font-medium rounded-md">
                                <svg class="w-4 h-4 me-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                {{ __('Company Rated') }} ✓
                            </div>
                            @endif

                            @if($hasRatedWorker && $hasRatedCompany)
                            <div class="text-center py-2">
                                <p class="text-sm text-green-600 dark:text-green-400 font-medium">
                                    🎉 {{ __('Thank you for rating your experience!') }}
                                </p>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
