<?php

namespace App\Http\Controllers;

use App\Models\Schedule;
use App\Models\Worker;
use App\Models\Booking;
use Illuminate\Http\Request;

class ScheduleController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('schedules.view');

        $query = Schedule::with(['worker', 'booking']);

        // If user is a company owner, only show schedules for their workers
        $user = auth()->user();
        if ($user && $user->hasRole('company')) {
            $company = $user->company;
            if ($company) {
                $query->whereHas('worker', function ($q) use ($company) {
                    $q->where('company_id', $company->id);
                });
            }
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->whereHas('worker', function ($workerQuery) use ($search) {
                    $workerQuery->where('name', 'like', "%{$search}%");
                });
            });
        }

        // Filter by schedule type
        if ($request->filled('schedule_type')) {
            $query->where('schedule_type', $request->schedule_type);
        }

        // Filter by worker
        if ($request->filled('worker_id')) {
            $query->where('worker_id', $request->worker_id);
        }

        // Filter by date range
        if ($request->filled('start_date')) {
            $query->whereDate('start_date', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->whereDate('end_date', '<=', $request->end_date);
        }

        // Sort
        $sortBy = $request->get('sort_by', 'start_date');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortBy, $sortDirection);

        $schedules = $query->paginate(15)->withQueryString();
        $workers = Worker::where('is_active', true)->get();

        return view('admin.schedules.index', compact('schedules', 'workers'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('schedules.create');

        // If user is a company owner, only show their workers
        $user = auth()->user();
        if ($user && $user->hasRole('company')) {
            $company = $user->company;
            if ($company) {
                $workers = Worker::where('is_active', true)
                                ->where('company_id', $company->id)
                                ->get();
                $bookings = Booking::where('status', 'confirmed')
                                  ->whereHas('worker', function ($q) use ($company) {
                                      $q->where('company_id', $company->id);
                                  })
                                  ->get();
            } else {
                $workers = collect();
                $bookings = collect();
            }
        } else {
            // Admin can see all workers and bookings
            $workers = Worker::where('is_active', true)->get();
            $bookings = Booking::where('status', 'confirmed')->get();
        }

        return view('admin.schedules.create', compact('workers', 'bookings'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('schedules.create');

        $validated = $request->validate([
            'worker_id' => 'required|exists:workers,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'schedule_type' => 'required|in:available,booked,vacation',
            'booking_id' => 'nullable|exists:bookings,id',
            'notes' => 'nullable|string',
        ]);

        // If user is a company owner, ensure they can only create schedules for their workers
        $user = auth()->user();
        if ($user && $user->hasRole('company')) {
            $company = $user->company;
            if ($company) {
                $worker = Worker::find($validated['worker_id']);
                if (!$worker || $worker->company_id !== $company->id) {
                    abort(403, 'You can only create schedules for your own workers.');
                }
            }
        }

        Schedule::create($validated);

        return redirect()->route('admin.schedules.index')
                        ->with('success', __('Schedule created successfully.'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Schedule $schedule)
    {
        $this->authorize('schedules.view');

        $schedule->load(['worker', 'booking']);

        return view('admin.schedules.show', compact('schedule'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Schedule $schedule)
    {
        $this->authorize('schedules.edit');

        // If user is a company owner, ensure they can only edit schedules for their workers
        $user = auth()->user();
        if ($user && $user->hasRole('company')) {
            $company = $user->company;
            if ($company && $schedule->worker->company_id !== $company->id) {
                abort(403, 'You can only edit schedules for your own workers.');
            }
        }

        // If user is a company owner, only show their workers
        if ($user && $user->hasRole('company')) {
            $company = $user->company;
            if ($company) {
                $workers = Worker::where('is_active', true)
                                ->where('company_id', $company->id)
                                ->get();
                $bookings = Booking::where('status', 'confirmed')
                                  ->whereHas('worker', function ($q) use ($company) {
                                      $q->where('company_id', $company->id);
                                  })
                                  ->get();
            } else {
                $workers = collect();
                $bookings = collect();
            }
        } else {
            // Admin can see all workers and bookings
            $workers = Worker::where('is_active', true)->get();
            $bookings = Booking::where('status', 'confirmed')->get();
        }

        return view('admin.schedules.edit', compact('schedule', 'workers', 'bookings'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Schedule $schedule)
    {
        $this->authorize('schedules.edit');

        $validated = $request->validate([
            'worker_id' => 'required|exists:workers,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'schedule_type' => 'required|in:available,booked,vacation',
            'booking_id' => 'nullable|exists:bookings,id',
            'notes' => 'nullable|string',
        ]);

        $schedule->update($validated);

        return redirect()->route('admin.schedules.index')
                        ->with('success', __('Schedule updated successfully.'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Schedule $schedule)
    {
        $this->authorize('schedules.delete');

        $schedule->delete();

        return redirect()->route('admin.schedules.index')
                        ->with('success', __('Schedule deleted successfully.'));
    }
}
