@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Page Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                تعديل المستخدم: {{ $user->name }}
            </h1>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="py-12">
                <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <form method="POST" action="{{ route('users.update', $user) }}" enctype="multipart/form-data">
                                @csrf
                                @method('PUT')
        
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- المعلومات الأساسية -->
                                    <div class="space-y-4">
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">المعلومات الأساسية</h3>
        
                                        <!-- الاسم -->
                                        <div>
                                            <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الاسم *</label>
                                            <input type="text" name="name" id="name" value="{{ old('name', $user->name) }}" required
                                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400">
                                            @error('name')
                                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                            @enderror
                                        </div>
        
                                        <!-- البريد الإلكتروني -->
                                        <div>
                                            <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">البريد الإلكتروني *</label>
                                            <input type="email" name="email" id="email" value="{{ old('email', $user->email) }}" required
                                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400">
                                            @error('email')
                                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                            @enderror
                                        </div>
        
                                        <!-- رقم الهاتف -->
                                        <div>
                                            <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">رقم الهاتف</label>
                                            <input type="text" name="phone" id="phone" value="{{ old('phone', $user->phone) }}"
                                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400">
                                            @error('phone')
                                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                            @enderror
                                        </div>
        
                                        <!-- الصورة الشخصية الحالية -->
                                        @if($user->avatar)
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الصورة الحالية</label>
                                                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                                                    <img src="{{ asset('storage/avatars/' . $user->avatar) }}" alt="{{ $user->name }}" 
                                                         class="w-16 h-16 rounded-full object-cover border-2 border-gray-300 dark:border-gray-600">
                                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                                        <p>{{ $user->avatar }}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        @endif
        
                                        <!-- الصورة الشخصية الجديدة -->
                                        <div>
                                            <label for="avatar" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تغيير الصورة الشخصية</label>
                                            <input type="file" name="avatar" id="avatar" accept="image/*"
                                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400">
                                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">الحد الأقصى: 2 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF</p>
                                            @error('avatar')
                                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                            @enderror
                                        </div>
        
                                        <!-- الحالة -->
                                        <div>
                                            <label class="flex items-center">
                                                <input type="checkbox" name="status" value="1" {{ old('status', $user->status) ? 'checked' : '' }}
                                                       class="rounded border-gray-300 dark:border-gray-600 dark:bg-gray-700 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400">
                                                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">حساب نشط</span>
                                            </label>
                                            @error('status')
                                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                            @enderror
                                        </div>
                                    </div>
        
                                    <!-- كلمة المرور والأدوار -->
                                    <div class="space-y-4">
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">كلمة المرور والصلاحيات</h3>
        
                                        <!-- كلمة المرور -->
                                        <div>
                                            <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">كلمة المرور الجديدة</label>
                                            <input type="password" name="password" id="password"
                                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400">
                                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">اتركه فارغاً إذا كنت لا تريد تغيير كلمة المرور</p>
                                            @error('password')
                                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                            @enderror
                                        </div>
        
                                        <!-- تأكيد كلمة المرور -->
                                        <div>
                                            <label for="password_confirmation" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تأكيد كلمة المرور</label>
                                            <input type="password" name="password_confirmation" id="password_confirmation"
                                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400">
                                        </div>
        
                                        <!-- الأدوار -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الأدوار *</label>
                                            <div class="space-y-2 max-h-40 overflow-y-auto border border-gray-200 dark:border-gray-600 dark:bg-gray-700 rounded-md p-3">
                                                @foreach($roles as $role)
                                                    <label class="flex items-center">
                                                        <input type="checkbox" name="roles[]" value="{{ $role->name }}"
                                                               class="rounded border-gray-300 dark:border-gray-600 dark:bg-gray-700 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400"
                                                               {{ in_array($role->name, old('roles', $user->roles->pluck('name')->toArray())) ? 'checked' : '' }}>
                                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">{{ $role->name }}</span>
                                                    </label>
                                                @endforeach
                                            </div>
                                            @error('roles')
                                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                            @enderror
                                        </div>
        
                                        <!-- معلومات إضافية -->
                                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">معلومات الحساب</h4>
                                            <div class="space-y-1 text-sm text-gray-600 dark:text-gray-400">
                                                <p><strong>تاريخ الإنشاء:</strong> {{ $user->created_at->format('Y-m-d H:i') }}</p>
                                                <p><strong>آخر تحديث:</strong> {{ $user->updated_at->format('Y-m-d H:i') }}</p>
                                                @if($user->email_verified_at)
                                                    <p><strong>تم التحقق من البريد:</strong> {{ $user->email_verified_at->format('Y-m-d H:i') }}</p>
                                                @else
                                                    <p class="text-yellow-600 dark:text-yellow-400"><strong>البريد غير محقق</strong></p>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
        
                                <!-- أزرار التحكم -->
                                <div class="flex items-center justify-end mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                                    <a href="{{ route('users.show', $user) }}" class="bg-gray-500 hover:bg-gray-700 dark:bg-gray-600 dark:hover:bg-gray-800 text-white font-bold py-2 px-4 rounded-lg mr-3 transition">
                                        إلغاء
                                    </a>
                                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white font-bold py-2 px-4 rounded-lg transition">
                                        <svg class="w-4 h-4 inline-block mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                                        </svg>
                                        تحديث المستخدم
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
    </div>
</div>
@endsection
