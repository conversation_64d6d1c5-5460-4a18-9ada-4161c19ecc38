<?php

namespace App\Http\Controllers;

use App\Models\Worker;
use App\Models\Company;
use App\Models\Nationality;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class WorkerController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('workers.view');

        $query = Worker::with('company');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('nationality', 'like', "%{$search}%")
                  ->orWhereHas('company', function ($companyQuery) use ($search) {
                      $companyQuery->where('company_name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        // Filter by company
        if ($request->filled('company_id')) {
            $query->where('company_id', $request->company_id);
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $workers = $query->paginate(15)->withQueryString();
        $companies = Company::where('is_active', true)->get();

        return view('admin.workers.index', compact('workers', 'companies'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('workers.create');

        $companies = Company::where('is_active', true)->get();
        $nationalities = Nationality::active()->ordered()->get();

        return view('admin.workers.create', compact('companies', 'nationalities'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('workers.create');

        // Validate all fields except image first
        $validated = $request->validate([
            'company_id' => 'required|exists:companies,id',
            'name' => 'required|string|max:255',
            'age' => 'required|integer|min:18|max:65',
            'nationality' => 'required|string|max:255',
            'category' => 'required|in:housemaid,nurse,nanny,cook,cleaner,elderly_care',
            'skills' => 'nullable|array',
            'languages' => 'nullable|array',
            'experience' => 'nullable|array',
            'hourly_rate' => 'required|numeric|min:0',
            'daily_rate' => 'required|numeric|min:0',
            'monthly_rate' => 'required|numeric|min:0',
            'yearly_rate' => 'required|numeric|min:0',
            'status' => 'required|in:available,booked,unavailable',
            'added_date' => 'required|date',
        ]);

        // Handle image upload separately
        if ($request->hasFile('image') && $request->file('image')->isValid() && $request->file('image')->getSize() > 0) {
            // Validate image
            $request->validate([
                'image' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            ]);

            $validated['image'] = $request->file('image')->store('workers/images', 'public');
        }

        // Handle array fields - ensure they are not empty arrays
        $validated['skills'] = !empty($validated['skills']) ? $validated['skills'] : null;
        $validated['languages'] = !empty($validated['languages']) ? $validated['languages'] : null;
        $validated['experience'] = !empty($validated['experience']) ? $validated['experience'] : null;

        // Handle is_active checkbox
        $validated['is_active'] = $request->has('is_active') ? true : false;

        Worker::create($validated);

        return redirect()->route('admin.workers.index')
                        ->with('success', __('Worker created successfully.'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Worker $worker)
    {
        $this->authorize('workers.view');

        $worker->load('company');

        return view('admin.workers.show', compact('worker'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Worker $worker)
    {
        $this->authorize('workers.edit');

        $companies = Company::where('is_active', true)->get();
        $nationalities = Nationality::active()->ordered()->get();

        return view('admin.workers.edit', compact('worker', 'companies', 'nationalities'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Worker $worker)
    {
        $this->authorize('workers.edit');

        // Validate all fields except image first
        $validated = $request->validate([
            'company_id' => 'required|exists:companies,id',
            'name' => 'required|string|max:255',
            'age' => 'required|integer|min:18|max:65',
            'nationality' => 'required|string|max:255',
            'category' => 'required|in:housemaid,nurse,nanny,cook,cleaner,elderly_care',
            'skills' => 'nullable|array',
            'languages' => 'nullable|array',
            'experience' => 'nullable|array',
            'hourly_rate' => 'required|numeric|min:0',
            'daily_rate' => 'required|numeric|min:0',
            'monthly_rate' => 'required|numeric|min:0',
            'yearly_rate' => 'required|numeric|min:0',
            'status' => 'required|in:available,booked,unavailable',
            'added_date' => 'required|date',
        ]);

        // Handle image upload separately
        if ($request->hasFile('image') && $request->file('image')->isValid() && $request->file('image')->getSize() > 0) {
            // Validate image
            $request->validate([
                'image' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            ]);

            // Delete old image
            if ($worker->image) {
                Storage::disk('public')->delete($worker->image);
            }
            $validated['image'] = $request->file('image')->store('workers/images', 'public');
        }

        // Handle array fields - ensure they are not empty arrays
        $validated['skills'] = !empty($validated['skills']) ? $validated['skills'] : null;
        $validated['languages'] = !empty($validated['languages']) ? $validated['languages'] : null;
        $validated['experience'] = !empty($validated['experience']) ? $validated['experience'] : null;

        // Handle is_active checkbox
        $validated['is_active'] = $request->has('is_active') ? true : false;

        $worker->update($validated);

        return redirect()->route('admin.workers.index')
                        ->with('success', __('Worker updated successfully.'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Worker $worker)
    {
        $this->authorize('workers.delete');

        // Delete image if exists
        if ($worker->image) {
            Storage::disk('public')->delete($worker->image);
        }

        $worker->delete();

        return redirect()->route('admin.workers.index')
                        ->with('success', __('Worker deleted successfully.'));
    }
}
