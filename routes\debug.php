<?php

use Illuminate\Support\Facades\Route;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

Route::get('/debug/roles', function () {
    echo "<h2>الأدوار الموجودة:</h2>";
    $roles = Role::all();
    foreach ($roles as $role) {
        echo "<p>- {$role->name} (ID: {$role->id})</p>";
    }
    
    echo "<h2>المستخدمون وأدوارهم:</h2>";
    $users = User::with('roles')->get();
    foreach ($users as $user) {
        $userRoles = $user->roles->pluck('name')->implode(', ');
        echo "<p>{$user->email} => {$userRoles}</p>";
    }
    
    echo "<h2>صلاحيات دور company:</h2>";
    $companyRole = Role::where('name', 'company')->first();
    if ($companyRole) {
        $permissions = $companyRole->permissions->pluck('name');
        foreach ($permissions as $permission) {
            echo "<p>- {$permission}</p>";
        }
    } else {
        echo "<p style='color: red;'>دور company غير موجود!</p>";
    }
});
