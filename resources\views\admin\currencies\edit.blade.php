@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center">
                <a href="{{ route('admin.currencies.index') }}"
                   class="mr-4 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </a>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ __('Edit Currency') }}</h1>
                    <p class="mt-2 text-gray-600 dark:text-gray-400">{{ __('Update currency information') }}</p>
                </div>
            </div>
        </div>

        <!-- Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
            <form action="{{ route('admin.currencies.update', $currency) }}" method="POST" class="p-6 space-y-6">
                @csrf
                @method('PUT')

                <!-- Currency Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {{ __('Currency Name') }} <span class="text-red-500">*</span>
                    </label>
                    <input type="text"
                           id="name"
                           name="name"
                           value="{{ old('name', $currency->name) }}"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white @error('name') border-red-500 @enderror"
                           placeholder="{{ __('e.g., Saudi Riyal') }}"
                           required>
                    @error('name')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Currency Code -->
                <div>
                    <label for="code" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {{ __('Currency Code') }} <span class="text-red-500">*</span>
                    </label>
                    <input type="text"
                           id="code"
                           name="code"
                           value="{{ old('code', $currency->code) }}"
                           maxlength="3"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white @error('code') border-red-500 @enderror"
                           placeholder="{{ __('e.g., SAR') }}"
                           style="text-transform: uppercase;"
                           required>
                    @error('code')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ __('3-letter ISO currency code') }}</p>
                </div>

                <!-- Currency Symbol -->
                <div>
                    <label for="symbol" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {{ __('Currency Symbol') }} <span class="text-red-500">*</span>
                    </label>
                    <input type="text"
                           id="symbol"
                           name="symbol"
                           value="{{ old('symbol', $currency->symbol) }}"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white @error('symbol') border-red-500 @enderror"
                           placeholder="{{ __('e.g., ر.س') }}"
                           required>
                    @error('symbol')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Exchange Rate -->
                <div>
                    <label for="exchange_rate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {{ __('Exchange Rate') }} <span class="text-red-500">*</span>
                    </label>
                    <input type="number"
                           id="exchange_rate"
                           name="exchange_rate"
                           value="{{ old('exchange_rate', $currency->exchange_rate) }}"
                           step="0.0001"
                           min="0.0001"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white @error('exchange_rate') border-red-500 @enderror"
                           required>
                    @error('exchange_rate')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ __('Exchange rate relative to base currency') }}</p>
                </div>

                <!-- Symbol Position -->
                <div>
                    <label for="position" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {{ __('Symbol Position') }} <span class="text-red-500">*</span>
                    </label>
                    <select id="position"
                            name="position"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white @error('position') border-red-500 @enderror"
                            required>
                        <option value="before" {{ old('position', $currency->position) === 'before' ? 'selected' : '' }}>
                            {{ __('Before amount (ر.س100.00)') }}
                        </option>
                        <option value="after" {{ old('position', $currency->position) === 'after' ? 'selected' : '' }}>
                            {{ __('After amount (100.00 ر.س)') }}
                        </option>
                    </select>
                    @error('position')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        <strong>{{ __('Note') }}:</strong> {{ __('In Arabic language, currency symbol will always appear after the amount regardless of this setting') }}
                    </p>
                </div>

                <!-- Status Checkboxes -->
                <div class="space-y-4">
                    <div class="flex items-center">
                        <input type="checkbox"
                               id="is_active"
                               name="is_active"
                               value="1"
                               {{ old('is_active', $currency->is_active) ? 'checked' : '' }}
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700">
                        <label for="is_active" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                            {{ __('Active') }}
                        </label>
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox"
                               id="is_default"
                               name="is_default"
                               value="1"
                               {{ old('is_default', $currency->is_default) ? 'checked' : '' }}
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700">
                        <label for="is_default" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                            {{ __('Set as default currency') }}
                        </label>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <a href="{{ route('admin.currencies.index') }}"
                       class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                        {{ __('Cancel') }}
                    </a>
                    <button type="submit"
                            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200">
                        {{ __('Update Currency') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Auto-uppercase currency code
document.getElementById('code').addEventListener('input', function(e) {
    e.target.value = e.target.value.toUpperCase();
});
</script>
@endsection
