<?php

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\PermissionsController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\Admin\AdvancedPermissionController;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\WorkerController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\ScheduleController;
use App\Http\Controllers\RatingController;
use App\Http\Controllers\NotificationController;
use App\Http\Middleware\SetLocale;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\App;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::middleware([SetLocale::class])->group(function () {

    Route::get('/', function () {
        return view('welcome');
    })->middleware(['auth', 'verified']);

    Route::get('/dashboard', [DashboardController::class, 'index'])
        ->middleware(['auth', 'verified'])
        ->name('dashboard');

    // Admin dashboard route (alias)
    Route::get('/admin/dashboard', [DashboardController::class, 'index'])
        ->middleware(['auth', 'verified'])
        ->name('admin.dashboard');

    // صفحة تشخيص الصلاحيات
    Route::get('/debug-permissions', function () {
        return view('debug-permissions');
    })->middleware(['auth'])->name('debug.permissions');

    Route::middleware('auth')->group(function () {
        Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
        Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
        Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

        // مسارات الصلاحيات العادية
        Route::prefix('admin')->group(function () {
            Route::middleware('permission:permissions.view')->group(function () {
                Route::get('/permissions', [PermissionsController::class, 'index'])->name('permissions.index');
                Route::get('/permissions/{permission}', [PermissionsController::class, 'show'])->name('permissions.show');
            });

            Route::middleware('permission:permissions.create')->group(function () {
                Route::get('/permissions/create', [PermissionsController::class, 'create'])->name('permissions.create');
                Route::post('/permissions', [PermissionsController::class, 'store'])->name('permissions.store');
            });

            Route::middleware('permission:permissions.edit')->group(function () {
                Route::get('/permissions/{permission}/edit', [PermissionsController::class, 'edit'])->name('permissions.edit');
                Route::put('/permissions/{permission}', [PermissionsController::class, 'update'])->name('permissions.update');
            });

            Route::middleware('permission:permissions.delete')->group(function () {
                Route::delete('/permissions/{permission}', [PermissionsController::class, 'destroy'])->name('permissions.destroy');
            });
        });

        // مسارات نظام الصلاحيات المتقدم (في مسار منفصل لتجنب التضارب)
        Route::prefix('admin/advanced-permissions')->name('admin.permissions.advanced.')->group(function () {
            Route::get('/', function () {
                $roles = \Spatie\Permission\Models\Role::with(['permissions', 'users'])->paginate(10);
                $groups = \App\Models\PermissionGroup::with('permissions')->ordered()->get();
                $stats = [
                    'total_roles' => \Spatie\Permission\Models\Role::count(),
                    'total_permissions' => \Spatie\Permission\Models\Permission::count(),
                    'total_groups' => \App\Models\PermissionGroup::count(),
                    'recent_changes' => 0 // \App\Models\PermissionLog::recent(7)->count()
                ];
                $search = request('search', '');
                return view('admin.permissions.advanced.index', compact('roles', 'groups', 'stats', 'search'));
            })->name('index');

            Route::get('/test', function () {
                $roles = \Spatie\Permission\Models\Role::with(['permissions'])->paginate(10);
                $groups = \App\Models\PermissionGroup::with('permissions')->ordered()->get();
                $stats = [
                    'total_roles' => \Spatie\Permission\Models\Role::count(),
                    'total_permissions' => \Spatie\Permission\Models\Permission::count(),
                    'total_groups' => \App\Models\PermissionGroup::count(),
                    'recent_changes' => 0 // \App\Models\PermissionLog::recent(7)->count()
                ];
                $search = '';
                return view('admin.permissions.advanced.test', compact('roles', 'groups', 'stats', 'search'));
            })->name('test');

            Route::get('/logs/history', [AdvancedPermissionController::class, 'logs'])->name('logs');
            Route::get('/stats/overview', [AdvancedPermissionController::class, 'statistics'])->name('statistics');
            Route::post('/copy', [AdvancedPermissionController::class, 'copyPermissions'])->name('copy');

            Route::get('/{role}', [AdvancedPermissionController::class, 'show'])->name('show');
            Route::get('/{role}/edit', [AdvancedPermissionController::class, 'edit'])->name('edit');
            Route::put('/{role}', [AdvancedPermissionController::class, 'update'])->name('update');
            Route::post('/{role}/quick-set', [AdvancedPermissionController::class, 'applyQuickSet'])->name('quick-set');
        });

        // مسارات إدارة المستخدمين
        Route::get('/users', [UserController::class, 'index'])->name('users.index');
        Route::get('/users/create', [UserController::class, 'create'])->name('users.create');
        Route::post('/users', [UserController::class, 'store'])->name('users.store');
        Route::get('/users/{user}', [UserController::class, 'show'])->name('users.show');
        Route::get('/users/{user}/edit', [UserController::class, 'edit'])->name('users.edit');
        Route::put('/users/{user}', [UserController::class, 'update'])->name('users.update');
        Route::patch('/users/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('users.toggle-status');
        Route::delete('/users/{user}', [UserController::class, 'destroy'])->name('users.destroy');

        // مسارات إدارة الأدوار
        Route::get('/roles', [RoleController::class, 'index'])->name('roles.index');
        Route::get('/roles/create', [RoleController::class, 'create'])->name('roles.create');
        Route::post('/roles', [RoleController::class, 'store'])->name('roles.store');
        Route::get('/roles/{role}', [RoleController::class, 'show'])->name('roles.show');
        Route::get('/roles/{role}/edit', [RoleController::class, 'edit'])->name('roles.edit');
        Route::put('/roles/{role}', [RoleController::class, 'update'])->name('roles.update');
        Route::delete('/roles/{role}', [RoleController::class, 'destroy'])->name('roles.destroy');

        // Companies Routes
        Route::prefix('admin')->group(function () {
            Route::resource('companies', CompanyController::class)->names([
                'index' => 'admin.companies.index',
                'create' => 'admin.companies.create',
                'store' => 'admin.companies.store',
                'show' => 'admin.companies.show',
                'edit' => 'admin.companies.edit',
                'update' => 'admin.companies.update',
                'destroy' => 'admin.companies.destroy',
            ]);
        });

        // Admin Routes with prefix
        Route::prefix('admin')->group(function () {
            // Clients Routes
            Route::resource('clients', ClientController::class)->names([
                'index' => 'admin.clients.index',
                'create' => 'admin.clients.create',
                'store' => 'admin.clients.store',
                'show' => 'admin.clients.show',
                'edit' => 'admin.clients.edit',
                'update' => 'admin.clients.update',
                'destroy' => 'admin.clients.destroy',
            ]);

            // Workers Routes
            Route::resource('workers', WorkerController::class)->names([
                'index' => 'admin.workers.index',
                'create' => 'admin.workers.create',
                'store' => 'admin.workers.store',
                'show' => 'admin.workers.show',
                'edit' => 'admin.workers.edit',
                'update' => 'admin.workers.update',
                'destroy' => 'admin.workers.destroy',
            ]);

            // Bookings Routes
            Route::resource('bookings', BookingController::class)->names([
                'index' => 'admin.bookings.index',
                'create' => 'admin.bookings.create',
                'store' => 'admin.bookings.store',
                'show' => 'admin.bookings.show',
                'edit' => 'admin.bookings.edit',
                'update' => 'admin.bookings.update',
                'destroy' => 'admin.bookings.destroy',
            ]);

            // اختبار الإشعارات (للتطوير فقط)
            Route::post('/bookings/{booking}/test-notifications', [BookingController::class, 'testNotifications'])->name('admin.bookings.test-notifications');

            // Schedules Routes
            Route::resource('schedules', ScheduleController::class)->names([
                'index' => 'admin.schedules.index',
                'create' => 'admin.schedules.create',
                'store' => 'admin.schedules.store',
                'show' => 'admin.schedules.show',
                'edit' => 'admin.schedules.edit',
                'update' => 'admin.schedules.update',
                'destroy' => 'admin.schedules.destroy',
            ]);

            // Ratings Routes
            Route::resource('ratings', RatingController::class)->names([
                'index' => 'admin.ratings.index',
                'create' => 'admin.ratings.create',
                'store' => 'admin.ratings.store',
                'show' => 'admin.ratings.show',
                'edit' => 'admin.ratings.edit',
                'update' => 'admin.ratings.update',
                'destroy' => 'admin.ratings.destroy',
            ]);

            // Notification API Routes (separate from resource routes)
            Route::prefix('notifications-api')->group(function () {
                Route::get('unread-count', [NotificationController::class, 'getUnreadCount'])->name('admin.notifications.unread-count');
                Route::get('recent', [NotificationController::class, 'getRecent'])->name('admin.notifications.recent');
                Route::post('mark-all-as-read', [NotificationController::class, 'markAllAsRead'])->name('admin.notifications.mark-all-as-read');
            });

            // Individual notification actions
            Route::post('notifications/{notification}/mark-as-read', [NotificationController::class, 'markAsRead'])->name('admin.notifications.mark-as-read');
            Route::post('notifications/{notification}/mark-as-unread', [NotificationController::class, 'markAsUnread'])->name('admin.notifications.mark-as-unread');

            // Notifications Routes
            Route::resource('notifications', NotificationController::class)->names([
                'index' => 'admin.notifications.index',
                'create' => 'admin.notifications.create',
                'store' => 'admin.notifications.store',
                'show' => 'admin.notifications.show',
                'edit' => 'admin.notifications.edit',
                'update' => 'admin.notifications.update',
                'destroy' => 'admin.notifications.destroy',
            ]);

            // Currency Routes
            Route::resource('currencies', \App\Http\Controllers\Admin\CurrencyController::class)->names([
                'index' => 'admin.currencies.index',
                'create' => 'admin.currencies.create',
                'store' => 'admin.currencies.store',
                'show' => 'admin.currencies.show',
                'edit' => 'admin.currencies.edit',
                'update' => 'admin.currencies.update',
                'destroy' => 'admin.currencies.destroy',
            ]);
            Route::post('currencies/{currency}/set-default', [\App\Http\Controllers\Admin\CurrencyController::class, 'setDefault'])->name('admin.currencies.set-default');

            // Test Currency Route
            Route::get('test-currency', function () {
                if (request('lang')) {
                    session(['locale' => request('lang')]);
                    app()->setLocale(request('lang'));
                }
                return view('test-currency');
            })->name('admin.test-currency');

            // Test Permissions Route
            Route::get('test-permissions', function () {
                return view('test-permissions');
            })->name('admin.test-permissions');

            // Permissions Demo Route
            Route::get('permissions-demo', function () {
                return view('admin.users.permissions-demo');
            })->name('admin.permissions-demo');

            // Test route for notifications
            Route::get('test-notifications', function () {
                return view('test-notifications');
            })->name('admin.test-notifications');
        });
    });

    Route::get('set_locale/{locale}', function ($locale) {
        if (in_array($locale, config('app.locales'))) {
            Session::put('locale', $locale);
            App::setLocale($locale);
        }

        // Get the current URL without any fragments or form data
        $referer = request()->header('referer');
        if ($referer && str_contains($referer, request()->getHost())) {
            // Clean the URL to avoid form resubmission
            $cleanUrl = strtok($referer, '?');
            return redirect($cleanUrl);
        }

        return redirect()->route('dashboard');
    })->name('set_locale');

    // Test route for notifications API (temporary - remove in production)
    Route::get('test-api/notifications/count', function () {
        $user = \App\Models\User::first();
        if (!$user) {
            return response()->json(['error' => 'No users found']);
        }

        $count = \App\Models\Notification::where('user_id', $user->id)
                                        ->where('is_read', false)
                                        ->count();

        return response()->json([
            'count' => $count,
            'user_id' => $user->id,
            'user_name' => $user->name,
            'message' => 'Test API working'
        ]);
    });

    require __DIR__.'/auth.php';
});

// Debug routes (remove in production)
require __DIR__.'/debug.php';

// Company Dashboard Routes
use App\Http\Controllers\Company\DashboardController as CompanyDashboardController;
use App\Http\Controllers\Company\WorkerController as CompanyWorkerController;
use App\Http\Controllers\Company\BookingController as CompanyBookingController;
use App\Http\Controllers\Company\ScheduleController as CompanyScheduleController;
use App\Http\Controllers\Company\RatingController as CompanyRatingController;
use App\Http\Controllers\Company\ProfileController as CompanyProfileController;

// Client Dashboard Routes
use App\Http\Controllers\Client\DashboardController as ClientDashboardController;
use App\Http\Controllers\Client\BookingController as ClientBookingController;

Route::middleware(['auth', 'role:company'])->prefix('company')->name('company.')->group(function () {
    Route::get('/dashboard', [CompanyDashboardController::class, 'index'])->name('dashboard');

    // Company Workers Management
    Route::resource('workers', CompanyWorkerController::class);

    // Company Bookings Management
    Route::resource('bookings', CompanyBookingController::class);
    Route::post('bookings/{booking}/approve', [CompanyBookingController::class, 'approve'])->name('bookings.approve');
    Route::post('bookings/{booking}/reject', [CompanyBookingController::class, 'reject'])->name('bookings.reject');
    Route::post('bookings/{booking}/complete', [CompanyBookingController::class, 'complete'])->name('bookings.complete');

    // Company Schedules Management
    Route::resource('schedules', CompanyScheduleController::class);

    // Company Ratings View
    Route::get('ratings', [CompanyRatingController::class, 'index'])->name('ratings.index');
    Route::get('ratings/{rating}', [CompanyRatingController::class, 'show'])->name('ratings.show');
    Route::post('ratings/{rating}/respond', [CompanyRatingController::class, 'respond'])->name('ratings.respond');

    // Company Profile Management
    Route::get('profile', [CompanyProfileController::class, 'edit'])->name('profile.edit');
    Route::put('profile', [CompanyProfileController::class, 'update'])->name('profile.update');

    // Company Notifications API Routes
    Route::prefix('notifications-api')->group(function () {
        Route::get('unread-count', [\App\Http\Controllers\NotificationController::class, 'getUnreadCount'])->name('notifications.unread-count');
        Route::get('recent', [\App\Http\Controllers\NotificationController::class, 'getRecent'])->name('notifications.recent');
        Route::post('mark-all-as-read', [\App\Http\Controllers\NotificationController::class, 'markAllAsRead'])->name('notifications.mark-all-as-read-api');
    });

    // Company Notifications Web Routes
    Route::post('notifications/mark-all-as-read', [\App\Http\Controllers\NotificationController::class, 'markAllAsReadWeb'])->name('notifications.mark-all-as-read');

    // Company Individual notification actions
    Route::post('notifications/{notification}/mark-as-read', [\App\Http\Controllers\NotificationController::class, 'markAsReadWeb'])->name('notifications.mark-as-read');
    Route::post('notifications/{notification}/mark-as-unread', [\App\Http\Controllers\NotificationController::class, 'markAsUnreadWeb'])->name('notifications.mark-as-unread');

    // Company Notifications Routes
    Route::get('notifications', [\App\Http\Controllers\NotificationController::class, 'index'])->name('notifications.index');
    Route::get('notifications/{notification}', [\App\Http\Controllers\NotificationController::class, 'show'])->name('notifications.show');
    Route::delete('notifications/{notification}', [\App\Http\Controllers\NotificationController::class, 'destroy'])->name('notifications.destroy');

    // Company Payments Management
    Route::get('payments', [\App\Http\Controllers\Company\PaymentController::class, 'index'])->name('payments.index');
    Route::get('payments/{booking}', [\App\Http\Controllers\Company\PaymentController::class, 'show'])->name('payments.show');
    Route::get('payments/receipt/{transactionId}', [\App\Http\Controllers\Company\PaymentController::class, 'receipt'])->name('payments.receipt');
    Route::get('payments/receipt/{transactionId}/download', [\App\Http\Controllers\Company\PaymentController::class, 'downloadReceipt'])->name('payments.receipt.download');
});

// Client Dashboard Routes (for both 'client' and 'user' roles)
Route::middleware(['auth'])->prefix('client')->name('client.')->group(function () {
    Route::get('/dashboard', [ClientDashboardController::class, 'index'])->name('dashboard');

    // Client Bookings Management
    Route::resource('bookings', ClientBookingController::class)->only(['index', 'create', 'store', 'show']);
    Route::post('bookings/{booking}/cancel', [ClientBookingController::class, 'cancel'])->name('bookings.cancel');

    // Client Ratings Management (View and Create Only - No Edit/Delete for Credibility)
    Route::resource('ratings', \App\Http\Controllers\Client\RatingController::class)->only(['index', 'create', 'store', 'show']);

    // Client Payments Management
    Route::get('payments', [\App\Http\Controllers\Client\PaymentController::class, 'index'])->name('payments.index');
    Route::get('payments/{booking}', [\App\Http\Controllers\Client\PaymentController::class, 'show'])->name('payments.show');
    Route::get('payments/receipt/{transactionId}', [\App\Http\Controllers\Client\PaymentController::class, 'receipt'])->name('payments.receipt');
    Route::get('payments/receipt/{transactionId}/download', [\App\Http\Controllers\Client\PaymentController::class, 'downloadReceipt'])->name('payments.receipt.download');

    // Client Notifications Management
    Route::resource('notifications', \App\Http\Controllers\Client\NotificationController::class)->only(['index', 'show', 'destroy']);
    Route::post('notifications/{notification}/mark-as-read', [\App\Http\Controllers\Client\NotificationController::class, 'markAsRead'])->name('notifications.mark-as-read');
    Route::post('notifications/mark-all-as-read', [\App\Http\Controllers\Client\NotificationController::class, 'markAllAsRead'])->name('notifications.mark-all-as-read');
    Route::get('notifications/unread-count', [\App\Http\Controllers\Client\NotificationController::class, 'getUnreadCount'])->name('notifications.unread-count');

    // Client Notifications API
    Route::get('notifications-api/unread-count', [\App\Http\Controllers\Client\NotificationApiController::class, 'unreadCount'])->name('notifications.api.unread-count');
    Route::get('notifications-api/recent', [\App\Http\Controllers\Client\NotificationApiController::class, 'recent'])->name('notifications.api.recent');
    Route::post('notifications-api/mark-all-as-read', [\App\Http\Controllers\Client\NotificationApiController::class, 'markAllAsRead'])->name('notifications.api.mark-all-as-read');
});

// Features Pages
Route::get('/features/whats-new', function () {
    return view('features.whats-new');
})->name('features.whats-new');

// Test Booking System
Route::get('/test-booking', function () {
    return view('test-booking');
})->name('test.booking');




