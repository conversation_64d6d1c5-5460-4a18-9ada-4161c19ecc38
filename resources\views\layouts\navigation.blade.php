<nav x-data="{
        open: false,
        darkMode: localStorage.getItem('darkMode') === 'true' || (localStorage.getItem('darkMode') === null && window.matchMedia('(prefers-color-scheme: dark)').matches),
        toggleDarkMode() {
            this.darkMode = !this.darkMode;
            localStorage.setItem('darkMode', this.darkMode);
            if (this.darkMode) {
                document.documentElement.classList.add('dark');
                document.body.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
                document.body.classList.remove('dark');
            }
        }
     }"
     x-init="
         if (darkMode) {
             document.documentElement.classList.add('dark');
             document.body.classList.add('dark');
         } else {
             document.documentElement.classList.remove('dark');
             document.body.classList.remove('dark');
         }
     "
     class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
    <!-- Primary Navigation Menu -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
            <div class="flex">
                <!-- Logo -->
                <div class="shrink-0 flex items-center">
                    <a href="{{ route('dashboard') }}">
                        <x-application-logo class="block h-9 w-auto fill-current text-gray-800 dark:text-gray-200" />
                    </a>
                </div>

                <!-- Navigation Links -->
                <div class="hidden space-x-8 sm:-my-px sm:ms-10 sm:flex">
                    <x-nav-link :href="route('dashboard')" :active="request()->routeIs('dashboard')">
                        {{ __('Dashboard') }}
                    </x-nav-link>

                    @can('users.view')
                    <x-nav-link :href="route('users.index')" :active="request()->routeIs('users.*')">
                        {{ __('المستخدمين') }}
                    </x-nav-link>
                    @endcan

                    @can('roles.view')
                    <x-nav-link :href="route('roles.index')" :active="request()->routeIs('roles.*')">
                        {{ __('الأدوار') }}
                    </x-nav-link>
                    @endcan

                    @can('permissions.view')
                    <x-nav-link :href="route('permissions.index')" :active="request()->routeIs('permissions.*')">
                        {{ __('الصلاحيات') }}
                    </x-nav-link>
                    @endcan

                    @can('permissions.view')
                    <x-nav-link :href="route('admin.permissions.advanced.index')" :active="request()->routeIs('admin.permissions.advanced.*')">
                        {{ __('الصلاحيات المتقدمة') }}
                    </x-nav-link>
                    @endcan

                    @can('companies.view')
                    <x-nav-link :href="route('admin.companies.index')" :active="request()->routeIs('admin.companies.*')">
                        {{ __('Companies') }}
                    </x-nav-link>
                    @endcan

                    @can('clients.view')
                    <x-nav-link :href="route('admin.clients.index')" :active="request()->routeIs('admin.clients.*')">
                        {{ __('Clients') }}
                    </x-nav-link>
                    @endcan

                    @can('workers.view')
                    <x-nav-link :href="route('admin.workers.index')" :active="request()->routeIs('admin.workers.*')">
                        {{ __('Workers') }}
                    </x-nav-link>
                    @endcan

                    @can('bookings.view')
                    <x-nav-link :href="route('admin.bookings.index')" :active="request()->routeIs('admin.bookings.*')">
                        {{ __('Bookings') }}
                    </x-nav-link>
                    @endcan

                    @can('schedules.view')
                    <x-nav-link :href="route('admin.schedules.index')" :active="request()->routeIs('admin.schedules.*')">
                        {{ __('Schedules') }}
                    </x-nav-link>
                    @endcan

                    @can('ratings.view')
                        @if(auth()->user()->hasAnyRole(['admin', 'editor', 'super-admin']))
                            <x-nav-link :href="route('admin.ratings.index')" :active="request()->routeIs('admin.ratings.*')">
                                {{ __('Ratings') }}
                            </x-nav-link>
                        @elseif(auth()->user()->hasAnyRole(['client', 'user']))
                            <x-nav-link :href="route('client.ratings.index')" :active="request()->routeIs('client.ratings.*')">
                                {{ __('Ratings') }}
                            </x-nav-link>
                        @endif
                    @endcan

                    @auth
                        @if(auth()->user()->hasAnyRole(['admin', 'editor', 'super-admin']) || auth()->user()->hasRole('Super Admin'))
                            <x-nav-link :href="route('admin.notifications.index')" :active="request()->routeIs('admin.notifications.*')">
                                {{ __('الإشعارات') }}
                            </x-nav-link>
                        @elseif(auth()->user()->hasAnyRole(['client', 'user']))
                            <x-nav-link :href="route('client.notifications.index')" :active="request()->routeIs('client.notifications.*')">
                                {{ __('الإشعارات') }}
                            </x-nav-link>
                        @elseif(auth()->user()->hasRole('company'))
                            <x-nav-link :href="route('company.notifications.index')" :active="request()->routeIs('company.notifications.*')">
                                {{ __('الإشعارات') }}
                            </x-nav-link>
                        @endif
                    @endauth

                    <!-- Client Payments Link -->
                    @if(auth()->user()->hasAnyRole(['client', 'user']))
                        @can('payments.view')
                            <x-nav-link :href="route('client.payments.index')" :active="request()->routeIs('client.payments.*')">
                                {{ __('My Payments') }}
                            </x-nav-link>
                        @endcan
                    @endif

                    <!-- Company Payments Link -->
                    @if(auth()->user()->hasRole('company'))
                        @can('payments.view')
                            <x-nav-link :href="route('company.payments.index')" :active="request()->routeIs('company.payments.*')">
                                {{ __('Payments') }}
                            </x-nav-link>
                        @endcan
                    @endif

                    @can('currencies.view')
                    <x-nav-link :href="route('admin.currencies.index')" :active="request()->routeIs('admin.currencies.*')">
                        {{ __('Currencies') }}
                    </x-nav-link>
                    @endcan
                </div>
            </div>

            <!-- Settings Dropdown -->
            <div class="hidden sm:flex sm:items-center sm:ms-6">
            @auth
                <!-- Language Switcher -->
                <x-dropdown align="right" width="32">
                    <x-slot name="trigger">
                        <button class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-full text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none transition ease-in-out duration-150">
                            <div>{{ strtoupper(app()->getLocale()) }}</div>
                        </button>
                    </x-slot>

                    <x-slot name="content">
                        @foreach (config('app.locales') as $locale)
                            <x-dropdown-link :href="route('set_locale', ['locale' => $locale])">
                                {{ strtoupper($locale) }}
                                @if(app()->getLocale() == $locale)
                                    ✓
                                @endif
                            </x-dropdown-link>
                        @endforeach
                    </x-slot>
                </x-dropdown>

                <!-- Notifications Dropdown -->
                @auth
                <div class="relative me-6">
                    <button onclick="toggleNotifications()"
                            class="relative p-2 text-gray-400 hover:text-gray-500 dark:text-gray-300 dark:hover:text-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-full transition duration-200"
                            title="{{ __('الإشعارات') }}">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0"></path>
                        </svg>
                        <span id="notification-badge" class="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full hidden">
                            0
                        </span>
                    </button>

                    <!-- Notifications Dropdown -->
                    <div id="notifications-dropdown"
                         class="hidden absolute left-0 rtl:right-0 mt-2 w-80 sm:w-96 bg-white dark:bg-gray-800 rounded-md shadow-xl ring-1 ring-black ring-opacity-5 z-50 max-h-[400px] flex flex-col">
                        <!-- Header -->
                        <div class="px-3 py-2 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
                            <div class="flex justify-between items-center">
                                <h3 class="text-xs font-medium text-gray-900 dark:text-white">{{ __('Notifications') }}</h3>
                                @auth
                                    @if(auth()->user()->hasAnyRole(['admin', 'editor', 'super-admin']))
                                        <a href="{{ route('admin.notifications.index') }}"
                                           class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 whitespace-nowrap">
                                            {{ __('View All') }}
                                        </a>
                                    @elseif(auth()->user()->hasAnyRole(['client', 'user']))
                                        <a href="{{ route('client.notifications.index') }}"
                                           class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 whitespace-nowrap">
                                            {{ __('View All') }}
                                        </a>
                                    @endif
                                @endauth
                            </div>
                        </div>

                        <!-- Notifications List -->
                        <div id="notifications-list" class="flex-1 overflow-y-auto min-h-0">
                            <!-- Notifications will be loaded here -->
                            <div class="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
                                <svg class="mx-auto h-8 w-8 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0"></path>
                                </svg>
                                <span class="text-sm">{{ __('Loading notifications...') }}</span>
                            </div>
                        </div>

                        <!-- Footer -->
                        <div class="px-3 py-1.5 border-t border-gray-200 dark:border-gray-700 flex-shrink-0 bg-gray-50 dark:bg-gray-700/50">
                            <div class="flex justify-between items-center text-xs">
                                <button onclick="markAllAsRead()"
                                        class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-xs">
                                    {{ __('Mark all as read') }}
                                </button>
                                <span class="text-gray-500 dark:text-gray-400 text-xs">
                                    <span id="last-updated">{{ __('Now') }}</span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                @endauth

                <!-- Dark Mode Toggle -->
                <button x-on:click="toggleDarkMode()"
                        class="mx-6 inline-flex items-center justify-center p-2 rounded-lg text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition duration-200 ease-in-out"
                        title="تبديل الوضع المظلم">
                    <!-- أيقونة الشمس (الوضع الفاتح) -->
                    <svg x-show="!darkMode" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m.386-6.364 1.591 1.591M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                    </svg>
                    <!-- أيقونة القمر (الوضع المظلم) -->
                    <svg x-show="darkMode" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z" />
                    </svg>
                </button>

                <x-dropdown align="right" width="48">
                    <x-slot name="trigger">
                        <button class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none transition ease-in-out duration-150">
                            <div>{{ Auth::user()->name }}</div>

                            <div class="ms-1">
                                <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </div>
                        </button>
                    </x-slot>

                    <x-slot name="content">
                        <x-dropdown-link :href="route('profile.edit')">
                            {{ __('Profile') }}
                        </x-dropdown-link>

                        <!-- Authentication -->
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf

                            <x-dropdown-link :href="route('logout')"
                                    onclick="event.preventDefault();
                                                this.closest('form').submit();">
                                {{ __('Log Out') }}
                            </x-dropdown-link>
                        </form>
                    </x-slot>
                </x-dropdown>
                @endauth
            </div>

            <!-- Hamburger -->
            <div class="-me-2 flex items-center sm:hidden">
                <button @click="open = ! open" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 dark:text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700 focus:text-gray-500 dark:focus:text-gray-300 transition duration-150 ease-in-out">
                    <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                        <path :class="{'hidden': open, 'inline-flex': ! open }" class="inline-flex" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        <path :class="{'hidden': ! open, 'inline-flex': open }" class="hidden" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Responsive Navigation Menu -->
    <div :class="{'block': open, 'hidden': ! open}" class="hidden sm:hidden">
        <div class="pt-2 pb-3 space-y-1">
            <x-responsive-nav-link :href="route('dashboard')" :active="request()->routeIs('dashboard')">
                {{ __('Dashboard') }}
            </x-responsive-nav-link>

            @can('users.view')
            <x-responsive-nav-link :href="route('users.index')" :active="request()->routeIs('users.*')">
                {{ __('المستخدمين') }}
            </x-responsive-nav-link>
            @endcan

            @can('roles.view')
            <x-responsive-nav-link :href="route('roles.index')" :active="request()->routeIs('roles.*')">
                {{ __('الأدوار') }}
            </x-responsive-nav-link>
            @endcan

            @can('permissions.view')
            <x-responsive-nav-link :href="route('permissions.index')" :active="request()->routeIs('permissions.*')">
                {{ __('الصلاحيات') }}
            </x-responsive-nav-link>
            @endcan

            @can('permissions.view')
            <x-responsive-nav-link :href="route('admin.permissions.advanced.index')" :active="request()->routeIs('admin.permissions.advanced.*')">
                {{ __('الصلاحيات المتقدمة') }}
            </x-responsive-nav-link>
            @endcan

            @can('companies.view')
            <x-responsive-nav-link :href="route('admin.companies.index')" :active="request()->routeIs('admin.companies.*')">
                {{ __('Companies') }}
            </x-responsive-nav-link>
            @endcan

            @can('clients.view')
            <x-responsive-nav-link :href="route('admin.clients.index')" :active="request()->routeIs('admin.clients.*')">
                {{ __('Clients') }}
            </x-responsive-nav-link>
            @endcan

            @can('workers.view')
            <x-responsive-nav-link :href="route('admin.workers.index')" :active="request()->routeIs('admin.workers.*')">
                {{ __('Workers') }}
            </x-responsive-nav-link>
            @endcan

            @can('bookings.view')
            <x-responsive-nav-link :href="route('admin.bookings.index')" :active="request()->routeIs('admin.bookings.*')">
                {{ __('Bookings') }}
            </x-responsive-nav-link>
            @endcan

            @can('schedules.view')
            <x-responsive-nav-link :href="route('admin.schedules.index')" :active="request()->routeIs('admin.schedules.*')">
                {{ __('Schedules') }}
            </x-responsive-nav-link>
            @endcan

            @can('ratings.view')
                @if(auth()->user()->hasAnyRole(['admin', 'editor', 'super-admin']))
                    <x-responsive-nav-link :href="route('admin.ratings.index')" :active="request()->routeIs('admin.ratings.*')">
                        {{ __('Ratings') }}
                    </x-responsive-nav-link>
                @elseif(auth()->user()->hasAnyRole(['client', 'user']))
                    <x-responsive-nav-link :href="route('client.ratings.index')" :active="request()->routeIs('client.ratings.*')">
                        {{ __('Ratings') }}
                    </x-responsive-nav-link>
                @endif
            @endcan

            @auth
                @if(auth()->user()->hasAnyRole(['admin', 'editor', 'super-admin']))
                    <x-responsive-nav-link :href="route('admin.notifications.index')" :active="request()->routeIs('admin.notifications.*')">
                        {{ __('الإشعارات') }}
                    </x-responsive-nav-link>
                @elseif(auth()->user()->hasAnyRole(['client', 'user']))
                    <x-responsive-nav-link :href="route('client.notifications.index')" :active="request()->routeIs('client.notifications.*')">
                        {{ __('الإشعارات') }}
                    </x-responsive-nav-link>
                @elseif(auth()->user()->hasRole('company'))
                    <x-responsive-nav-link :href="route('company.notifications.index')" :active="request()->routeIs('company.notifications.*')">
                        {{ __('الإشعارات') }}
                    </x-responsive-nav-link>
                @endif
            @endauth

            <!-- Client Payments Link -->
            @if(auth()->user()->hasAnyRole(['client', 'user']))
                @can('payments.view')
                    <x-responsive-nav-link :href="route('client.payments.index')" :active="request()->routeIs('client.payments.*')">
                        {{ __('My Payments') }}
                    </x-responsive-nav-link>
                @endcan
            @endif

            <!-- Company Payments Link -->
            @if(auth()->user()->hasRole('company'))
                @can('payments.view')
                    <x-responsive-nav-link :href="route('company.payments.index')" :active="request()->routeIs('company.payments.*')">
                        {{ __('Payments') }}
                    </x-responsive-nav-link>
                @endcan
            @endif

            @can('currencies.view')
            <x-responsive-nav-link :href="route('admin.currencies.index')" :active="request()->routeIs('admin.currencies.*')">
                {{ __('Currencies') }}
            </x-responsive-nav-link>
            @endcan
        </div>

        <!-- Responsive Settings Options -->
        @auth
        <div class="pt-4 pb-1 border-t border-gray-600">
            <div class="px-4">
                <div class="font-medium text-base text-gray-200">{{ Auth::user()->name }}</div>
                <div class="font-medium text-sm text-gray-400">{{ Auth::user()->email }}</div>
            </div>

            <div class="mt-3 space-y-1">
                <!-- Dark Mode Toggle for Mobile -->
                <div class="px-4 py-2">
                    <button x-on:click="toggleDarkMode()"
                            class="w-full flex items-center justify-between p-3 rounded-lg text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 transition duration-200 ease-in-out">
                        <span class="text-sm font-medium">الوضع المظلم</span>
                        <div class="flex items-center">
                            <!-- أيقونة الشمس (الوضع الفاتح) -->
                            <svg x-show="!darkMode" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m.386-6.364 1.591 1.591M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                            </svg>
                            <!-- أيقونة القمر (الوضع المظلم) -->
                            <svg x-show="darkMode" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z" />
                            </svg>
                        </div>
                    </button>
                </div>

                <x-responsive-nav-link :href="route('profile.edit')">
                    {{ __('Profile') }}
                </x-responsive-nav-link>

                <!-- Authentication -->
                <form method="POST" action="{{ route('logout') }}">
                    @csrf

                    <x-responsive-nav-link :href="route('logout')"
                            onclick="event.preventDefault();
                                        this.closest('form').submit();">
                        {{ __('Log Out') }}
                    </x-responsive-nav-link>
                </form>
            </div>
        </div>
        @endauth
    </div>
</nav>

<script>
// Notifications functionality
let notificationsDropdown = null;
let notificationsBadge = null;
let notificationsList = null;

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing notifications...');

    notificationsDropdown = document.getElementById('notifications-dropdown');
    notificationsBadge = document.getElementById('notification-badge');
    notificationsList = document.getElementById('notifications-list');

    console.log('Elements found:', {
        dropdown: !!notificationsDropdown,
        badge: !!notificationsBadge,
        list: !!notificationsList
    });

    if (!notificationsBadge) {
        console.error('Notification badge element not found!');
        return;
    }

    // Load notifications count on page load
    loadNotificationsCount();



    // Auto-refresh notifications every 30 seconds
    setInterval(loadNotificationsCount, 30000);

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.relative')) {
            hideNotifications();
        }
    });
});

function toggleNotifications() {
    if (notificationsDropdown.classList.contains('hidden')) {
        showNotifications();
    } else {
        hideNotifications();
    }
}

function showNotifications() {
    notificationsDropdown.classList.remove('hidden');

    // Adjust position to prevent overflow
    adjustDropdownPosition();

    loadRecentNotifications();
}

function adjustDropdownPosition() {
    const dropdown = notificationsDropdown;
    const rect = dropdown.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;

    // Reset classes
    dropdown.classList.remove('bottom-0', 'top-full', 'right-0', 'left-0');

    // Check if dropdown goes below viewport
    if (rect.bottom > viewportHeight - 20) {
        // Position above the button
        dropdown.classList.add('bottom-full', 'mb-2');
        dropdown.classList.remove('mt-2');
    } else {
        // Position below the button (default)
        dropdown.classList.add('top-full', 'mt-2');
        dropdown.classList.remove('mb-2', 'bottom-full');
    }

    // Check if dropdown goes beyond right edge (for LTR)
    if (rect.right > viewportWidth - 20) {
        dropdown.classList.add('right-0');
        dropdown.classList.remove('left-0');
    }
}

function hideNotifications() {
    notificationsDropdown.classList.add('hidden');
}

function loadNotificationsCount() {
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

    // Determine the correct API endpoint based on user role
    let apiEndpoint = '/admin/notifications-api/unread-count';
    @auth
        @if(auth()->user()->hasRole('company'))
            apiEndpoint = '/company/notifications-api/unread-count';
        @elseif(auth()->user()->hasRole('client') || auth()->user()->hasRole('user'))
            apiEndpoint = '/client/notifications-api/unread-count';
        @endif
    @endauth

    fetch(apiEndpoint, {
        method: 'GET',
        headers: {
            'X-CSRF-TOKEN': csrfToken || '',
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        updateNotificationsBadge(data.count || 0);
    })
    .catch(error => {
        // Silently fail and retry in next interval
        console.log('Notifications temporarily unavailable');
    });
}

function loadRecentNotifications() {
    // Determine the correct API endpoint based on user role
    let apiEndpoint = '/admin/notifications-api/recent';
    @auth
        @if(auth()->user()->hasRole('company'))
            apiEndpoint = '/company/notifications-api/recent';
        @elseif(auth()->user()->hasRole('client') || auth()->user()->hasRole('user'))
            apiEndpoint = '/client/notifications-api/recent';
        @endif
    @endauth

    fetch(apiEndpoint, {
        method: 'GET',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
            'Content-Type': 'application/json',
            'Accept': 'application/json',
        },
    })
    .then(response => {
        if (!response.ok) {
            // If route doesn't exist, show no notifications message
            if (notificationsList) {
                notificationsList.innerHTML = `
                    <div class="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
                        <svg class="mx-auto h-8 w-8 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0"></path>
                        </svg>
                        <span class="text-sm">{{ __('No notifications') }}</span>
                    </div>
                `;
            }
            return { notifications: [] };
        }
        return response.json();
    })
    .then(data => {
        displayNotifications(data.notifications || []);
    })
    .catch(error => {
        console.log('Notifications not available:', error.message);
        if (notificationsList) {
            notificationsList.innerHTML = `
                <div class="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
                    <svg class="mx-auto h-8 w-8 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0"></path>
                    </svg>
                    <span class="text-sm">{{ __('No notifications') }}</span>
                </div>
            `;
        }
    });
}

function updateNotificationsBadge(count) {
    if (count > 0) {
        notificationsBadge.textContent = count > 99 ? '99+' : count;
        notificationsBadge.classList.remove('hidden');
    } else {
        notificationsBadge.classList.add('hidden');
    }
}

function displayNotifications(notifications) {
    if (notifications.length === 0) {
        notificationsList.innerHTML = `
            <div class="px-4 py-6 text-center text-gray-500 dark:text-gray-400">
                <svg class="mx-auto h-6 w-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0"></path>
                </svg>
                <span class="text-xs">{{ __('No notifications') }}</span>
            </div>
        `;
        return;
    }

    let html = '';
    // Limit to 6 notifications in dropdown
    const limitedNotifications = notifications.slice(0, 6);
    limitedNotifications.forEach(notification => {
        const priorityColor = notification.priority_color || 'blue';
        const isUnread = !notification.is_read;

        // Truncate long text
        const maxTitleLength = 40;
        const maxMessageLength = 60;
        const truncatedTitle = notification.title.length > maxTitleLength
            ? notification.title.substring(0, maxTitleLength) + '...'
            : notification.title;
        const truncatedMessage = notification.message.length > maxMessageLength
            ? notification.message.substring(0, maxMessageLength) + '...'
            : notification.message;

        html += `
            <div class="px-3 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-200 dark:border-gray-700 last:border-b-0 ${isUnread ? 'bg-blue-50 dark:bg-blue-900/50' : ''} cursor-pointer transition-colors duration-150"
                 onclick="${notification.action_url ? `window.location.href='${notification.action_url}'` : ''}">
                <div class="flex items-start space-x-2 rtl:space-x-reverse">
                    <div class="flex-shrink-0">
                        <div class="h-6 w-6 rounded-full bg-${priorityColor}-100 dark:bg-${priorityColor}-900 flex items-center justify-center">
                            ${getNotificationIcon(notification.icon, priorityColor)}
                        </div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-start justify-between">
                            <div class="flex-1 min-w-0">
                                <p class="text-xs font-medium text-gray-900 dark:text-white ${isUnread ? 'font-bold' : ''} leading-tight">
                                    ${truncatedTitle}
                                </p>
                                <p class="text-xs text-gray-600 dark:text-gray-400 mt-0.5 leading-tight">
                                    ${truncatedMessage}
                                </p>
                                <p class="text-xs text-gray-500 dark:text-gray-500 mt-0.5">
                                    ${notification.created_at}
                                </p>
                            </div>
                            ${isUnread ? '<div class="flex-shrink-0 ml-1"><div class="h-1.5 w-1.5 bg-blue-600 rounded-full"></div></div>' : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    notificationsList.innerHTML = html;

    // Update last updated time
    document.getElementById('last-updated').textContent = new Date().toLocaleTimeString();
}

function markAllAsRead() {
    // Determine the correct API endpoint based on user role
    let apiEndpoint = '/admin/notifications-api/mark-all-as-read';
    @auth
        @if(auth()->user()->hasRole('company'))
            apiEndpoint = '/company/notifications-api/mark-all-as-read';
        @elseif(auth()->user()->hasRole('client') || auth()->user()->hasRole('user'))
            apiEndpoint = '/client/notifications-api/mark-all-as-read';
        @endif
    @endauth

    fetch(apiEndpoint, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Refresh notifications
            loadNotificationsCount();
            loadRecentNotifications();
        }
    })
    .catch(error => {
        console.log('Error marking notifications as read:', error);
    });
}

function getNotificationIcon(icon, color) {
    const iconClass = `h-3 w-3 text-${color}-600 dark:text-${color}-400`;

    switch(icon) {
        case 'calendar':
            return `<svg class="${iconClass}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6-4h6"></path>
                    </svg>`;
        case 'credit-card':
            return `<svg class="${iconClass}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                    </svg>`;
        case 'star':
            return `<svg class="${iconClass}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                    </svg>`;
        default:
            return `<svg class="${iconClass}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0"></path>
                    </svg>`;
    }
}
</script>

