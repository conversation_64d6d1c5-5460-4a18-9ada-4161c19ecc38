@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ __('Payment Details') }}</h1>
                    <p class="mt-2 text-gray-600 dark:text-gray-400">{{ __('Booking') }} #{{ $booking->id }}</p>
                </div>
                
                <div class="flex items-center space-x-3">
                    <a href="{{ route('client.payments.index') }}" 
                       class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                        <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                        {{ __('Back to Payments') }}
                    </a>
                    
                    @if($booking->payment_status === 'paid' && $booking->transaction_id)
                        <a href="{{ route('client.payments.receipt', $booking->transaction_id) }}" 
                           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                            <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            {{ __('View Receipt') }}
                        </a>
                    @endif
                </div>
            </div>
        </div>

        <!-- Payment Status Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden mb-6">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">{{ __('Payment Status') }}</h2>
                    <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium 
                        @if($booking->payment_status === 'paid') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                        @elseif($booking->payment_status === 'pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                        @elseif($booking->payment_status === 'failed') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                        @else bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200 @endif">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            @if($booking->payment_status === 'paid')
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            @else
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            @endif
                        </svg>
                        {{ ucfirst($booking->payment_status) }}
                    </span>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Total Amount') }}</dt>
                        <dd class="text-2xl font-bold text-gray-900 dark:text-white">${{ number_format($booking->total_amount, 2) }}</dd>
                    </div>
                    
                    @if($booking->transaction_id)
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Transaction ID') }}</dt>
                            <dd class="text-sm font-mono text-gray-900 dark:text-white">{{ $booking->transaction_id }}</dd>
                        </div>
                    @endif

                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Payment Method') }}</dt>
                        <dd class="text-sm text-gray-900 dark:text-white">{{ ucfirst(str_replace('_', ' ', $booking->payment_method ?? __('Not specified'))) }}</dd>
                    </div>
                </div>
            </div>
        </div>

        <!-- Service Details -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden mb-6">
            <div class="p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">{{ __('Service Details') }}</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- Worker Information -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Worker Information') }}</h3>
                        <div class="space-y-3">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Name') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $booking->worker->name ?? __('Not specified') }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Category') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ ucfirst($booking->worker->category ?? __('Not specified')) }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Nationality') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $booking->worker->nationality ?? __('Not specified') }}</dd>
                            </div>
                        </div>
                    </div>

                    <!-- Company Information -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Company Information') }}</h3>
                        <div class="space-y-3">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Company Name') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $booking->company->company_name ?? __('Not specified') }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Rating') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">
                                    @if($booking->company && $booking->company->rating)
                                        <div class="flex items-center">
                                            <span class="mr-1">{{ number_format($booking->company->rating, 1) }}</span>
                                            <div class="flex">
                                                @for($i = 1; $i <= 5; $i++)
                                                    <svg class="w-4 h-4 {{ $i <= $booking->company->rating ? 'text-yellow-400' : 'text-gray-300' }}" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                    </svg>
                                                @endfor
                                            </div>
                                        </div>
                                    @else
                                        {{ __('Not rated') }}
                                    @endif
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Workers Count') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $booking->company->workers_count ?? 0 }} {{ __('workers') }}</dd>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Booking Timeline -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden mb-6">
            <div class="p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">{{ __('Booking Timeline') }}</h2>
                
                <div class="space-y-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-sm font-medium text-gray-900 dark:text-white">{{ __('Booking Created') }}</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $booking->created_at->format('F j, Y \a\t g:i A') }}</p>
                        </div>
                    </div>

                    @if($booking->payment_status === 'paid')
                        <div class="flex items-center">
                            <div class="flex-shrink-0 w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-sm font-medium text-gray-900 dark:text-white">{{ __('Payment Completed') }}</h3>
                                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $booking->updated_at->format('F j, Y \a\t g:i A') }}</p>
                            </div>
                        </div>
                    @endif

                    @if($booking->start_date)
                        <div class="flex items-center">
                            <div class="flex-shrink-0 w-8 h-8 {{ $booking->start_date->isPast() ? 'bg-green-100 dark:bg-green-900' : 'bg-gray-100 dark:bg-gray-700' }} rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 {{ $booking->start_date->isPast() ? 'text-green-600 dark:text-green-400' : 'text-gray-400' }}" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-sm font-medium text-gray-900 dark:text-white">
                                    {{ $booking->start_date->isPast() ? __('Service Completed') : __('Scheduled Service') }}
                                </h3>
                                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $booking->start_date->format('F j, Y \a\t g:i A') }}</p>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Additional Information -->
        @if($booking->notes)
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
                <div class="p-6">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">{{ __('Additional Notes') }}</h2>
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <p class="text-gray-700 dark:text-gray-300">{{ $booking->notes }}</p>
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
