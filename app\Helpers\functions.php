<?php

use App\Helpers\CurrencyHelper;

if (!function_exists('currency_format')) {
    /**
     * Format amount with currency
     */
    function currency_format($amount, $currency = null)
    {
        return CurrencyHelper::format($amount, $currency);
    }
}

if (!function_exists('currency_symbol')) {
    /**
     * Get currency symbol
     */
    function currency_symbol($currency = null)
    {
        return CurrencyHelper::getSymbol($currency);
    }
}

if (!function_exists('default_currency')) {
    /**
     * Get default currency
     */
    function default_currency()
    {
        return CurrencyHelper::getDefault();
    }
}

if (!function_exists('currency_format_locale')) {
    /**
     * Format amount with locale-aware positioning
     */
    function currency_format_locale($amount, $symbol = null)
    {
        return CurrencyHelper::formatWithLocale($amount, $symbol);
    }
}
