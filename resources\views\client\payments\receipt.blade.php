@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ __('Payment Receipt') }}</h1>
                    <p class="mt-2 text-gray-600 dark:text-gray-400">{{ __('Transaction ID') }}: {{ $booking->transaction_id }}</p>
                </div>

                <div class="flex items-center space-x-3">
                    <a href="{{ route('client.payments.index') }}"
                       class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                        <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                        {{ __('Back to Payments') }}
                    </a>

                    <button onclick="printReceipt('{{ route('client.payments.receipt.download', $booking->transaction_id) }}')"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                        <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        {{ __('Download PDF') }}
                    </button>
                </div>
            </div>
        </div>

        <!-- Receipt Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
            <div class="p-8">
                <!-- Receipt Header -->
                <div class="text-center mb-8 pb-6 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">{{ __('Payment Receipt') }}</h2>
                    <p class="text-gray-600 dark:text-gray-400">{{ __('Official receipt for your service booking') }}</p>

                    <!-- Status Badge -->
                    <div class="mt-4">
                        <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium
                            @if($booking->payment_status === 'paid') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                            @elseif($booking->payment_status === 'pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                            @elseif($booking->payment_status === 'failed') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                            @else bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200 @endif">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                @if($booking->payment_status === 'paid')
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                @else
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                @endif
                            </svg>
                            {{ ucfirst($booking->payment_status) }}
                        </span>
                    </div>
                </div>

                <!-- Receipt Details -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                    <!-- Customer Information -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">{{ __('Customer Information') }}</h3>
                        <div class="space-y-3">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Name') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $booking->client->user->name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Email') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $booking->client->user->email }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Phone') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $booking->client->phone ?? __('Not provided') }}</dd>
                            </div>
                        </div>
                    </div>

                    <!-- Service Information -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">{{ __('Service Information') }}</h3>
                        <div class="space-y-3">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Company') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $booking->company->company_name ?? __('Not specified') }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Worker') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ $booking->worker->name ?? __('Not specified') }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Service Type') }}</dt>
                                <dd class="text-sm text-gray-900 dark:text-white">{{ ucfirst($booking->booking_type) }}</dd>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Booking Details -->
                <div class="mb-8 p-6 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">{{ __('Booking Details') }}</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Booking ID') }}</dt>
                            <dd class="text-sm text-gray-900 dark:text-white">#{{ $booking->id }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Service Date') }}</dt>
                            <dd class="text-sm text-gray-900 dark:text-white">
                                {{ $booking->start_date ? $booking->start_date->format('F j, Y \a\t g:i A') : __('Not specified') }}
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Duration') }}</dt>
                            <dd class="text-sm text-gray-900 dark:text-white">
                                @if($booking->start_date && $booking->end_date)
                                    {{ $booking->start_date->diffInHours($booking->end_date) }} {{ __('hours') }}
                                @else
                                    {{ __('Not specified') }}
                                @endif
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Booking Status') }}</dt>
                            <dd class="text-sm">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    @if($booking->status === 'completed') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                    @elseif($booking->status === 'confirmed') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                    @elseif($booking->status === 'pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                    @else bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200 @endif">
                                    {{ ucfirst($booking->status) }}
                                </span>
                            </dd>
                        </div>
                    </div>
                </div>

                <!-- Payment Summary -->
                <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">{{ __('Payment Summary') }}</h3>

                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">{{ __('Service Amount') }}</span>
                            <span class="text-sm text-gray-900 dark:text-white">${{ number_format($booking->total_amount * 0.95, 2) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">{{ __('Service Fee') }} (5%)</span>
                            <span class="text-sm text-gray-900 dark:text-white">${{ number_format($booking->total_amount * 0.05, 2) }}</span>
                        </div>
                        <div class="border-t border-gray-200 dark:border-gray-600 pt-3">
                            <div class="flex justify-between">
                                <span class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('Total Amount') }}</span>
                                <span class="text-lg font-semibold text-gray-900 dark:text-white">${{ number_format($booking->total_amount, 2) }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Information -->
                <div class="mt-8 p-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-4">{{ __('Payment Information') }}</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <dt class="text-sm font-medium text-blue-700 dark:text-blue-300">{{ __('Transaction ID') }}</dt>
                            <dd class="text-sm text-blue-900 dark:text-blue-100 font-mono">{{ $booking->transaction_id }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-blue-700 dark:text-blue-300">{{ __('Payment Method') }}</dt>
                            <dd class="text-sm text-blue-900 dark:text-blue-100">{{ ucfirst(str_replace('_', ' ', $booking->payment_method ?? 'Not specified')) }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-blue-700 dark:text-blue-300">{{ __('Payment Date') }}</dt>
                            <dd class="text-sm text-blue-900 dark:text-blue-100">{{ $booking->updated_at->format('F j, Y \a\t g:i A') }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-blue-700 dark:text-blue-300">{{ __('Receipt Date') }}</dt>
                            <dd class="text-sm text-blue-900 dark:text-blue-100">{{ now()->format('F j, Y \a\t g:i A') }}</dd>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 text-center">
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        {{ __('Thank you for using our services!') }}
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-500 mt-2">
                        {{ __('This is an official receipt generated on') }} {{ now()->format('F j, Y \a\t g:i A') }}
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function printReceipt(url) {
    // Open the PDF view in a new window
    const printWindow = window.open(url, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');

    // Focus on the new window
    if (printWindow) {
        printWindow.focus();
    } else {
        // Fallback if popup is blocked
        alert('{{ __("Please allow popups to download the receipt") }}');
        window.location.href = url;
    }
}
</script>
@endsection
