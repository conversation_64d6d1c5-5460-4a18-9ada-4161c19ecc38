@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Page Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                    {{ __('Worker Details') }}: {{ $worker->name }}
                </h1>
                <div class="flex space-x-3">
                    <a href="{{ route('admin.workers.index') }}" 
                       class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-md transition duration-200">
                        <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        {{ __('Back to Workers') }}
                    </a>
                    @can('workers.edit')
                    <a href="{{ route('admin.workers.edit', $worker) }}" 
                       class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-200">
                        <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        {{ __('Edit Worker') }}
                    </a>
                    @endcan
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Worker Image and Basic Info -->
            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <!-- Worker Image -->
                        <div class="text-center mb-6">
                            @if($worker->image)
                                <img src="{{ asset('storage/' . $worker->image) }}" 
                                     alt="{{ $worker->name }}" 
                                     class="w-32 h-32 rounded-full mx-auto object-cover border-4 border-gray-200 dark:border-gray-600">
                            @else
                                <div class="w-32 h-32 rounded-full mx-auto bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
                                    <svg class="w-16 h-16 text-gray-400 dark:text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            @endif
                        </div>

                        <!-- Basic Info -->
                        <div class="text-center">
                            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $worker->name }}</h2>
                            <p class="text-lg text-gray-600 dark:text-gray-400">{{ $worker->category_text }}</p>
                            <div class="mt-4">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                    @if($worker->status === 'available') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                    @elseif($worker->status === 'booked') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                    @else bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 @endif">
                                    {{ $worker->status_text }}
                                </span>
                            </div>
                        </div>

                        <!-- Company Info -->
                        <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">{{ __('Company') }}</h3>
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0">
                                    @if($worker->company->logo)
                                        <img src="{{ asset('storage/' . $worker->company->logo) }}" 
                                             alt="{{ $worker->company->company_name }}" 
                                             class="w-10 h-10 rounded-full object-cover">
                                    @else
                                        <div class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                                            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 114 0 2 2 0 01-4 0zm8-1a1 1 0 100 2h2a1 1 0 100-2h-2z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                    @endif
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $worker->company->company_name }}</p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ __('Company') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed Information -->
            <div class="lg:col-span-2">
                <div class="space-y-6">
                    <!-- Personal Information -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Personal Information') }}</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Age') }}</label>
                                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $worker->age }} {{ __('years') }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Nationality') }}</label>
                                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $worker->nationality }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Added Date') }}</label>
                                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $worker->added_date->format('M d, Y') }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Status') }}</label>
                                    <p class="mt-1 text-sm text-gray-900 dark:text-white">
                                        @if($worker->is_active)
                                            <span class="text-green-600 dark:text-green-400">{{ __('Active') }}</span>
                                        @else
                                            <span class="text-red-600 dark:text-red-400">{{ __('Inactive') }}</span>
                                        @endif
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Skills and Languages -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Skills & Languages') }}</h3>
                            
                            <!-- Skills -->
                            @if($worker->skills)
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">{{ __('Skills') }}</label>
                                <div class="flex flex-wrap gap-2">
                                    @foreach($worker->skills as $skill)
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                            {{ __(ucfirst(str_replace('_', ' ', $skill))) }}
                                        </span>
                                    @endforeach
                                </div>
                            </div>
                            @endif

                            <!-- Languages -->
                            @if($worker->languages)
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">{{ __('Languages') }}</label>
                                <div class="flex flex-wrap gap-2">
                                    @foreach($worker->languages as $language)
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                            {{ __(ucfirst($language)) }}
                                        </span>
                                    @endforeach
                                </div>
                            </div>
                            @endif

                            <!-- Experience -->
                            @if($worker->experience)
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">{{ __('Experience') }}</label>
                                <div class="flex flex-wrap gap-2">
                                    @foreach($worker->experience as $exp)
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                            {{ __(str_replace('_', ' ', $exp)) }}
                                        </span>
                                    @endforeach
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Rates -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Rates') }}</h3>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Hourly') }}</p>
                                    <p class="text-lg font-bold text-gray-900 dark:text-white">${{ number_format($worker->hourly_rate, 2) }}</p>
                                </div>
                                <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Daily') }}</p>
                                    <p class="text-lg font-bold text-gray-900 dark:text-white">${{ number_format($worker->daily_rate, 2) }}</p>
                                </div>
                                <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Monthly') }}</p>
                                    <p class="text-lg font-bold text-gray-900 dark:text-white">${{ number_format($worker->monthly_rate, 2) }}</p>
                                </div>
                                <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Yearly') }}</p>
                                    <p class="text-lg font-bold text-gray-900 dark:text-white">${{ number_format($worker->yearly_rate, 2) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Rating -->
                    @if($worker->rating > 0)
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Rating') }}</h3>
                            <div class="flex items-center">
                                <div class="flex items-center">
                                    @for($i = 1; $i <= 5; $i++)
                                        @if($i <= $worker->rating)
                                            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                            </svg>
                                        @else
                                            <svg class="w-5 h-5 text-gray-300 dark:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                            </svg>
                                        @endif
                                    @endfor
                                </div>
                                <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">{{ number_format($worker->rating, 1) }}/5</span>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
