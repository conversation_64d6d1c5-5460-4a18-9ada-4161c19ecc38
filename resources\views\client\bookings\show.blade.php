@extends('layouts.app')

@section('title', __('Booking Details'))

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ __('Booking Details') }}</h1>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">{{ __('Booking #:id', ['id' => $booking->id]) }}</p>
                </div>
                <a href="{{ route('client.bookings.index') }}"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                    <svg class="w-4 h-4 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    {{ __('Back to Bookings') }}
                </a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Status Banner -->
        <div class="mb-6">
            @if($booking->status === 'pending')
                <div class="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
                    <div class="flex">
                        <svg class="w-5 h-5 text-yellow-400 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">{{ __('Booking Pending') }}</h3>
                            <p class="text-sm text-yellow-700 dark:text-yellow-300 mt-1">{{ __('Your booking is waiting for company approval.') }}</p>
                        </div>
                    </div>
                </div>
            @elseif($booking->status === 'confirmed')
                <div class="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700 rounded-lg p-4">
                    <div class="flex">
                        <svg class="w-5 h-5 text-green-400 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h3 class="text-sm font-medium text-green-800 dark:text-green-200">{{ __('Booking Confirmed') }}</h3>
                            <p class="text-sm text-green-700 dark:text-green-300 mt-1">{{ __('Your booking has been confirmed by the company.') }}</p>
                        </div>
                    </div>
                </div>
            @elseif($booking->status === 'completed')
                <div class="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
                    <div class="flex">
                        <svg class="w-5 h-5 text-blue-400 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <div>
                            <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">{{ __('Booking Completed') }}</h3>
                            <p class="text-sm text-blue-700 dark:text-blue-300 mt-1">{{ __('Your booking has been completed successfully.') }}</p>
                        </div>
                    </div>
                </div>
            @else
                <div class="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700 rounded-lg p-4">
                    <div class="flex">
                        <svg class="w-5 h-5 text-red-400 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        <div>
                            <h3 class="text-sm font-medium text-red-800 dark:text-red-200">{{ __('Booking Cancelled') }}</h3>
                            <p class="text-sm text-red-700 dark:text-red-300 mt-1">{{ __('This booking has been cancelled.') }}</p>
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <!-- Booking Details -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ __('Booking Information') }}</h3>
            </div>

            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Worker Information -->
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">{{ __('Worker Information') }}</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('Name') }}:</span>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $booking->worker->name }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('Nationality') }}:</span>
                                <span class="text-sm text-gray-900 dark:text-white">{{ $booking->worker->nationality }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('Experience') }}:</span>
                                <span class="text-sm text-gray-900 dark:text-white">{{ $booking->worker->experience_years }} {{ __('years') }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Company Information -->
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">{{ __('Company Information') }}</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('Company Name') }}:</span>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $booking->company->company_name }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('Phone') }}:</span>
                                <span class="text-sm text-gray-900 dark:text-white">{{ $booking->company->phone ?? __('Not provided') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('Email') }}:</span>
                                <span class="text-sm text-gray-900 dark:text-white">{{ $booking->company->user->email }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Booking Details -->
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">{{ __('Booking Details') }}</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('Booking Type') }}:</span>
                                <span class="text-sm text-gray-900 dark:text-white">{{ $booking->booking_type_text }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('Start Date') }}:</span>
                                <span class="text-sm text-gray-900 dark:text-white">{{ $booking->start_date->format('M d, Y H:i') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('End Date') }}:</span>
                                <span class="text-sm text-gray-900 dark:text-white">{{ $booking->end_date->format('M d, Y H:i') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('Total Amount') }}:</span>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">${{ number_format($booking->total_amount, 2) }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Status Information -->
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">{{ __('Status Information') }}</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('Status') }}:</span>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $booking->status_text }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('Payment Status') }}:</span>
                                <span class="text-sm text-gray-900 dark:text-white">{{ $booking->payment_status_text }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('Created') }}:</span>
                                <span class="text-sm text-gray-900 dark:text-white">{{ $booking->created_at->format('M d, Y H:i') }}</span>
                            </div>
                            @if($booking->confirmed_at)
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500 dark:text-gray-400">{{ __('Confirmed') }}:</span>
                                    <span class="text-sm text-gray-900 dark:text-white">{{ $booking->confirmed_at->format('M d, Y H:i') }}</span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Notes -->
                @if($booking->notes)
                    <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">{{ __('Notes') }}</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $booking->notes }}</p>
                    </div>
                @endif

                <!-- Cancellation/Rejection Reason -->
                @if($booking->cancellation_reason)
                    <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                        <h4 class="text-sm font-medium text-red-600 dark:text-red-400 mb-2">{{ __('Cancellation Reason') }}</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $booking->cancellation_reason }}</p>
                    </div>
                @endif

                @if($booking->rejection_reason)
                    <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                        <h4 class="text-sm font-medium text-red-600 dark:text-red-400 mb-2">{{ __('Rejection Reason') }}</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $booking->rejection_reason }}</p>
                    </div>
                @endif
            </div>

            <!-- Actions -->
            <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3 rtl:space-x-reverse">
                        @if(in_array($booking->status, ['pending', 'confirmed']))
                            <button onclick="showCancelModal({{ $booking->id }})"
                                    class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-md transition duration-200">
                                <svg class="w-4 h-4 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                {{ __('Cancel Booking') }}
                            </button>
                        @endif

                        @if($booking->status === 'completed' && !$booking->ratings->where('rater_id', auth()->id())->count())
                            <a href="{{ route('admin.ratings.create', ['booking_id' => $booking->id]) }}"
                               class="inline-flex items-center px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white text-sm font-medium rounded-md transition duration-200">
                                <svg class="w-4 h-4 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                </svg>
                                {{ __('Rate Service') }}
                            </a>
                        @endif
                    </div>

                    <a href="{{ route('client.bookings.create', ['worker_id' => $booking->worker_id]) }}"
                       class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition duration-200">
                        <svg class="w-4 h-4 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        {{ __('Book Again') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cancel Modal -->
<div id="cancelModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white text-center">{{ __('Cancel Booking') }}</h3>
            <form id="cancelForm" method="POST" action="{{ route('client.bookings.cancel', $booking) }}" class="mt-4">
                @csrf
                <div class="mb-4">
                    <label for="cancellation_reason" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {{ __('Cancellation Reason') }} <span class="text-red-500">*</span>
                    </label>
                    <textarea name="cancellation_reason" id="cancellation_reason" rows="3" required
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                              placeholder="{{ __('Please provide a reason for cancellation...') }}"></textarea>
                </div>
                <div class="flex items-center justify-end space-x-3 rtl:space-x-reverse">
                    <button type="button" onclick="hideCancelModal()"
                            class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 text-sm font-medium rounded-md">
                        {{ __('Cancel') }}
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-md">
                        {{ __('Confirm Cancellation') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showCancelModal(bookingId) {
    document.getElementById('cancelModal').classList.remove('hidden');
}

function hideCancelModal() {
    document.getElementById('cancelModal').classList.add('hidden');
    document.getElementById('cancellation_reason').value = '';
}

// Close modal when clicking outside
document.getElementById('cancelModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideCancelModal();
    }
});
</script>
@endsection
