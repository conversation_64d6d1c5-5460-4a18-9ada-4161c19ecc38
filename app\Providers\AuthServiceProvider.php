<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    protected $policies = [
        // تسجيل السياسات هنا
    ];

    public function boot(): void
    {
        $this->registerPolicies();

        // إضافة gate للمسؤول - يمنح المسؤول جميع الصلاحيات
        Gate::before(function ($user, $ability) {
            if ($user->hasRole('admin')) {
                return true;
            }
        });

        // Gates إضافية للتحكم في الوصول
        Gate::define('access-admin-panel', function ($user) {
            return $user->hasAnyRole(['admin', 'editor']);
        });

        Gate::define('manage-system-settings', function ($user) {
            return $user->hasRole('admin');
        });
    }
}
