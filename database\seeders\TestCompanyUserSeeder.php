<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Company;
use Illuminate\Support\Facades\Hash;

class TestCompanyUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء مستخدم شركة تجريبي
        $companyUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'شركة تجريبية',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );

        // إعطاء دور الشركة
        $companyUser->assignRole('company');

        // إنشاء سجل الشركة
        $company = Company::firstOrCreate(
            ['user_id' => $companyUser->id],
            [
                'company_name' => 'شركة الخدمات المنزلية التجريبية',
                'description' => 'شركة تجريبية لاختبار النظام',
                'commercial_register' => 'CR-TEST-123456',
                'contact_info' => [
                    'phone' => '**********',
                    'email' => '<EMAIL>',
                    'address' => 'الرياض، المملكة العربية السعودية',
                    'website' => 'https://test-company.com'
                ],
                'join_date' => now(),
                'bank_info' => [
                    'bank_name' => 'البنك الأهلي السعودي',
                    'account_number' => '*********',
                    'iban' => 'SA*********0*********'
                ],
                'is_active' => true,
                'rating' => 4.5,
                'workers_count' => 0,
            ]
        );

        $this->command->info('تم إنشاء مستخدم الشركة التجريبي بنجاح!');
        $this->command->info('البريد الإلكتروني: <EMAIL>');
        $this->command->info('كلمة المرور: password');
    }
}
