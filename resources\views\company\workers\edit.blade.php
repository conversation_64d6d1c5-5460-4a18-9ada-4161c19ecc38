@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Page Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ __('Edit Worker') }}</h1>
                    <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">{{ __('Edit worker information') }}</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('company.workers.index') }}"
                       class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-md transition duration-200">
                        <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        {{ __('Back to Workers') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Success/Error Messages -->
        @if(session('success'))
            <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative dark:bg-green-800 dark:border-green-600 dark:text-green-200" role="alert">
                <span class="block sm:inline">{{ session('success') }}</span>
            </div>
        @endif

        @if(session('error'))
            <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative dark:bg-red-800 dark:border-red-600 dark:text-red-200" role="alert">
                <span class="block sm:inline">{{ session('error') }}</span>
            </div>
        @endif

        @if($errors->any())
            <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative dark:bg-red-800 dark:border-red-600 dark:text-red-200" role="alert">
                <ul class="list-disc list-inside">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div class="px-6 py-6">
                <form method="POST" action="{{ route('company.workers.update', $worker) }}" enctype="multipart/form-data" class="space-y-6">
                    @csrf
                    @method('PUT')

                    <!-- Personal Information -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Personal Information') }}</h3>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- Name -->
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Full Name') }} <span class="text-red-500">*</span></label>
                                <input type="text" name="name" id="name" value="{{ old('name', $worker->name) }}" required
                                       class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('name') border-red-500 @enderror">
                                @error('name')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Nationality -->
                            <div>
                                <label for="nationality" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Nationality') }} <span class="text-red-500">*</span></label>
                                <select name="nationality" id="nationality" required
                                        class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('nationality') border-red-500 @enderror">
                                    <option value="">{{ __('Select Nationality') }}</option>
                                    @foreach($nationalities as $nationality)
                                        <option value="{{ $nationality->name_en }}" {{ old('nationality', $worker->nationality) == $nationality->name_en ? 'selected' : '' }}>{{ $nationality->name }}</option>
                                    @endforeach
                                </select>
                                @error('nationality')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Age -->
                            <div>
                                <label for="age" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Age') }} <span class="text-red-500">*</span></label>
                                <input type="number" name="age" id="age" value="{{ old('age', $worker->age) }}" min="18" max="65" required
                                       class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('age') border-red-500 @enderror">
                                @error('age')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Category -->
                            <div>
                                <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Category') }} <span class="text-red-500">*</span></label>
                                <select name="category" id="category" required
                                        class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('category') border-red-500 @enderror">
                                    <option value="">{{ __('Select Category') }}</option>
                                    <option value="Housemaid" {{ old('category', $worker->category) == 'Housemaid' ? 'selected' : '' }}>{{ __('Housemaid') }}</option>
                                    <option value="Nanny" {{ old('category', $worker->category) == 'Nanny' ? 'selected' : '' }}>{{ __('Nanny') }}</option>
                                    <option value="Elderly Care" {{ old('category', $worker->category) == 'Elderly Care' ? 'selected' : '' }}>{{ __('Elderly Care') }}</option>
                                    <option value="Cook" {{ old('category', $worker->category) == 'Cook' ? 'selected' : '' }}>{{ __('Cook') }}</option>
                                    <option value="Cleaner" {{ old('category', $worker->category) == 'Cleaner' ? 'selected' : '' }}>{{ __('Cleaner') }}</option>
                                    <option value="Driver" {{ old('category', $worker->category) == 'Driver' ? 'selected' : '' }}>{{ __('Driver') }}</option>
                                    <option value="Gardener" {{ old('category', $worker->category) == 'Gardener' ? 'selected' : '' }}>{{ __('Gardener') }}</option>
                                </select>
                                @error('category')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Experience Years -->
                            <div>
                                <label for="experience_years" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Experience Years') }} <span class="text-red-500">*</span></label>
                                <input type="number" name="experience_years" id="experience_years" value="{{ old('experience_years', $worker->experience_years) }}" min="0" max="50" required
                                       class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('experience_years') border-red-500 @enderror">
                                @error('experience_years')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Languages -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ __('Languages') }}</label>
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                                    @php
                                        $languages = [
                                            'arabic' => __('Arabic'),
                                            'english' => __('English'),
                                            'french' => __('French'),
                                            'spanish' => __('Spanish'),
                                            'german' => __('German'),
                                            'italian' => __('Italian'),
                                            'urdu' => __('Urdu'),
                                            'hindi' => __('Hindi'),
                                            'bengali' => __('Bengali'),
                                            'tagalog' => __('Tagalog'),
                                            'indonesian' => __('Indonesian'),
                                            'thai' => __('Thai'),
                                            'chinese' => __('Chinese'),
                                            'japanese' => __('Japanese'),
                                            'korean' => __('Korean'),
                                            'russian' => __('Russian'),
                                            'portuguese' => __('Portuguese'),
                                            'dutch' => __('Dutch'),
                                            'turkish' => __('Turkish'),
                                            'persian' => __('Persian')
                                        ];
                                        $workerLanguages = is_array($worker->languages) ? $worker->languages : [];
                                    @endphp
                                    @foreach($languages as $code => $name)
                                        <label class="flex items-center">
                                            <input type="checkbox"
                                                   name="languages[]"
                                                   value="{{ $code }}"
                                                   {{ in_array($code, old('languages', $workerLanguages)) ? 'checked' : '' }}
                                                   class="rounded border-gray-300 dark:border-gray-600 dark:bg-gray-700 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                            <span class="{{ app()->getLocale() === 'ar' ? 'mr-2' : 'ml-2' }} text-sm text-gray-700 dark:text-gray-300">{{ $name }}</span>
                                        </label>
                                    @endforeach
                                </div>
                                @error('languages')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Skills -->
                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ __('Skills') }}</label>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                                @php
                                    $skills = [
                                        'cleaning' => __('Cleaning'),
                                        'cooking' => __('Cooking'),
                                        'child_care' => __('Child Care'),
                                        'elderly_care' => __('Elderly Care'),
                                        'laundry' => __('Laundry'),
                                        'ironing' => __('Ironing'),
                                        'gardening' => __('Gardening'),
                                        'pet_care' => __('Pet Care'),
                                        'driving' => __('Driving'),
                                        'tutoring' => __('Tutoring'),
                                        'nursing' => __('Nursing'),
                                        'physiotherapy' => __('Physiotherapy'),
                                        'massage' => __('Massage'),
                                        'hairdressing' => __('Hairdressing'),
                                        'sewing' => __('Sewing'),
                                        'computer_skills' => __('Computer Skills'),
                                        'first_aid' => __('First Aid'),
                                        'swimming' => __('Swimming'),
                                        'music' => __('Music'),
                                        'art_crafts' => __('Art & Crafts'),
                                        'organization' => __('Organization'),
                                        'shopping' => __('Shopping'),
                                        'meal_planning' => __('Meal Planning'),
                                        'baby_care' => __('Baby Care'),
                                        'special_needs_care' => __('Special Needs Care')
                                    ];
                                    $workerSkills = is_array($worker->skills) ? $worker->skills : [];
                                @endphp
                                @foreach($skills as $code => $name)
                                    <label class="flex items-center">
                                        <input type="checkbox"
                                               name="skills[]"
                                               value="{{ $code }}"
                                               {{ in_array($code, old('skills', $workerSkills)) ? 'checked' : '' }}
                                               class="rounded border-gray-300 dark:border-gray-600 dark:bg-gray-700 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        <span class="{{ app()->getLocale() === 'ar' ? 'mr-2' : 'ml-2' }} text-sm text-gray-700 dark:text-gray-300">{{ $name }}</span>
                                    </label>
                                @endforeach
                            </div>
                            @error('skills')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div class="mt-4">
                            <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Description') }}</label>
                            <textarea name="description" id="description" rows="3"
                                      placeholder="{{ __('Brief description about the worker...') }}"
                                      class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('description') border-red-500 @enderror">{{ old('description', $worker->description) }}</textarea>
                            @error('description')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Pricing Information -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Pricing Information') }}</h3>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- Hourly Rate -->
                            <div>
                                <label for="hourly_rate" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Hourly Rate') }} ($) <span class="text-red-500">*</span></label>
                                <input type="number" name="hourly_rate" id="hourly_rate" value="{{ old('hourly_rate', $worker->hourly_rate) }}" step="0.01" min="0" required
                                       class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('hourly_rate') border-red-500 @enderror">
                                @error('hourly_rate')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Monthly Rate -->
                            <div>
                                <label for="monthly_rate" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Monthly Rate') }} ($) <span class="text-red-500">*</span></label>
                                <input type="number" name="monthly_rate" id="monthly_rate" value="{{ old('monthly_rate', $worker->monthly_rate) }}" step="0.01" min="0" required
                                       class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('monthly_rate') border-red-500 @enderror">
                                @error('monthly_rate')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Documents and Photos -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Documents and Photos') }}</h3>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <!-- Worker Photo -->
                            <div>
                                <label for="photo" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Worker Photo') }}</label>
                                @if($worker->photo)
                                    <div class="mb-2">
                                        <img src="{{ asset('storage/' . $worker->photo) }}" alt="{{ $worker->name }}" class="w-20 h-20 object-cover rounded">
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ __('Current Photo') }}</p>
                                    </div>
                                @endif
                                <input type="file" name="photo" id="photo" accept="image/*"
                                       class="mt-1 block w-full text-sm text-gray-500 dark:text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 @error('photo') border-red-500 @enderror">
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">{{ __('JPEG, PNG, JPG (Max: 2MB)') }}</p>
                                @error('photo')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Passport Photo -->
                            <div>
                                <label for="passport_photo" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Passport Photo') }}</label>
                                @if($worker->passport_photo)
                                    <div class="mb-2">
                                        <a href="{{ asset('storage/' . $worker->passport_photo) }}" target="_blank" class="text-blue-600 hover:text-blue-800 text-xs">{{ __('View Current File') }}</a>
                                    </div>
                                @endif
                                <input type="file" name="passport_photo" id="passport_photo" accept="image/*,.pdf"
                                       class="mt-1 block w-full text-sm text-gray-500 dark:text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 @error('passport_photo') border-red-500 @enderror">
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">{{ __('JPEG, PNG, JPG, PDF (Max: 2MB)') }}</p>
                                @error('passport_photo')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- CV File -->
                            <div>
                                <label for="cv_file" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('CV File') }}</label>
                                @if($worker->cv_file)
                                    <div class="mb-2">
                                        <a href="{{ asset('storage/' . $worker->cv_file) }}" target="_blank" class="text-blue-600 hover:text-blue-800 text-xs">{{ __('View Current CV') }}</a>
                                    </div>
                                @endif
                                <input type="file" name="cv_file" id="cv_file" accept=".pdf,.doc,.docx"
                                       class="mt-1 block w-full text-sm text-gray-500 dark:text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 @error('cv_file') border-red-500 @enderror">
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">{{ __('PDF, DOC, DOCX (Max: 5MB)') }}</p>
                                @error('cv_file')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Status -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Status') }}</h3>

                        <div class="flex items-center">
                            <input type="checkbox" name="is_active" id="is_active" value="1" {{ old('is_active', $worker->is_active) ? 'checked' : '' }}
                                   class="rounded border-gray-300 dark:border-gray-600 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <label for="is_active" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                {{ __('Worker is active and available for bookings') }}
                            </label>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="flex justify-end space-x-3 rtl:space-x-reverse pt-6 border-t border-gray-200 dark:border-gray-700">
                        <a href="{{ route('company.workers.index') }}"
                           class="inline-flex items-center px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium rounded-md transition duration-200">
                            {{ __('Cancel') }}
                        </a>
                        <button type="submit"
                                class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-200">
                            <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            {{ __('Update Worker') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
