<?php

namespace App\Http\Controllers\Company;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Schedule;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Carbon\Carbon;

class BookingController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (!auth()->user()->hasRole('company')) {
                abort(403, 'Access denied. Company role required.');
            }
            return $next($request);
        });
        $this->notificationService = $notificationService;
    }

    /**
     * Display a listing of company bookings.
     */
    public function index(Request $request)
    {
        $company = auth()->user()->company;

        if (!$company) {
            return redirect()->route('company.dashboard')->with('error', __('Company not found.'));
        }

        $query = Booking::with(['client.user', 'worker'])
                       ->whereHas('worker', function($q) use ($company) {
                           $q->where('company_id', $company->id);
                       });

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->whereHas('client.user', function($subQ) use ($search) {
                    $subQ->where('name', 'like', "%{$search}%")
                         ->orWhere('email', 'like', "%{$search}%");
                })
                ->orWhereHas('worker', function($subQ) use ($search) {
                    $subQ->where('name', 'like', "%{$search}%");
                })
                ->orWhere('id', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by worker
        if ($request->filled('worker_id')) {
            $query->where('worker_id', $request->worker_id);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->where('start_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->where('end_date', '<=', $request->date_to);
        }

        $bookings = $query->orderBy('created_at', 'desc')->paginate(15);

        // Get workers for filter
        $workers = $company->workers()->where('is_active', true)->get();

        // Get booking statistics
        $stats = [
            'total' => Booking::whereHas('worker', function($q) use ($company) {
                $q->where('company_id', $company->id);
            })->count(),
            'pending' => Booking::whereHas('worker', function($q) use ($company) {
                $q->where('company_id', $company->id);
            })->where('status', 'pending')->count(),
            'confirmed' => Booking::whereHas('worker', function($q) use ($company) {
                $q->where('company_id', $company->id);
            })->where('status', 'confirmed')->count(),
            'completed' => Booking::whereHas('worker', function($q) use ($company) {
                $q->where('company_id', $company->id);
            })->where('status', 'completed')->count(),
            'cancelled' => Booking::whereHas('worker', function($q) use ($company) {
                $q->where('company_id', $company->id);
            })->where('status', 'cancelled')->count(),
        ];

        return view('company.bookings.index', compact('bookings', 'workers', 'stats'));
    }

    /**
     * Display the specified booking.
     */
    public function show(Booking $booking)
    {
        $company = auth()->user()->company;

        if (!$company || $booking->worker->company_id !== $company->id) {
            abort(403, 'Access denied.');
        }

        $booking->load(['client.user', 'worker', 'ratings']);

        return view('company.bookings.show', compact('booking'));
    }

    /**
     * Approve a booking.
     */
    public function approve(Request $request, Booking $booking)
    {
        $company = auth()->user()->company;

        if (!$company || $booking->worker->company_id !== $company->id) {
            abort(403, 'Access denied.');
        }

        if ($booking->status !== 'pending') {
            return back()->withErrors(['error' => __('Only pending bookings can be approved.')]);
        }

        try {
            // Update booking status
            $booking->update([
                'status' => 'confirmed',
                'confirmed_at' => now(),
                'confirmed_by' => auth()->id(),
            ]);

            // Create schedule entry for the booking
            Schedule::create([
                'worker_id' => $booking->worker_id,
                'booking_id' => $booking->id,
                'start_date' => $booking->start_date,
                'end_date' => $booking->end_date,
                'schedule_type' => 'booked',
                'notes' => 'Auto-created from booking approval',
            ]);

            // Send notification to client
            $this->notificationService->send($booking->client->user, [
                'type' => 'booking_confirmed',
                'title' => __('Booking Confirmed'),
                'message' => __('Your booking #:id has been confirmed by :company. Worker: :worker', [
                    'id' => $booking->id,
                    'company' => $booking->company->company_name,
                    'worker' => $booking->worker->name,
                ]),
                'notifiable_type' => get_class($booking),
                'notifiable_id' => $booking->id,
                'priority' => 'high',
                'action_url' => route('client.bookings.show', $booking),
                'action_text' => __('View Booking Details'),
                'icon' => 'check-circle',
                'color' => 'green',
                'data' => [
                    'booking_id' => $booking->id,
                    'company_name' => $booking->company->company_name,
                    'worker_name' => $booking->worker->name,
                    'start_date' => $booking->start_date->format('Y-m-d H:i'),
                    'end_date' => $booking->end_date->format('Y-m-d H:i'),
                ],
            ]);

            return back()->with('success', __('Booking approved successfully.'));

        } catch (\Exception $e) {
            return back()->withErrors(['error' => __('Failed to approve booking. Please try again.')]);
        }
    }

    /**
     * Reject a booking.
     */
    public function reject(Request $request, Booking $booking)
    {
        $company = auth()->user()->company;

        if (!$company || $booking->worker->company_id !== $company->id) {
            abort(403, 'Access denied.');
        }

        if ($booking->status !== 'pending') {
            return back()->withErrors(['error' => __('Only pending bookings can be rejected.')]);
        }

        $request->validate([
            'rejection_reason' => 'required|string|max:500',
        ]);

        try {
            // Update booking status
            $booking->update([
                'status' => 'cancelled',
                'rejection_reason' => $request->rejection_reason,
                'rejected_at' => now(),
                'rejected_by' => auth()->id(),
            ]);

            // Send notification to client
            $this->notificationService->send($booking->client->user, [
                'type' => 'booking_rejected',
                'title' => __('Booking Rejected'),
                'message' => __('Your booking #:id has been rejected by :company. Reason: :reason', [
                    'id' => $booking->id,
                    'company' => $booking->company->company_name,
                    'reason' => $request->rejection_reason,
                ]),
                'notifiable_type' => get_class($booking),
                'notifiable_id' => $booking->id,
                'priority' => 'high',
                'action_url' => route('client.bookings.show', $booking),
                'action_text' => __('View Booking Details'),
                'icon' => 'x-circle',
                'color' => 'red',
                'data' => [
                    'booking_id' => $booking->id,
                    'company_name' => $booking->company->company_name,
                    'worker_name' => $booking->worker->name,
                    'rejection_reason' => $request->rejection_reason,
                ],
            ]);

            return back()->with('success', __('Booking rejected.'));

        } catch (\Exception $e) {
            return back()->withErrors(['error' => __('Failed to reject booking. Please try again.')]);
        }
    }

    /**
     * Mark booking as completed.
     */
    public function complete(Booking $booking)
    {
        $company = auth()->user()->company;

        if (!$company || $booking->worker->company_id !== $company->id) {
            abort(403, 'Access denied.');
        }

        if ($booking->status !== 'confirmed') {
            return back()->withErrors(['error' => __('Only confirmed bookings can be completed.')]);
        }

        try {
            $booking->update([
                'status' => 'completed',
                'completed_at' => now(),
            ]);

            // Send notification to client
            $this->notificationService->send($booking->client->user, [
                'type' => 'booking_completed',
                'title' => __('Booking Completed'),
                'message' => __('Your booking #:id has been completed by :company. Worker: :worker', [
                    'id' => $booking->id,
                    'company' => $booking->company->company_name,
                    'worker' => $booking->worker->name,
                ]),
                'notifiable_type' => get_class($booking),
                'notifiable_id' => $booking->id,
                'priority' => 'medium',
                'action_url' => route('client.bookings.show', $booking),
                'action_text' => __('View Booking Details'),
                'icon' => 'check-circle',
                'color' => 'green',
                'data' => [
                    'booking_id' => $booking->id,
                    'company_name' => $booking->company->company_name,
                    'worker_name' => $booking->worker->name,
                    'completed_at' => now()->format('Y-m-d H:i'),
                ],
            ]);

            return back()->with('success', __('Booking marked as completed.'));

        } catch (\Exception $e) {
            return back()->withErrors(['error' => __('Failed to complete booking. Please try again.')]);
        }
    }

    /**
     * Get booking analytics data.
     */
    public function analytics()
    {
        $company = auth()->user()->company;

        // Monthly bookings for the last 12 months
        $monthlyBookings = [];
        for ($i = 11; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            $count = Booking::whereHas('worker', function($q) use ($company) {
                $q->where('company_id', $company->id);
            })->whereYear('created_at', $month->year)
              ->whereMonth('created_at', $month->month)
              ->count();

            $monthlyBookings[] = [
                'month' => $month->format('M Y'),
                'count' => $count
            ];
        }

        // Revenue by month
        $monthlyRevenue = [];
        for ($i = 11; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            $revenue = Booking::whereHas('worker', function($q) use ($company) {
                $q->where('company_id', $company->id);
            })->whereYear('created_at', $month->year)
              ->whereMonth('created_at', $month->month)
              ->where('status', 'completed')
              ->sum('total_amount');

            $monthlyRevenue[] = [
                'month' => $month->format('M Y'),
                'revenue' => $revenue
            ];
        }

        // Top workers by bookings
        $topWorkers = Booking::whereHas('worker', function($q) use ($company) {
            $q->where('company_id', $company->id);
        })->selectRaw('worker_id, COUNT(*) as booking_count')
          ->with('worker')
          ->groupBy('worker_id')
          ->orderBy('booking_count', 'desc')
          ->limit(5)
          ->get();

        return response()->json([
            'monthlyBookings' => $monthlyBookings,
            'monthlyRevenue' => $monthlyRevenue,
            'topWorkers' => $topWorkers,
        ]);
    }
}
