<?php

namespace App\Http\Controllers\Company;

use App\Http\Controllers\Controller;
use App\Models\Schedule;
use App\Models\Worker;
use App\Models\Booking;
use Illuminate\Http\Request;
use Carbon\Carbon;

class ScheduleController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (!auth()->user()->hasRole('company')) {
                abort(403, 'Access denied. Company role required.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of company schedules.
     */
    public function index(Request $request)
    {
        $company = auth()->user()->company;
        
        if (!$company) {
            return redirect()->route('company.dashboard')->with('error', __('Company not found.'));
        }

        $query = Schedule::with(['worker', 'booking'])
                        ->whereHas('worker', function($q) use ($company) {
                            $q->where('company_id', $company->id);
                        });

        // Filter by worker
        if ($request->filled('worker_id')) {
            $query->where('worker_id', $request->worker_id);
        }

        // Filter by schedule type
        if ($request->filled('schedule_type')) {
            $query->where('schedule_type', $request->schedule_type);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->where('start_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->where('end_date', '<=', $request->date_to);
        }

        // Default to current month if no filters
        if (!$request->filled('date_from') && !$request->filled('date_to')) {
            $query->whereBetween('start_date', [
                Carbon::now()->startOfMonth(),
                Carbon::now()->endOfMonth()
            ]);
        }

        $schedules = $query->orderBy('start_date', 'asc')->paginate(20);

        // Get workers for filter
        $workers = $company->workers()->where('is_active', true)->get();

        // Get schedule statistics
        $stats = [
            'total' => Schedule::whereHas('worker', function($q) use ($company) {
                $q->where('company_id', $company->id);
            })->count(),
            'available' => Schedule::whereHas('worker', function($q) use ($company) {
                $q->where('company_id', $company->id);
            })->where('schedule_type', 'available')->count(),
            'booked' => Schedule::whereHas('worker', function($q) use ($company) {
                $q->where('company_id', $company->id);
            })->where('schedule_type', 'booked')->count(),
            'vacation' => Schedule::whereHas('worker', function($q) use ($company) {
                $q->where('company_id', $company->id);
            })->where('schedule_type', 'vacation')->count(),
        ];

        return view('company.schedules.index', compact('schedules', 'workers', 'stats'));
    }

    /**
     * Show the form for creating a new schedule.
     */
    public function create()
    {
        $company = auth()->user()->company;
        
        if (!$company) {
            return redirect()->route('company.dashboard')->with('error', __('Company not found.'));
        }

        $workers = $company->workers()->where('is_active', true)->get();
        $bookings = Booking::whereHas('worker', function($q) use ($company) {
            $q->where('company_id', $company->id);
        })->where('status', 'confirmed')->get();

        return view('company.schedules.create', compact('workers', 'bookings'));
    }

    /**
     * Store a newly created schedule.
     */
    public function store(Request $request)
    {
        $company = auth()->user()->company;
        
        if (!$company) {
            return redirect()->route('company.dashboard')->with('error', __('Company not found.'));
        }

        $validated = $request->validate([
            'worker_id' => 'required|exists:workers,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'schedule_type' => 'required|in:available,booked,vacation',
            'booking_id' => 'nullable|exists:bookings,id',
            'notes' => 'nullable|string',
        ]);

        // Verify worker belongs to company
        $worker = Worker::find($validated['worker_id']);
        if (!$worker || $worker->company_id !== $company->id) {
            return back()->withErrors(['worker_id' => __('Invalid worker selected.')]);
        }

        // Check for schedule conflicts
        $conflicts = Schedule::where('worker_id', $validated['worker_id'])
                           ->where(function($query) use ($validated) {
                               $query->whereBetween('start_date', [$validated['start_date'], $validated['end_date']])
                                     ->orWhereBetween('end_date', [$validated['start_date'], $validated['end_date']])
                                     ->orWhere(function($q) use ($validated) {
                                         $q->where('start_date', '<=', $validated['start_date'])
                                           ->where('end_date', '>=', $validated['end_date']);
                                     });
                           })
                           ->exists();

        if ($conflicts) {
            return back()->withErrors(['error' => __('Schedule conflicts with existing schedule.')]);
        }

        try {
            Schedule::create($validated);

            return redirect()->route('company.schedules.index')
                           ->with('success', __('Schedule created successfully.'));

        } catch (\Exception $e) {
            return back()->withInput()->withErrors(['error' => __('Failed to create schedule. Please try again.')]);
        }
    }

    /**
     * Display the specified schedule.
     */
    public function show(Schedule $schedule)
    {
        $company = auth()->user()->company;
        
        if (!$company || $schedule->worker->company_id !== $company->id) {
            abort(403, 'Access denied.');
        }

        $schedule->load(['worker', 'booking.client.user']);

        return view('company.schedules.show', compact('schedule'));
    }

    /**
     * Show the form for editing the specified schedule.
     */
    public function edit(Schedule $schedule)
    {
        $company = auth()->user()->company;
        
        if (!$company || $schedule->worker->company_id !== $company->id) {
            abort(403, 'Access denied.');
        }

        $workers = $company->workers()->where('is_active', true)->get();
        $bookings = Booking::whereHas('worker', function($q) use ($company) {
            $q->where('company_id', $company->id);
        })->where('status', 'confirmed')->get();

        return view('company.schedules.edit', compact('schedule', 'workers', 'bookings'));
    }

    /**
     * Update the specified schedule.
     */
    public function update(Request $request, Schedule $schedule)
    {
        $company = auth()->user()->company;
        
        if (!$company || $schedule->worker->company_id !== $company->id) {
            abort(403, 'Access denied.');
        }

        $validated = $request->validate([
            'worker_id' => 'required|exists:workers,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'schedule_type' => 'required|in:available,booked,vacation',
            'booking_id' => 'nullable|exists:bookings,id',
            'notes' => 'nullable|string',
        ]);

        // Verify worker belongs to company
        $worker = Worker::find($validated['worker_id']);
        if (!$worker || $worker->company_id !== $company->id) {
            return back()->withErrors(['worker_id' => __('Invalid worker selected.')]);
        }

        // Check for schedule conflicts (excluding current schedule)
        $conflicts = Schedule::where('worker_id', $validated['worker_id'])
                           ->where('id', '!=', $schedule->id)
                           ->where(function($query) use ($validated) {
                               $query->whereBetween('start_date', [$validated['start_date'], $validated['end_date']])
                                     ->orWhereBetween('end_date', [$validated['start_date'], $validated['end_date']])
                                     ->orWhere(function($q) use ($validated) {
                                         $q->where('start_date', '<=', $validated['start_date'])
                                           ->where('end_date', '>=', $validated['end_date']);
                                     });
                           })
                           ->exists();

        if ($conflicts) {
            return back()->withErrors(['error' => __('Schedule conflicts with existing schedule.')]);
        }

        try {
            $schedule->update($validated);

            return redirect()->route('company.schedules.show', $schedule)
                           ->with('success', __('Schedule updated successfully.'));

        } catch (\Exception $e) {
            return back()->withInput()->withErrors(['error' => __('Failed to update schedule. Please try again.')]);
        }
    }

    /**
     * Remove the specified schedule.
     */
    public function destroy(Schedule $schedule)
    {
        $company = auth()->user()->company;
        
        if (!$company || $schedule->worker->company_id !== $company->id) {
            abort(403, 'Access denied.');
        }

        try {
            // Check if schedule is linked to a booking
            if ($schedule->booking_id && $schedule->booking->status === 'confirmed') {
                return back()->withErrors(['error' => __('Cannot delete schedule linked to confirmed booking.')]);
            }

            $schedule->delete();

            return redirect()->route('company.schedules.index')
                           ->with('success', __('Schedule deleted successfully.'));

        } catch (\Exception $e) {
            return back()->withErrors(['error' => __('Failed to delete schedule. Please try again.')]);
        }
    }

    /**
     * Get calendar view data.
     */
    public function calendar(Request $request)
    {
        $company = auth()->user()->company;
        
        $start = $request->get('start', Carbon::now()->startOfMonth());
        $end = $request->get('end', Carbon::now()->endOfMonth());

        $schedules = Schedule::with(['worker', 'booking'])
                           ->whereHas('worker', function($q) use ($company) {
                               $q->where('company_id', $company->id);
                           })
                           ->whereBetween('start_date', [$start, $end])
                           ->get();

        $events = $schedules->map(function($schedule) {
            $color = match($schedule->schedule_type) {
                'available' => '#10B981', // Green
                'booked' => '#3B82F6',    // Blue
                'vacation' => '#EF4444',  // Red
                default => '#6B7280'      // Gray
            };

            return [
                'id' => $schedule->id,
                'title' => $schedule->worker->name . ' - ' . __(ucfirst($schedule->schedule_type)),
                'start' => $schedule->start_date->toISOString(),
                'end' => $schedule->end_date->toISOString(),
                'color' => $color,
                'extendedProps' => [
                    'worker' => $schedule->worker->name,
                    'type' => $schedule->schedule_type,
                    'notes' => $schedule->notes,
                    'booking_id' => $schedule->booking_id,
                ]
            ];
        });

        return response()->json($events);
    }
}
