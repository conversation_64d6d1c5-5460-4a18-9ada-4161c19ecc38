<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PermissionGroup;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class AdvancedPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء مجموعات الصلاحيات المتقدمة
        $groups = [
            [
                'name' => 'products',
                'display_name' => 'Product Management',
                'description' => 'Manage products and inventory',
                'icon' => 'box',
                'sort_order' => 6,
                'is_protected' => false
            ],
            [
                'name' => 'orders',
                'display_name' => 'Order Management',
                'description' => 'Manage customer orders',
                'icon' => 'shopping-cart',
                'sort_order' => 7,
                'is_protected' => false
            ],
            [
                'name' => 'reports',
                'display_name' => 'Reports & Analytics',
                'description' => 'View reports and analytics',
                'icon' => 'chart-line',
                'sort_order' => 8,
                'is_protected' => false
            ],
            [
                'name' => 'customers',
                'display_name' => 'Customer Management',
                'description' => 'Manage customer accounts',
                'icon' => 'user-friends',
                'sort_order' => 9,
                'is_protected' => false
            ],
            [
                'name' => 'settings',
                'display_name' => 'System Settings',
                'description' => 'Configure system settings',
                'icon' => 'cogs',
                'sort_order' => 10,
                'is_protected' => true
            ]
        ];

        foreach ($groups as $groupData) {
            $group = PermissionGroup::firstOrCreate(
                ['name' => $groupData['name']],
                $groupData
            );

            // إنشاء الصلاحيات لكل مجموعة
            $this->createPermissionsForGroup($group);
        }

        // إنشاء أدوار تجريبية
        $this->createTestRoles();
    }

    private function createPermissionsForGroup(PermissionGroup $group)
    {
        $permissions = [
            'view' => 'View ' . $group->display_name,
            'create' => 'Create ' . $group->display_name,
            'edit' => 'Edit ' . $group->display_name,
            'delete' => 'Delete ' . $group->display_name,
            'export' => 'Export ' . $group->display_name,
            'import' => 'Import ' . $group->display_name,
            'approve' => 'Approve ' . $group->display_name,
            'publish' => 'Publish ' . $group->display_name
        ];

        foreach ($permissions as $action => $description) {
            $permissionName = $group->name . '.' . $action;

            $permission = Permission::firstOrCreate([
                'name' => $permissionName,
                'guard_name' => 'web'
            ]);

            // ربط الصلاحية بالمجموعة
            $permission->update(['group_id' => $group->id]);
        }
    }

    private function createTestRoles()
    {
        // دور المدير العام
        $superAdmin = Role::firstOrCreate([
            'name' => 'super-admin',
            'guard_name' => 'web'
        ]);
        $superAdmin->display_name = 'Super Administrator';
        $superAdmin->save();

        // منح جميع الصلاحيات للمدير العام
        $superAdmin->syncPermissions(Permission::all());

        // دور المحرر
        $editor = Role::firstOrCreate([
            'name' => 'editor',
            'guard_name' => 'web'
        ]);
        $editor->display_name = 'Content Editor';
        $editor->save();

        // منح صلاحيات محددة للمحرر
        $editorPermissions = Permission::where('name', 'not like', '%.delete')
            ->where('name', 'not like', '%.approve')
            ->where('name', 'not like', 'settings.%')
            ->get();
        $editor->syncPermissions($editorPermissions);

        // دور المشاهد
        $viewer = Role::firstOrCreate([
            'name' => 'viewer',
            'guard_name' => 'web'
        ]);
        $viewer->display_name = 'Viewer';
        $viewer->save();

        // منح صلاحيات العرض فقط للمشاهد
        $viewerPermissions = Permission::where('name', 'like', '%.view')->get();
        $viewer->syncPermissions($viewerPermissions);

        // دور مدير المنتجات
        $productManager = Role::firstOrCreate([
            'name' => 'product-manager',
            'guard_name' => 'web'
        ]);
        $productManager->display_name = 'Product Manager';
        $productManager->save();

        // منح صلاحيات المنتجات والطلبات
        $productPermissions = Permission::where('name', 'like', 'products.%')
            ->orWhere('name', 'like', 'orders.%')
            ->orWhere('name', 'like', 'customers.view')
            ->orWhere('name', 'like', 'reports.view')
            ->get();
        $productManager->syncPermissions($productPermissions);
    }
}
