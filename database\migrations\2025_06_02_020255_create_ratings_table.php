<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ratings', function (Blueprint $table) {
            $table->id();
            $table->enum('rating_type', ['worker', 'company', 'client']);
            $table->foreignId('rater_id')->constrained('users')->onDelete('cascade');
            $table->unsignedBigInteger('rated_id'); // polymorphic relation
            $table->foreignId('booking_id')->constrained()->onDelete('cascade');
            $table->integer('star_rating')->unsigned()->default(1); // 1-5
            $table->json('criteria_ratings')->nullable(); // detailed ratings
            $table->text('comment')->nullable();
            $table->json('images')->nullable();
            $table->datetime('rating_date');
            $table->text('response')->nullable();
            $table->datetime('response_date')->nullable();
            $table->timestamps();

            $table->index(['rating_type', 'rated_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ratings');
    }
};
