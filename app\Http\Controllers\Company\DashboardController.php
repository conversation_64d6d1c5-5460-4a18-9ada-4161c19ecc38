<?php

namespace App\Http\Controllers\Company;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\Worker;
use App\Models\Booking;
use App\Models\Rating;
use App\Models\Schedule;
use Illuminate\Http\Request;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (!auth()->user()->hasRole('company')) {
                abort(403, 'Access denied. Company role required.');
            }
            return $next($request);
        });
    }

    /**
     * Show company dashboard
     */
    public function index()
    {
        $user = auth()->user();
        $company = $user->company;

        if (!$company) {
            return redirect()->route('company.setup')->with('warning', __('Please complete your company setup first.'));
        }

        // Get company statistics
        $stats = $this->getCompanyStats($company);
        
        // Get recent activities
        $recentBookings = $this->getRecentBookings($company);
        $recentRatings = $this->getRecentRatings($company);
        
        // Get upcoming schedules
        $upcomingSchedules = $this->getUpcomingSchedules($company);

        return view('company.dashboard', compact(
            'company', 
            'stats', 
            'recentBookings', 
            'recentRatings', 
            'upcomingSchedules'
        ));
    }

    /**
     * Get company statistics
     */
    private function getCompanyStats($company)
    {
        $currentMonth = Carbon::now()->startOfMonth();

        return [
            // Workers
            'total_workers' => Worker::where('company_id', $company->id)->count(),
            'active_workers' => Worker::where('company_id', $company->id)->where('is_active', true)->count(),
            
            // Bookings
            'total_bookings' => Booking::whereHas('worker', function($q) use ($company) {
                $q->where('company_id', $company->id);
            })->count(),
            
            'pending_bookings' => Booking::whereHas('worker', function($q) use ($company) {
                $q->where('company_id', $company->id);
            })->where('status', 'pending')->count(),
            
            'confirmed_bookings' => Booking::whereHas('worker', function($q) use ($company) {
                $q->where('company_id', $company->id);
            })->where('status', 'confirmed')->count(),
            
            'completed_bookings' => Booking::whereHas('worker', function($q) use ($company) {
                $q->where('company_id', $company->id);
            })->where('status', 'completed')->count(),
            
            // Monthly bookings
            'this_month_bookings' => Booking::whereHas('worker', function($q) use ($company) {
                $q->where('company_id', $company->id);
            })->where('created_at', '>=', $currentMonth)->count(),
            
            // Revenue
            'this_month_revenue' => Booking::whereHas('worker', function($q) use ($company) {
                $q->where('company_id', $company->id);
            })->where('created_at', '>=', $currentMonth)
              ->where('status', 'completed')
              ->sum('total_amount'),
            
            // Ratings
            'average_rating' => Rating::where('rating_type', 'company')
                                   ->where('rated_id', $company->id)
                                   ->avg('star_rating'),
            
            'total_ratings' => Rating::where('rating_type', 'company')
                                  ->where('rated_id', $company->id)
                                  ->count(),
        ];
    }

    /**
     * Get recent bookings
     */
    private function getRecentBookings($company)
    {
        return Booking::with(['client.user', 'worker'])
                     ->whereHas('worker', function($q) use ($company) {
                         $q->where('company_id', $company->id);
                     })
                     ->orderBy('created_at', 'desc')
                     ->limit(5)
                     ->get();
    }

    /**
     * Get recent ratings
     */
    private function getRecentRatings($company)
    {
        return Rating::with(['rater', 'booking'])
                    ->where(function($query) use ($company) {
                        $query->where('rating_type', 'company')
                              ->where('rated_id', $company->id);
                    })
                    ->orWhere(function($query) use ($company) {
                        $query->where('rating_type', 'worker')
                              ->whereIn('rated_id', function($subQuery) use ($company) {
                                  $subQuery->select('id')
                                           ->from('workers')
                                           ->where('company_id', $company->id);
                              });
                    })
                    ->orderBy('created_at', 'desc')
                    ->limit(5)
                    ->get();
    }

    /**
     * Get upcoming schedules
     */
    private function getUpcomingSchedules($company)
    {
        return Schedule::with(['worker', 'booking'])
                      ->whereHas('worker', function($q) use ($company) {
                          $q->where('company_id', $company->id);
                      })
                      ->where('start_date', '>=', Carbon::now())
                      ->orderBy('start_date', 'asc')
                      ->limit(10)
                      ->get();
    }
}
