<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use App\Models\PermissionGroup;

class CurrencyPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Currency permission group
        $currencyGroup = PermissionGroup::firstOrCreate([
            'name' => 'Currencies',
            'display_name' => 'Currencies',
            'description' => 'Currency management permissions'
        ]);

        // Define currency permissions
        $permissions = [
            [
                'name' => 'currencies.view',
                'display_name' => 'View Currencies',
                'description' => 'Can view currencies list'
            ],
            [
                'name' => 'currencies.create',
                'display_name' => 'Create Currency',
                'description' => 'Can create new currencies'
            ],
            [
                'name' => 'currencies.edit',
                'display_name' => 'Edit Currency',
                'description' => 'Can edit existing currencies'
            ],
            [
                'name' => 'currencies.delete',
                'display_name' => 'Delete Currency',
                'description' => 'Can delete currencies'
            ]
        ];

        // Create permissions
        foreach ($permissions as $permissionData) {
            $permission = Permission::firstOrCreate(
                ['name' => $permissionData['name']],
                [
                    'guard_name' => 'web'
                ]
            );

            // Update group_id if permission was created or found
            $permission->update(['group_id' => $currencyGroup->id]);
        }

        // Assign all currency permissions to Super Admin role
        $superAdminRole = Role::where('name', 'Super Admin')->first();
        if ($superAdminRole) {
            $currencyPermissions = Permission::where('name', 'like', 'currencies.%')->get();
            $superAdminRole->syncPermissions($superAdminRole->permissions->merge($currencyPermissions));
        }

        // Assign view permission to Admin role
        $adminRole = Role::where('name', 'Admin')->first();
        if ($adminRole) {
            $viewPermission = Permission::where('name', 'currencies.view')->first();
            if ($viewPermission) {
                $adminRole->givePermissionTo($viewPermission);
            }
        }
    }
}
