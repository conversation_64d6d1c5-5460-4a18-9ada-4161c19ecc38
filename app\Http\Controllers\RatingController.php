<?php

namespace App\Http\Controllers;

use App\Models\Rating;
use App\Models\User;
use App\Models\Booking;
use Illuminate\Http\Request;

class RatingController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('ratings.view');

        $query = Rating::with(['rater', 'booking']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->whereHas('rater', function ($userQuery) use ($search) {
                    $userQuery->where('name', 'like', "%{$search}%")
                             ->orWhere('email', 'like', "%{$search}%");
                })
                ->orWhere('comment', 'like', "%{$search}%");
            });
        }

        // Filter by rating type
        if ($request->filled('rating_type')) {
            $query->where('rating_type', $request->rating_type);
        }

        // Filter by star rating
        if ($request->filled('star_rating')) {
            $query->where('star_rating', $request->star_rating);
        }

        // Sort
        $sortBy = $request->get('sort_by', 'rating_date');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $ratings = $query->paginate(15)->withQueryString();

        return view('admin.ratings.index', compact('ratings'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Allow users to rate their own completed bookings
        if (!auth()->user()->can('ratings.create')) {
            // Check if this is a client trying to rate their own booking
            $bookingId = request('booking_id');
            if ($bookingId) {
                $booking = Booking::with('client.user')->find($bookingId);
                if (!$booking || $booking->client->user_id !== auth()->user()->id) {
                    abort(403, 'This action is unauthorized.');
                }
            } else {
                $this->authorize('ratings.create');
            }
        }

        $users = User::where('status', true)->get();
        $bookings = Booking::where('status', 'completed')->get();

        return view('admin.ratings.create', compact('users', 'bookings'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Allow users to rate their own completed bookings
        if (!auth()->user()->can('ratings.create')) {
            // Check if this is a client trying to rate their own booking
            $bookingId = $request->booking_id;
            if ($bookingId) {
                $booking = Booking::with('client.user')->find($bookingId);
                if (!$booking || $booking->client->user_id !== auth()->user()->id) {
                    abort(403, 'This action is unauthorized.');
                }
                // Also check that the rater_id matches the current user
                if ($request->rater_id != auth()->user()->id) {
                    abort(403, 'You can only create ratings for yourself.');
                }
            } else {
                $this->authorize('ratings.create');
            }
        }

        $validated = $request->validate([
            'rating_type' => 'required|in:worker,company,client',
            'rater_id' => 'required|exists:users,id',
            'rated_id' => 'required|integer',
            'booking_id' => 'required|exists:bookings,id',
            'star_rating' => 'required|integer|min:1|max:5',
            'criteria_ratings' => 'nullable|array',
            'comment' => 'nullable|string',
            'images' => 'nullable|array',
            'rating_date' => 'required|date',
        ]);

        Rating::create($validated);

        return redirect()->route('admin.ratings.index')
                        ->with('success', __('Rating created successfully.'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Rating $rating)
    {
        $this->authorize('ratings.view');

        $rating->load(['rater', 'booking']);

        return view('admin.ratings.show', compact('rating'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Rating $rating)
    {
        $this->authorize('ratings.edit');

        $users = User::where('status', true)->get();
        $bookings = Booking::where('status', 'completed')->get();

        return view('admin.ratings.edit', compact('rating', 'users', 'bookings'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Rating $rating)
    {
        $this->authorize('ratings.edit');

        $validated = $request->validate([
            'rating_type' => 'required|in:worker,company,client',
            'rater_id' => 'required|exists:users,id',
            'rated_id' => 'required|integer',
            'booking_id' => 'required|exists:bookings,id',
            'star_rating' => 'required|integer|min:1|max:5',
            'criteria_ratings' => 'nullable|array',
            'comment' => 'nullable|string',
            'images' => 'nullable|array',
            'rating_date' => 'required|date',
            'response' => 'nullable|string',
            'response_date' => 'nullable|date',
        ]);

        $rating->update($validated);

        return redirect()->route('admin.ratings.index')
                        ->with('success', __('Rating updated successfully.'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Rating $rating)
    {
        $this->authorize('ratings.delete');

        $rating->delete();

        return redirect()->route('admin.ratings.index')
                        ->with('success', __('Rating deleted successfully.'));
    }
}
