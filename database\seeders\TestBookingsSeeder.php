<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Client;
use App\Models\Booking;
use App\Models\Worker;
use App\Models\Company;
use Carbon\Carbon;

class TestBookingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('📅 Creating Test Bookings for Client');
        $this->command->info('=====================================');

        // Find the test user
        $testUser = User::where('email', '<EMAIL>')->first();

        if (!$testUser) {
            $this->command->error('❌ Test user (<EMAIL>) not found!');
            return;
        }

        // Ensure the user has a client profile
        $client = $testUser->client;
        if (!$client) {
            $this->command->info('🔧 Creating client profile for test user...');
            $client = Client::create([
                'user_id' => $testUser->id,
                'phone' => '+966501234567',
                'address' => 'Test Address, Riyadh',
                'date_of_birth' => '1990-01-01',
                'gender' => 'male',
                'nationality' => 'Saudi Arabia',
                'is_active' => true,
            ]);
            $this->command->info('✅ Client profile created');
        }

        // Get a test worker and company
        $worker = Worker::with('company')->first();
        if (!$worker) {
            $this->command->error('❌ No workers found! Please run WorkersSeeder first.');
            return;
        }

        $company = $worker->company;
        if (!$company) {
            $this->command->error('❌ Worker has no company! Please check data.');
            return;
        }

        // Clear existing bookings for clean test
        Booking::where('client_id', $client->id)->delete();
        $this->command->info('🗑️ Cleared existing bookings');

        // Create test bookings
        $bookings = [
            [
                'client_id' => $client->id,
                'worker_id' => $worker->id,
                'company_id' => $company->id,
                'booking_type' => 'daily',
                'start_date' => Carbon::now()->addDays(1),
                'end_date' => Carbon::now()->addDays(1)->addHours(8),
                'total_amount' => 200.00,
                'payment_status' => 'paid',
                'status' => 'confirmed',
                'payment_method' => 'credit_card',
                'transaction_id' => 'TXN-' . time() . '-001',
                'notes' => 'Test booking for house cleaning',
                'created_at' => Carbon::now()->subHours(2),
                'updated_at' => Carbon::now()->subHours(1),
            ],
            [
                'client_id' => $client->id,
                'worker_id' => $worker->id,
                'company_id' => $company->id,
                'booking_type' => 'hourly',
                'start_date' => Carbon::now()->addDays(3),
                'end_date' => Carbon::now()->addDays(3)->addHours(4),
                'total_amount' => 120.00,
                'payment_status' => 'pending',
                'status' => 'pending',
                'payment_method' => null,
                'transaction_id' => null,
                'notes' => 'Test booking for kitchen cleaning',
                'created_at' => Carbon::now()->subHours(1),
                'updated_at' => Carbon::now()->subHours(1),
            ],
            [
                'client_id' => $client->id,
                'worker_id' => $worker->id,
                'company_id' => $company->id,
                'booking_type' => 'daily',
                'start_date' => Carbon::now()->subDays(2),
                'end_date' => Carbon::now()->subDays(2)->addHours(6),
                'total_amount' => 180.00,
                'payment_status' => 'paid',
                'status' => 'completed',
                'payment_method' => 'cash',
                'transaction_id' => 'TXN-' . time() . '-002',
                'notes' => 'Completed booking for deep cleaning',
                'created_at' => Carbon::now()->subDays(3),
                'updated_at' => Carbon::now()->subDays(2),
            ],
        ];

        foreach ($bookings as $bookingData) {
            $booking = Booking::create($bookingData);
            $this->command->info("✅ Created booking #{$booking->id} - Status: {$booking->status}");
        }

        $this->command->info('');
        $this->command->info('🎉 Test bookings created successfully!');
        $this->command->info('📊 Summary:');
        $this->command->info("   • Client ID: {$client->id}");
        $this->command->info("   • Total bookings: " . Booking::where('client_id', $client->id)->count());
        $this->command->info("   • Confirmed bookings: " . Booking::where('client_id', $client->id)->where('status', 'confirmed')->count());
        $this->command->info("   • Completed bookings: " . Booking::where('client_id', $client->id)->where('status', 'completed')->count());
        $this->command->info('');
        $this->command->info('🔗 Test the bookings:');
        $this->command->info('   • <NAME_EMAIL>');
        $this->command->info('   • Visit /client/bookings');
        $this->command->info('   • Click on any booking to see details');
    }
}
