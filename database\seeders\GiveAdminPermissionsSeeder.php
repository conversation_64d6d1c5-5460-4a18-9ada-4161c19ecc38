<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class GiveAdminPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first user (admin)
        $user = User::first();

        if (!$user) {
            $this->command->error('No users found. Please create a user first.');
            return;
        }

        // Create Super Admin role if it doesn't exist
        $superAdminRole = Role::firstOrCreate(['name' => 'Super Admin']);

        // Get all permissions
        $permissions = Permission::all();

        // Give all permissions to Super Admin role
        $superAdminRole->syncPermissions($permissions);

        // Assign Super Admin role to the first user
        $user->assignRole($superAdminRole);

        $this->command->info("Super Admin role created and assigned to user: {$user->name}");
        $this->command->info("Total permissions assigned: " . $permissions->count());
    }
}
