<?php

namespace App\Http\Controllers\Company;

use App\Http\Controllers\Controller;
use App\Models\Rating;
use App\Models\Worker;
use Illuminate\Http\Request;

class RatingController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (!auth()->user()->hasRole('company')) {
                abort(403, 'Access denied. Company role required.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of company ratings.
     */
    public function index(Request $request)
    {
        $company = auth()->user()->company;

        if (!$company) {
            return redirect()->route('company.dashboard')->with('error', __('Company not found.'));
        }

        $query = Rating::with(['rater', 'booking'])
                      ->where(function($q) use ($company) {
                          // Company ratings
                          $q->where('rating_type', 'company')
                            ->where('rated_id', $company->id);
                      })
                      ->orWhere(function($q) use ($company) {
                          // Worker ratings for this company
                          $q->where('rating_type', 'worker')
                            ->whereIn('rated_id', function($subQuery) use ($company) {
                                $subQuery->select('id')
                                         ->from('workers')
                                         ->where('company_id', $company->id);
                            });
                      });

        // Filter by rating type
        if ($request->filled('rating_type')) {
            if ($request->rating_type === 'company') {
                $query->where('rating_type', 'company')
                      ->where('rated_id', $company->id);
            } elseif ($request->rating_type === 'worker') {
                $query->where('rating_type', 'worker')
                      ->whereIn('rated_id', function($subQuery) use ($company) {
                          $subQuery->select('id')
                                   ->from('workers')
                                   ->where('company_id', $company->id);
                      });
            }
        }

        // Filter by worker (for worker ratings)
        if ($request->filled('worker_id')) {
            $query->where('rating_type', 'worker')
                  ->where('rated_id', $request->worker_id);
        }

        // Filter by star rating
        if ($request->filled('star_rating')) {
            $query->where('star_rating', $request->star_rating);
        }

        // Search in comments
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('comment', 'like', "%{$search}%")
                  ->orWhereHas('rater', function($subQ) use ($search) {
                      $subQ->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $ratings = $query->orderBy('created_at', 'desc')->paginate(15);

        // Get workers for filter
        $workers = $company->workers()->where('is_active', true)->get();

        // Get rating statistics
        $stats = [
            'total_ratings' => Rating::where(function($q) use ($company) {
                $q->where('rating_type', 'company')
                  ->where('rated_id', $company->id);
            })->orWhere(function($q) use ($company) {
                $q->where('rating_type', 'worker')
                  ->whereIn('rated_id', function($subQuery) use ($company) {
                      $subQuery->select('id')
                               ->from('workers')
                               ->where('company_id', $company->id);
                  });
            })->count(),

            'company_ratings' => Rating::where('rating_type', 'company')
                                      ->where('rated_id', $company->id)
                                      ->count(),

            'worker_ratings' => Rating::where('rating_type', 'worker')
                                     ->whereIn('rated_id', function($subQuery) use ($company) {
                                         $subQuery->select('id')
                                                  ->from('workers')
                                                  ->where('company_id', $company->id);
                                     })
                                     ->count(),

            'average_company_rating' => Rating::where('rating_type', 'company')
                                             ->where('rated_id', $company->id)
                                             ->avg('star_rating'),

            'average_worker_rating' => Rating::where('rating_type', 'worker')
                                            ->whereIn('rated_id', function($subQuery) use ($company) {
                                                $subQuery->select('id')
                                                         ->from('workers')
                                                         ->where('company_id', $company->id);
                                            })
                                            ->avg('star_rating'),
        ];

        // Rating distribution
        $ratingDistribution = [];
        for ($i = 1; $i <= 5; $i++) {
            $ratingDistribution[$i] = Rating::where(function($q) use ($company) {
                $q->where('rating_type', 'company')
                  ->where('rated_id', $company->id);
            })->orWhere(function($q) use ($company) {
                $q->where('rating_type', 'worker')
                  ->whereIn('rated_id', function($subQuery) use ($company) {
                      $subQuery->select('id')
                               ->from('workers')
                               ->where('company_id', $company->id);
                  });
            })->where('star_rating', $i)->count();
        }

        return view('company.ratings.index', compact('ratings', 'workers', 'stats', 'ratingDistribution'));
    }

    /**
     * Display the specified rating.
     */
    public function show(Rating $rating)
    {
        $company = auth()->user()->company;

        // Check if rating belongs to this company
        $belongsToCompany = false;

        if ($rating->rating_type === 'company' && $rating->rated_id === $company->id) {
            $belongsToCompany = true;
        } elseif ($rating->rating_type === 'worker') {
            $worker = Worker::find($rating->rated_id);
            if ($worker && $worker->company_id === $company->id) {
                $belongsToCompany = true;
            }
        }

        if (!$belongsToCompany) {
            abort(403, 'Access denied.');
        }

        $rating->load(['rater', 'booking.client.user', 'booking.worker']);

        return view('company.ratings.show', compact('rating'));
    }

    /**
     * Respond to a rating.
     */
    public function respond(Request $request, Rating $rating)
    {
        $company = auth()->user()->company;

        // Check if rating belongs to this company
        $belongsToCompany = false;

        if ($rating->rating_type === 'company' && $rating->rated_id === $company->id) {
            $belongsToCompany = true;
        } elseif ($rating->rating_type === 'worker') {
            $worker = Worker::find($rating->rated_id);
            if ($worker && $worker->company_id === $company->id) {
                $belongsToCompany = true;
            }
        }

        if (!$belongsToCompany) {
            abort(403, 'Access denied.');
        }

        $request->validate([
            'response' => 'required|string|max:1000',
        ]);

        $rating->update([
            'response' => $request->response,
            'response_date' => now(),
        ]);

        return redirect()->route('company.ratings.index')
                        ->with('success', __('Response sent successfully.'));
    }

    /**
     * Get rating analytics data.
     */
    public function analytics()
    {
        $company = auth()->user()->company;

        // Monthly ratings for the last 12 months
        $monthlyRatings = [];
        for ($i = 11; $i >= 0; $i--) {
            $month = \Carbon\Carbon::now()->subMonths($i);
            $count = Rating::where(function($q) use ($company) {
                $q->where('rating_type', 'company')
                  ->where('rated_id', $company->id);
            })->orWhere(function($q) use ($company) {
                $q->where('rating_type', 'worker')
                  ->whereIn('rated_id', function($subQuery) use ($company) {
                      $subQuery->select('id')
                               ->from('workers')
                               ->where('company_id', $company->id);
                  });
            })->whereYear('created_at', $month->year)
              ->whereMonth('created_at', $month->month)
              ->count();

            $monthlyRatings[] = [
                'month' => $month->format('M Y'),
                'count' => $count
            ];
        }

        // Average rating by month
        $monthlyAverageRating = [];
        for ($i = 11; $i >= 0; $i--) {
            $month = \Carbon\Carbon::now()->subMonths($i);
            $average = Rating::where(function($q) use ($company) {
                $q->where('rating_type', 'company')
                  ->where('rated_id', $company->id);
            })->orWhere(function($q) use ($company) {
                $q->where('rating_type', 'worker')
                  ->whereIn('rated_id', function($subQuery) use ($company) {
                      $subQuery->select('id')
                               ->from('workers')
                               ->where('company_id', $company->id);
                  });
            })->whereYear('created_at', $month->year)
              ->whereMonth('created_at', $month->month)
              ->avg('star_rating');

            $monthlyAverageRating[] = [
                'month' => $month->format('M Y'),
                'average' => round($average, 2)
            ];
        }

        // Top rated workers
        $topWorkers = Rating::where('rating_type', 'worker')
                           ->whereIn('rated_id', function($subQuery) use ($company) {
                               $subQuery->select('id')
                                        ->from('workers')
                                        ->where('company_id', $company->id);
                           })
                           ->selectRaw('rated_id, AVG(star_rating) as average_rating, COUNT(*) as rating_count')
                           ->groupBy('rated_id')
                           ->having('rating_count', '>=', 3) // At least 3 ratings
                           ->orderBy('average_rating', 'desc')
                           ->limit(5)
                           ->get()
                           ->map(function($item) {
                               $worker = Worker::find($item->rated_id);
                               return [
                                   'worker_name' => $worker ? $worker->name : 'Unknown',
                                   'average_rating' => round($item->average_rating, 2),
                                   'rating_count' => $item->rating_count
                               ];
                           });

        return response()->json([
            'monthlyRatings' => $monthlyRatings,
            'monthlyAverageRating' => $monthlyAverageRating,
            'topWorkers' => $topWorkers,
        ]);
    }

    /**
     * Export ratings to CSV.
     */
    public function export(Request $request)
    {
        $company = auth()->user()->company;

        $ratings = Rating::with(['rater', 'booking'])
                        ->where(function($q) use ($company) {
                            $q->where('rating_type', 'company')
                              ->where('rated_id', $company->id);
                        })
                        ->orWhere(function($q) use ($company) {
                            $q->where('rating_type', 'worker')
                              ->whereIn('rated_id', function($subQuery) use ($company) {
                                  $subQuery->select('id')
                                           ->from('workers')
                                           ->where('company_id', $company->id);
                              });
                        })
                        ->orderBy('created_at', 'desc')
                        ->get();

        $filename = 'company_ratings_' . $company->id . '_' . now()->format('Y_m_d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($ratings) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Rating ID',
                'Type',
                'Rated Entity',
                'Rater Name',
                'Star Rating',
                'Comment',
                'Booking ID',
                'Date'
            ]);

            foreach ($ratings as $rating) {
                $ratedEntity = '';
                if ($rating->rating_type === 'company') {
                    $ratedEntity = 'Company';
                } elseif ($rating->rating_type === 'worker') {
                    $worker = Worker::find($rating->rated_id);
                    $ratedEntity = $worker ? $worker->name : 'Unknown Worker';
                }

                fputcsv($file, [
                    $rating->id,
                    ucfirst($rating->rating_type),
                    $ratedEntity,
                    $rating->rater->name,
                    $rating->star_rating,
                    $rating->comment,
                    $rating->booking_id,
                    $rating->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
