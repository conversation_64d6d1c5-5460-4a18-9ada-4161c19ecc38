<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('workers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->integer('age');
            $table->string('nationality');
            $table->string('image')->nullable();
            $table->string('category'); // housemaid, nurse, nanny, cook, etc.
            $table->json('skills')->nullable();
            $table->json('languages')->nullable();
            $table->json('experience')->nullable();
            $table->decimal('hourly_rate', 8, 2)->default(0.00);
            $table->decimal('daily_rate', 8, 2)->default(0.00);
            $table->decimal('monthly_rate', 8, 2)->default(0.00);
            $table->decimal('yearly_rate', 8, 2)->default(0.00);
            $table->decimal('rating', 3, 2)->default(0.00);
            $table->enum('status', ['available', 'booked', 'unavailable'])->default('available');
            $table->date('added_date');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('workers');
    }
};
