@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">

    <!-- Page Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ __('Ratings') }}</h1>
                    <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">{{ __('Manage ratings and reviews') }}</p>
                </div>
                @can('ratings.create')
                <div>
                    <a href="{{ route('admin.ratings.create') }}"
                       class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-200">
                        <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        {{ __('Add New Rating') }}
                    </a>
                </div>
                @endcan
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Search and Filters -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6">
            <div class="p-6">
                <form method="GET" action="{{ route('admin.ratings.index') }}" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <!-- Search -->
                        <div>
                            <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Search') }}
                            </label>
                            <input type="text"
                                   name="search"
                                   id="search"
                                   value="{{ request('search') }}"
                                   placeholder="{{ __('Search by rater or comment...') }}"
                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>

                        <!-- Rating Type Filter -->
                        <div>
                            <label for="rating_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Rating Type') }}
                            </label>
                            <select name="rating_type"
                                    id="rating_type"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">{{ __('All Types') }}</option>
                                <option value="worker" {{ request('rating_type') === 'worker' ? 'selected' : '' }}>{{ __('Worker') }}</option>
                                <option value="company" {{ request('rating_type') === 'company' ? 'selected' : '' }}>{{ __('Company') }}</option>
                                <option value="client" {{ request('rating_type') === 'client' ? 'selected' : '' }}>{{ __('Client') }}</option>
                            </select>
                        </div>

                        <!-- Star Rating Filter -->
                        <div>
                            <label for="star_rating" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Star Rating') }}
                            </label>
                            <select name="star_rating"
                                    id="star_rating"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">{{ __('All Ratings') }}</option>
                                <option value="5" {{ request('star_rating') === '5' ? 'selected' : '' }}>5 {{ __('Stars') }}</option>
                                <option value="4" {{ request('star_rating') === '4' ? 'selected' : '' }}>4 {{ __('Stars') }}</option>
                                <option value="3" {{ request('star_rating') === '3' ? 'selected' : '' }}>3 {{ __('Stars') }}</option>
                                <option value="2" {{ request('star_rating') === '2' ? 'selected' : '' }}>2 {{ __('Stars') }}</option>
                                <option value="1" {{ request('star_rating') === '1' ? 'selected' : '' }}>1 {{ __('Star') }}</option>
                            </select>
                        </div>

                        <!-- Sort -->
                        <div>
                            <label for="sort_by" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Sort By') }}
                            </label>
                            <select name="sort_by"
                                    id="sort_by"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="rating_date" {{ request('sort_by') === 'rating_date' ? 'selected' : '' }}>{{ __('Rating Date') }}</option>
                                <option value="star_rating" {{ request('sort_by') === 'star_rating' ? 'selected' : '' }}>{{ __('Star Rating') }}</option>
                                <option value="created_at" {{ request('sort_by') === 'created_at' ? 'selected' : '' }}>{{ __('Date Created') }}</option>
                            </select>
                        </div>
                    </div>

                    <div class="flex justify-between items-center">
                        <div class="flex space-x-2 rtl:space-x-reverse">
                            <button type="submit"
                                    class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-md transition duration-200">
                                <svg class="w-4 h-4 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                {{ __('Search') }}
                            </button>
                            <a href="{{ route('admin.ratings.index') }}"
                               class="inline-flex items-center px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium rounded-md transition duration-200">
                                {{ __('Clear') }}
                            </a>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            {{ __('Total: :count ratings', ['count' => $ratings->total()]) }}
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Ratings Cards -->
        <div class="space-y-6">
            @if($ratings->count() > 0)
                @foreach($ratings as $rating)
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                    <div class="p-6">
                        <div class="flex items-start justify-between">
                            <div class="flex items-start space-x-4 rtl:space-x-reverse">
                                <!-- Rater Avatar -->
                                <div class="flex-shrink-0">
                                    <div class="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                                        <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                    </div>
                                </div>

                                <!-- Rating Content -->
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center space-x-2 rtl:space-x-reverse mb-2">
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ $rating->rater->name }}</h3>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                            {{ $rating->rating_type_text }}
                                        </span>
                                    </div>

                                    <!-- Star Rating -->
                                    <div class="flex items-center mb-3">
                                        <div class="flex">
                                            @for($i = 1; $i <= 5; $i++)
                                                @if($i <= $rating->star_rating)
                                                    <svg class="w-5 h-5" fill="#FCD34D" viewBox="0 0 20 20">
                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                                    </svg>
                                                @else
                                                    <svg class="w-5 h-5" fill="#D1D5DB" viewBox="0 0 20 20">
                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                                    </svg>
                                                @endif
                                            @endfor
                                        </div>
                                        <span class="ms-2 text-sm text-gray-600 dark:text-gray-400">{{ $rating->star_rating }}/5</span>
                                        <span class="ms-2 text-sm text-gray-500 dark:text-gray-500">•</span>
                                        <span class="ms-2 text-sm text-gray-500 dark:text-gray-500">{{ $rating->rating_date->format('M d, Y') }}</span>
                                    </div>

                                    <!-- Comment -->
                                    @if($rating->comment)
                                    <p class="text-gray-700 dark:text-gray-300 mb-3">{{ $rating->comment }}</p>
                                    @endif

                                    <!-- Booking Link -->
                                    @if($rating->booking)
                                    <div class="text-sm text-gray-500 dark:text-gray-400 mb-3">
                                        {{ __('Related to booking') }}
                                        @can('bookings.view')
                                        <a href="{{ route('admin.bookings.show', $rating->booking) }}"
                                           class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                            #{{ $rating->booking->id }}
                                        </a>
                                        @else
                                        <span class="font-medium">#{{ $rating->booking->id }}</span>
                                        @endcan
                                    </div>
                                    @endif

                                    <!-- Response -->
                                    @if($rating->response)
                                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 mt-3">
                                        <div class="flex items-center mb-2">
                                            <svg class="w-4 h-4 text-gray-500 dark:text-gray-400 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                                            </svg>
                                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Response') }}</span>
                                            @if($rating->response_date)
                                            <span class="ms-2 text-xs text-gray-500 dark:text-gray-500">{{ $rating->response_date->format('M d, Y') }}</span>
                                            @endif
                                        </div>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $rating->response }}</p>
                                    </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Actions -->
                            <div class="flex items-center space-x-2 rtl:space-x-reverse">
                                @can('ratings.view')
                                <a href="{{ route('admin.ratings.show', $rating) }}"
                                   class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium">
                                    {{ __('View') }}
                                </a>
                                @endcan

                                @can('ratings.edit')
                                <a href="{{ route('admin.ratings.edit', $rating) }}"
                                   class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 text-sm font-medium">
                                    {{ __('Edit') }}
                                </a>
                                @endcan

                                @can('ratings.delete')
                                <form method="POST" action="{{ route('admin.ratings.destroy', $rating) }}" class="inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit"
                                            class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 text-sm font-medium"
                                            onclick="return confirm('{{ __('Are you sure you want to delete this rating?') }}')">
                                        {{ __('Delete') }}
                                    </button>
                                </form>
                                @endcan
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach

                <!-- Pagination -->
                <div class="bg-white dark:bg-gray-800 px-4 py-3 border border-gray-200 dark:border-gray-700 rounded-lg sm:px-6">
                    {{ $ratings->links() }}
                </div>
            @else
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                    <div class="text-center py-12">
                        <svg class="mx-auto h-8 w-8 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">{{ __('No ratings found') }}</h3>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ __('Get started by creating a new rating.') }}</p>
                        @can('ratings.create')
                        <div class="mt-6">
                            <a href="{{ route('admin.ratings.create') }}"
                               class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-200">
                                <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                {{ __('Add New Rating') }}
                            </a>
                        </div>
                        @endcan
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
