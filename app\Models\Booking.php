<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Booking extends Model
{
    protected $fillable = [
        'client_id',
        'worker_id',
        'company_id',
        'booking_type',
        'start_date',
        'end_date',
        'total_amount',
        'payment_status',
        'payment_method',
        'transaction_id',
        'status',
        'notes',
        'cancellation_reason',
        'cancelled_at',
        'cancelled_by',
        'rejection_reason',
        'rejected_at',
        'rejected_by',
        'confirmed_at',
        'confirmed_by',
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'total_amount' => 'decimal:2',
        'cancelled_at' => 'datetime',
        'rejected_at' => 'datetime',
        'confirmed_at' => 'datetime',
    ];

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function worker(): BelongsTo
    {
        return $this->belongsTo(Worker::class);
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function schedules(): HasMany
    {
        return $this->hasMany(Schedule::class);
    }

    public function ratings(): HasMany
    {
        return $this->hasMany(Rating::class);
    }

    public function getBookingTypeTextAttribute(): string
    {
        return match($this->booking_type) {
            'hourly' => __('Hourly'),
            'daily' => __('Daily'),
            'monthly' => __('Monthly'),
            'yearly' => __('Yearly'),
            default => __('Unknown'),
        };
    }

    public function getStatusTextAttribute(): string
    {
        return match($this->status) {
            'pending' => __('Pending'),
            'confirmed' => __('Confirmed'),
            'cancelled' => __('Cancelled'),
            'completed' => __('Completed'),
            default => __('Unknown'),
        };
    }

    public function getPaymentStatusTextAttribute(): string
    {
        return match($this->payment_status) {
            'pending' => __('Pending'),
            'paid' => __('Paid'),
            'failed' => __('Failed'),
            'refunded' => __('Refunded'),
            default => __('Unknown'),
        };
    }
}
