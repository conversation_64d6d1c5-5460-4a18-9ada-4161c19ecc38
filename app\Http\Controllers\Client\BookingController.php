<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Worker;
use App\Models\Company;
use App\Services\NotificationService;
use App\Traits\CreatesClientProfile;
use Illuminate\Http\Request;
use Carbon\Carbon;

class BookingController extends Controller
{
    use CreatesClientProfile;

    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            $user = auth()->user();
            if (!$user || (!$user->hasRole('client') && !$user->hasRole('user'))) {
                abort(403, 'Access denied. Client or User role required.');
            }
            return $next($request);
        });
        $this->notificationService = $notificationService;
    }

    /**
     * Display a listing of client bookings.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $client = $this->ensureClientProfile($user);

        if (!$client) {
            return redirect()->route('client.dashboard')->with('error', __('Client profile not found.'));
        }

        $query = Booking::with(['worker', 'company'])
                       ->where('client_id', $client->id);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->whereHas('worker', function ($workerQuery) use ($search) {
                    $workerQuery->where('name', 'like', "%{$search}%");
                })
                ->orWhereHas('company', function ($companyQuery) use ($search) {
                    $companyQuery->where('company_name', 'like', "%{$search}%");
                })
                ->orWhere('id', 'like', "%{$search}%");
            });
        }

        $bookings = $query->orderBy('created_at', 'desc')->paginate(10);

        return view('client.bookings.index', compact('bookings'));
    }

    /**
     * Show the form for creating a new booking.
     */
    public function create(Request $request)
    {
        $user = auth()->user();
        $client = $this->ensureClientProfile($user);

        if (!$client) {
            return redirect()->route('client.dashboard')->with('error', __('Client profile not found.'));
        }

        $workers = Worker::with('company')
                        ->where('status', 'available')
                        ->where('is_active', true)
                        ->get();

        $companies = Company::where('is_active', true)->get();

        // If worker_id is provided, pre-select the worker
        $selectedWorker = null;
        if ($request->filled('worker_id')) {
            $selectedWorker = Worker::find($request->worker_id);
        }

        return view('client.bookings.create', compact('workers', 'companies', 'selectedWorker'));
    }

    /**
     * Store a newly created booking.
     */
    public function store(Request $request)
    {
        $user = auth()->user();
        $client = $this->ensureClientProfile($user);

        if (!$client) {
            return redirect()->route('client.dashboard')->with('error', __('Client profile not found.'));
        }

        $validated = $request->validate([
            'worker_id' => 'required|exists:workers,id',
            'booking_type' => 'required|in:hourly,daily,monthly,yearly',
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after:start_date',
            'notes' => 'nullable|string|max:1000',
        ]);

        // Get worker and company
        $worker = Worker::with('company')->find($validated['worker_id']);

        if (!$worker || !$worker->is_active || $worker->status !== 'available') {
            return back()->withErrors(['worker_id' => __('Selected worker is not available.')]);
        }

        // Calculate total amount based on booking type and duration
        $totalAmount = $this->calculateBookingAmount($worker, $validated['booking_type'], $validated['start_date'], $validated['end_date']);

        // Create booking
        $booking = Booking::create([
            'client_id' => $client->id,
            'worker_id' => $worker->id,
            'company_id' => $worker->company_id,
            'booking_type' => $validated['booking_type'],
            'start_date' => $validated['start_date'],
            'end_date' => $validated['end_date'],
            'total_amount' => $totalAmount,
            'payment_status' => 'pending',
            'status' => 'pending',
            'notes' => $validated['notes'],
        ]);

        $booking->load(['client.user', 'worker', 'company.user']);

        // Send notifications
        $this->sendBookingNotifications($booking);

        return redirect()->route('client.bookings.index')
                        ->with('success', __('Booking created successfully. You will be notified once the company responds.'));
    }

    /**
     * Display the specified booking.
     */
    public function show(Booking $booking)
    {
        $user = auth()->user();
        $client = $this->ensureClientProfile($user);

        if (!$client || $booking->client_id !== $client->id) {
            abort(403, 'Access denied.');
        }

        $booking->load(['worker', 'company', 'ratings']);

        return view('client.bookings.show', compact('booking'));
    }

    /**
     * Cancel a booking.
     */
    public function cancel(Request $request, Booking $booking)
    {
        $user = auth()->user();
        $client = $this->ensureClientProfile($user);

        if (!$client || $booking->client_id !== $client->id) {
            abort(403, 'Access denied.');
        }

        if (!in_array($booking->status, ['pending', 'confirmed'])) {
            return back()->withErrors(['error' => __('Only pending or confirmed bookings can be cancelled.')]);
        }

        $request->validate([
            'cancellation_reason' => 'required|string|max:500',
        ]);

        $booking->update([
            'status' => 'cancelled',
            'cancellation_reason' => $request->cancellation_reason,
            'cancelled_at' => now(),
            'cancelled_by' => $user->id,
        ]);

        // Send notifications about cancellation
        $this->sendCancellationNotifications($booking);

        return redirect()->route('client.bookings.index')
                        ->with('success', __('Booking cancelled successfully.'));
    }

    /**
     * Calculate booking amount based on type and duration.
     */
    private function calculateBookingAmount($worker, $bookingType, $startDate, $endDate)
    {
        $start = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);

        switch ($bookingType) {
            case 'hourly':
                $hours = $start->diffInHours($end);
                return $worker->hourly_rate * $hours;

            case 'daily':
                $days = $start->diffInDays($end);
                return $worker->daily_rate * $days;

            case 'monthly':
                $months = $start->diffInMonths($end);
                return $worker->monthly_rate * $months;

            case 'yearly':
                $years = $start->diffInYears($end);
                return $worker->yearly_rate * $years;

            default:
                return 0;
        }
    }

    /**
     * Send booking notifications.
     */
    private function sendBookingNotifications($booking)
    {
        // Notify client
        $this->notificationService->send($booking->client->user, [
            'type' => 'booking_created',
            'title' => __('Booking Created Successfully'),
            'message' => __('Your booking #:id has been created and is pending confirmation. Worker: :worker, Amount: $:amount', [
                'id' => $booking->id,
                'worker' => $booking->worker->name,
                'amount' => number_format($booking->total_amount, 2)
            ]),
            'notifiable_type' => get_class($booking),
            'notifiable_id' => $booking->id,
            'priority' => 'medium',
            'action_url' => route('client.bookings.show', $booking),
            'action_text' => __('View Booking Details'),
            'icon' => 'calendar',
            'color' => 'blue',
            'data' => [
                'booking_id' => $booking->id,
                'total_amount' => $booking->total_amount,
                'worker_name' => $booking->worker->name,
                'start_date' => $booking->start_date->format('Y-m-d H:i'),
                'end_date' => $booking->end_date->format('Y-m-d H:i'),
            ],
        ]);

        // Notify company
        $this->notificationService->send($booking->company->user, [
            'type' => 'booking_created',
            'title' => __('New Booking Received'),
            'message' => __('A new booking #:id has been created for worker :worker. Client: :client, Amount: $:amount', [
                'id' => $booking->id,
                'worker' => $booking->worker->name,
                'client' => $booking->client->user->name,
                'amount' => number_format($booking->total_amount, 2)
            ]),
            'notifiable_type' => get_class($booking),
            'notifiable_id' => $booking->id,
            'priority' => 'high',
            'action_url' => route('company.bookings.show', $booking),
            'action_text' => __('View Booking Details'),
            'icon' => 'briefcase',
            'color' => 'green',
            'data' => [
                'booking_id' => $booking->id,
                'client_name' => $booking->client->user->name,
                'worker_name' => $booking->worker->name,
                'total_amount' => $booking->total_amount,
                'start_date' => $booking->start_date->format('Y-m-d H:i'),
                'end_date' => $booking->end_date->format('Y-m-d H:i'),
            ],
        ]);

        // Notify admin
        $adminUsers = \App\Models\User::permission('bookings.view')->get();
        foreach ($adminUsers as $admin) {
            if ($admin->id !== $booking->client->user->id && $admin->id !== $booking->company->user->id) {
                $this->notificationService->send($admin, [
                    'type' => 'booking_created',
                    'title' => __('New Booking in System'),
                    'message' => __('Booking #:id created. Client: :client, Worker: :worker, Amount: $:amount', [
                        'id' => $booking->id,
                        'client' => $booking->client->user->name,
                        'worker' => $booking->worker->name,
                        'amount' => number_format($booking->total_amount, 2)
                    ]),
                    'notifiable_type' => get_class($booking),
                    'notifiable_id' => $booking->id,
                    'priority' => 'medium',
                    'action_url' => route('admin.bookings.show', $booking),
                    'action_text' => __('View Booking Details'),
                    'icon' => 'information-circle',
                    'color' => 'blue',
                    'data' => [
                        'booking_id' => $booking->id,
                        'client_name' => $booking->client->user->name,
                        'worker_name' => $booking->worker->name,
                        'total_amount' => $booking->total_amount,
                    ],
                ]);
            }
        }
    }

    /**
     * Send cancellation notifications.
     */
    private function sendCancellationNotifications($booking)
    {
        // Notify company
        $this->notificationService->send($booking->company->user, [
            'type' => 'booking_cancelled',
            'title' => __('Booking Cancelled'),
            'message' => __('Booking #:id has been cancelled by the client. Worker: :worker', [
                'id' => $booking->id,
                'worker' => $booking->worker->name,
            ]),
            'notifiable_type' => get_class($booking),
            'notifiable_id' => $booking->id,
            'priority' => 'medium',
            'action_url' => route('company.bookings.show', $booking),
            'action_text' => __('View Booking Details'),
            'icon' => 'x-circle',
            'color' => 'red',
        ]);
    }
}
