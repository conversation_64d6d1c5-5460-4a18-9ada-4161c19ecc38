@extends('layouts.app')

@section('title', __('Client Dashboard'))

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ __('Client Dashboard') }}</h1>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">{{ __('Welcome back, :name!', ['name' => auth()->user()->name]) }}</p>
                </div>
                <a href="{{ route('client.bookings.create') }}" 
                   class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-200">
                    <svg class="w-4 h-4 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    {{ __('New Booking') }}
                </a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <!-- Total Bookings -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                        </div>
                        <div class="{{ app()->getLocale() === 'ar' ? 'mr-5' : 'ml-5' }} w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">{{ __('Total Bookings') }}</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $stats['total_bookings'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pending Bookings -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="{{ app()->getLocale() === 'ar' ? 'mr-5' : 'ml-5' }} w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">{{ __('Pending') }}</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $stats['pending_bookings'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Confirmed Bookings -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="{{ app()->getLocale() === 'ar' ? 'mr-5' : 'ml-5' }} w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">{{ __('Confirmed') }}</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $stats['confirmed_bookings'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Completed Bookings -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <div class="{{ app()->getLocale() === 'ar' ? 'mr-5' : 'ml-5' }} w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">{{ __('Completed') }}</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{ $stats['completed_bookings'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Spent -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                        <div class="{{ app()->getLocale() === 'ar' ? 'mr-5' : 'ml-5' }} w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">{{ __('Total Spent') }}</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">${{ number_format($stats['total_spent'], 2) }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Recent Bookings -->
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ __('Recent Bookings') }}</h3>
                            <a href="{{ route('client.bookings.index') }}" class="text-sm text-blue-600 hover:text-blue-500">{{ __('View All') }}</a>
                        </div>
                    </div>
                    <div class="p-6">
                        @if($recentBookings->count() > 0)
                            <div class="space-y-4">
                                @foreach($recentBookings as $booking)
                                    <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                        <div class="flex items-center space-x-3 rtl:space-x-reverse">
                                            <div class="flex-shrink-0">
                                                @if($booking->status === 'pending')
                                                    <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
                                                @elseif($booking->status === 'confirmed')
                                                    <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                                                @elseif($booking->status === 'completed')
                                                    <div class="w-3 h-3 bg-blue-400 rounded-full"></div>
                                                @else
                                                    <div class="w-3 h-3 bg-red-400 rounded-full"></div>
                                                @endif
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $booking->worker->name }}</p>
                                                <p class="text-xs text-gray-500 dark:text-gray-400">{{ $booking->company->company_name }}</p>
                                            </div>
                                        </div>
                                        <div class="text-{{ app()->getLocale() === 'ar' ? 'left' : 'right' }}">
                                            <p class="text-sm font-medium text-gray-900 dark:text-white">${{ number_format($booking->total_amount, 2) }}</p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">{{ $booking->created_at->diffForHumans() }}</p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-8">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">{{ __('No bookings yet') }}</h3>
                                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ __('Start by creating your first booking.') }}</p>
                                <div class="mt-6">
                                    <a href="{{ route('client.bookings.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                        {{ __('Create Booking') }}
                                    </a>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Available Workers -->
            <div>
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ __('Available Workers') }}</h3>
                    </div>
                    <div class="p-6">
                        @if($availableWorkers->count() > 0)
                            <div class="space-y-4">
                                @foreach($availableWorkers as $worker)
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $worker->name }}</p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">{{ $worker->company->company_name }}</p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">{{ $worker->nationality }}</p>
                                        </div>
                                        <a href="{{ route('client.bookings.create', ['worker_id' => $worker->id]) }}" 
                                           class="text-xs bg-blue-100 hover:bg-blue-200 text-blue-800 px-2 py-1 rounded-full transition duration-200">
                                            {{ __('Book') }}
                                        </a>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <p class="text-sm text-gray-500 dark:text-gray-400 text-center py-4">{{ __('No workers available at the moment.') }}</p>
                        @endif
                    </div>
                </div>

                <!-- Upcoming Bookings -->
                @if($upcomingBookings->count() > 0)
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg mt-6">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ __('Upcoming Bookings') }}</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            @foreach($upcomingBookings as $booking)
                                <div class="border-{{ app()->getLocale() === 'ar' ? 'r' : 'l' }}-4 border-green-400 pl-4 rtl:pr-4">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $booking->worker->name }}</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ $booking->start_date->format('M d, Y H:i') }}</p>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
