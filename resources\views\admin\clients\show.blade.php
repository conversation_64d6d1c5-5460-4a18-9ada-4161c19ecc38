@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Page Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                    {{ __('Client Details') }}: {{ $client->user->name }}
                </h1>
                <div class="flex space-x-3">
                    <a href="{{ route('admin.clients.index') }}" 
                       class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-md transition duration-200">
                        <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        {{ __('Back to Clients') }}
                    </a>
                    @can('clients.edit')
                    <a href="{{ route('admin.clients.edit', $client) }}" 
                       class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-200">
                        <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        {{ __('Edit Client') }}
                    </a>
                    @endcan
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Client Profile -->
            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <!-- User Avatar -->
                        <div class="text-center mb-6">
                            @if($client->user->avatar)
                                <img src="{{ asset('storage/avatars/' . $client->user->avatar) }}" 
                                     alt="{{ $client->user->name }}" 
                                     class="w-32 h-32 rounded-full mx-auto object-cover border-4 border-gray-200 dark:border-gray-600">
                            @else
                                <div class="w-32 h-32 rounded-full mx-auto bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
                                    <svg class="w-16 h-16 text-gray-400 dark:text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            @endif
                        </div>

                        <!-- Basic Info -->
                        <div class="text-center">
                            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $client->user->name }}</h2>
                            <p class="text-lg text-gray-600 dark:text-gray-400">{{ __('Client') }}</p>
                            <div class="mt-4">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                    @if($client->is_active) bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                    @else bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 @endif">
                                    @if($client->is_active)
                                        {{ __('Active') }}
                                    @else
                                        {{ __('Inactive') }}
                                    @endif
                                </span>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">{{ __('Contact Information') }}</h3>
                            <div class="space-y-3">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                                    </svg>
                                    <span class="text-sm text-gray-900 dark:text-white">{{ $client->user->email }}</span>
                                </div>
                                @if($client->user->phone)
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                                    </svg>
                                    <span class="text-sm text-gray-900 dark:text-white">{{ $client->user->phone }}</span>
                                </div>
                                @endif
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="text-sm text-gray-900 dark:text-white">{{ __('Member since') }} {{ $client->created_at->format('M Y') }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed Information -->
            <div class="lg:col-span-2">
                <div class="space-y-6">
                    <!-- Household Information -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Household Information') }}</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Number of Children') }}</label>
                                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $client->children_count }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Number of People') }}</label>
                                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $client->people_count }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Home Size') }}</label>
                                    <p class="mt-1 text-sm text-gray-900 dark:text-white">
                                        @if($client->home_size)
                                            {{ $client->home_size_text }}
                                        @else
                                            <span class="text-gray-500 dark:text-gray-400">{{ __('Not specified') }}</span>
                                        @endif
                                    </p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Number of Floors') }}</label>
                                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $client->floors_count }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Has Yard') }}</label>
                                    <p class="mt-1 text-sm text-gray-900 dark:text-white">
                                        @if($client->has_yard)
                                            <span class="text-green-600 dark:text-green-400">{{ __('Yes') }}</span>
                                        @else
                                            <span class="text-red-600 dark:text-red-400">{{ __('No') }}</span>
                                        @endif
                                    </p>
                                </div>
                                @if($client->rating > 0)
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Rating') }}</label>
                                    <div class="mt-1 flex items-center">
                                        <div class="flex items-center">
                                            @for($i = 1; $i <= 5; $i++)
                                                @if($i <= $client->rating)
                                                    <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                    </svg>
                                                @else
                                                    <svg class="w-4 h-4 text-gray-300 dark:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                    </svg>
                                                @endif
                                            @endfor
                                        </div>
                                        <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">{{ number_format($client->rating, 1) }}/5</span>
                                    </div>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Additional Information -->
                    @if($client->additional_info)
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Additional Information') }}</h3>
                            <p class="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">{{ $client->additional_info }}</p>
                        </div>
                    </div>
                    @endif

                    <!-- Recent Bookings -->
                    @if($client->bookings && $client->bookings->count() > 0)
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Recent Bookings') }}</h3>
                            <div class="space-y-4">
                                @foreach($client->bookings->take(5) as $booking)
                                <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900 dark:text-white">
                                            {{ __('Booking') }} #{{ $booking->id }}
                                        </p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ $booking->start_date->format('M d, Y') }} - {{ $booking->end_date->format('M d, Y') }}
                                        </p>
                                    </div>
                                    <div class="text-right">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            @if($booking->status === 'confirmed') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                            @elseif($booking->status === 'pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                            @elseif($booking->status === 'completed') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                            @else bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 @endif">
                                            {{ ucfirst($booking->status) }}
                                        </span>
                                        <p class="text-sm font-medium text-gray-900 dark:text-white mt-1">
                                            ${{ number_format($booking->total_amount, 2) }}
                                        </p>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                            @if($client->bookings->count() > 5)
                            <div class="mt-4 text-center">
                                <a href="{{ route('admin.bookings.index', ['client_id' => $client->id]) }}" 
                                   class="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium">
                                    {{ __('View All Bookings') }} ({{ $client->bookings->count() }})
                                </a>
                            </div>
                            @endif
                        </div>
                    </div>
                    @endif

                    <!-- Statistics -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Statistics') }}</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
                                    <p class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ $client->bookings->count() }}</p>
                                    <p class="text-sm text-blue-600 dark:text-blue-400">{{ __('Total Bookings') }}</p>
                                </div>
                                <div class="text-center p-4 bg-green-50 dark:bg-green-900/30 rounded-lg">
                                    <p class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $client->bookings->where('status', 'completed')->count() }}</p>
                                    <p class="text-sm text-green-600 dark:text-green-400">{{ __('Completed') }}</p>
                                </div>
                                <div class="text-center p-4 bg-purple-50 dark:bg-purple-900/30 rounded-lg">
                                    <p class="text-2xl font-bold text-purple-600 dark:text-purple-400">${{ number_format($client->bookings->sum('total_amount'), 2) }}</p>
                                    <p class="text-sm text-purple-600 dark:text-purple-400">{{ __('Total Spent') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
