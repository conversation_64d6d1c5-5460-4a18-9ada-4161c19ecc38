@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Page Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                إضافة صلاحية جديدة
            </h1>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="py-12">
                <div class="max-w-2xl mx-auto sm:px-6 lg:px-8">
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <form method="POST" action="{{ route('permissions.store') }}">
                                @csrf
        
                                <div class="space-y-6">
                                    <!-- معلومات الصلاحية -->
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">معلومات الصلاحية</h3>
        
                                        <!-- اسم الصلاحية -->
                                        <div class="mb-4">
                                            <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">اسم الصلاحية *</label>
                                            <input type="text" name="name" id="name" value="{{ old('name') }}" required
                                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400"
                                                   placeholder="مثال: users.create, posts.edit, admin.access">
                                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                                استخدم نمط "module.action" مثل users.create أو posts.edit
                                            </p>
                                            @error('name')
                                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                            @enderror
                                        </div>
        
                                        <!-- Guard Name -->
                                        <div class="mb-4">
                                            <label for="guard_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Guard Name *</label>
                                            <select name="guard_name" id="guard_name" required
                                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400">
                                                <option value="web" {{ old('guard_name', 'web') == 'web' ? 'selected' : '' }}>Web</option>
                                                <option value="api" {{ old('guard_name') == 'api' ? 'selected' : '' }}>API</option>
                                            </select>
                                            @error('guard_name')
                                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                            @enderror
                                        </div>
                                    </div>
        
                                    <!-- أمثلة على الصلاحيات الشائعة -->
                                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">أمثلة على الصلاحيات الشائعة</h4>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                                            <div>
                                                <h5 class="font-medium text-gray-700 dark:text-gray-300 mb-1">إدارة المستخدمين:</h5>
                                                <ul class="text-gray-600 dark:text-gray-400 space-y-1">
                                                    <li>• users.view</li>
                                                    <li>• users.create</li>
                                                    <li>• users.edit</li>
                                                    <li>• users.delete</li>
                                                </ul>
                                            </div>
                                            <div>
                                                <h5 class="font-medium text-gray-700 dark:text-gray-300 mb-1">إدارة الأدوار:</h5>
                                                <ul class="text-gray-600 dark:text-gray-400 space-y-1">
                                                    <li>• roles.view</li>
                                                    <li>• roles.create</li>
                                                    <li>• roles.edit</li>
                                                    <li>• roles.delete</li>
                                                </ul>
                                            </div>
                                            <div>
                                                <h5 class="font-medium text-gray-700 dark:text-gray-300 mb-1">إدارة المحتوى:</h5>
                                                <ul class="text-gray-600 dark:text-gray-400 space-y-1">
                                                    <li>• posts.view</li>
                                                    <li>• posts.create</li>
                                                    <li>• posts.edit</li>
                                                    <li>• posts.publish</li>
                                                </ul>
                                            </div>
                                            <div>
                                                <h5 class="font-medium text-gray-700 dark:text-gray-300 mb-1">إدارة النظام:</h5>
                                                <ul class="text-gray-600 dark:text-gray-400 space-y-1">
                                                    <li>• admin.access</li>
                                                    <li>• settings.edit</li>
                                                    <li>• reports.view</li>
                                                    <li>• logs.view</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
        
                                    <!-- أزرار سريعة لإنشاء صلاحيات شائعة -->
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">إنشاء سريع للصلاحيات</h4>
                                        <div class="flex flex-wrap gap-2">
                                            <button type="button" onclick="fillPermission('users')" class="text-sm bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 px-3 py-1 rounded hover:bg-blue-200 dark:hover:bg-blue-900/50 transition">
                                                صلاحيات المستخدمين
                                            </button>
                                            <button type="button" onclick="fillPermission('roles')" class="text-sm bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 px-3 py-1 rounded hover:bg-green-200 dark:hover:bg-green-900/50 transition">
                                                صلاحيات الأدوار
                                            </button>
                                            <button type="button" onclick="fillPermission('posts')" class="text-sm bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 px-3 py-1 rounded hover:bg-purple-200 dark:hover:bg-purple-900/50 transition">
                                                صلاحيات المحتوى
                                            </button>
                                            <button type="button" onclick="fillPermission('admin')" class="text-sm bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 px-3 py-1 rounded hover:bg-red-200 dark:hover:bg-red-900/50 transition">
                                                صلاحيات الإدارة
                                            </button>
                                        </div>
                                        <p class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                                            انقر على أي زر لملء حقل الاسم بالصلاحية المناسبة
                                        </p>
                                    </div>
                                </div>
        
                                <!-- أزرار التحكم -->
                                <div class="flex items-center justify-end mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                                    <a href="{{ route('permissions.index') }}" class="bg-gray-500 hover:bg-gray-700 dark:bg-gray-600 dark:hover:bg-gray-800 text-white font-bold py-2 px-4 rounded-lg mr-3 transition">
                                        إلغاء
                                    </a>
                                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white font-bold py-2 px-4 rounded-lg transition">
                                        <svg class="w-4 h-4 inline-block mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                                        </svg>
                                        إنشاء الصلاحية
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        
            <script>
                function fillPermission(module) {
                    const nameInput = document.getElementById('name');
                    const actions = ['view', 'create', 'edit', 'delete'];
        
                    // إذا كان الحقل فارغ، ضع الصلاحية الأولى
                    if (!nameInput.value) {
                        nameInput.value = module + '.view';
                        return;
                    }
        
                    // إذا كان الحقل يحتوي على نفس الوحدة، انتقل للصلاحية التالية
                    const currentValue = nameInput.value;
                    if (currentValue.startsWith(module + '.')) {
                        const currentAction = currentValue.split('.')[1];
                        const currentIndex = actions.indexOf(currentAction);
                        const nextIndex = (currentIndex + 1) % actions.length;
                        nameInput.value = module + '.' + actions[nextIndex];
                    } else {
                        nameInput.value = module + '.view';
                    }
                }
            </script>
    </div>
</div>
@endsection
