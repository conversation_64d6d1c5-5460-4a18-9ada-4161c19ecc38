@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ __('Currency Display Test') }}</h1>
            <p class="mt-2 text-gray-600 dark:text-gray-400">{{ __('Test currency formatting in different languages') }}</p>
        </div>

        <!-- Language Toggle -->
        <div class="mb-8 flex justify-center">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
                <div class="flex space-x-4">
                    <a href="{{ url()->current() }}?lang=en" 
                       class="px-4 py-2 rounded-lg {{ app()->getLocale() === 'en' ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300' }}">
                        English
                    </a>
                    <a href="{{ url()->current() }}?lang=ar" 
                       class="px-4 py-2 rounded-lg {{ app()->getLocale() === 'ar' ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300' }}">
                        العربية
                    </a>
                </div>
            </div>
        </div>

        <!-- Currency Display Examples -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Current Language Display -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    {{ __('Current Language') }}: {{ app()->getLocale() === 'ar' ? 'العربية' : 'English' }}
                </h3>
                
                <div class="space-y-4">
                    <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <span class="text-gray-600 dark:text-gray-400">{{ __('Amount') }}: 40.00</span>
                        <span class="font-semibold text-gray-900 dark:text-white">{{ currency_format(40.00) }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <span class="text-gray-600 dark:text-gray-400">{{ __('Amount') }}: 150.50</span>
                        <span class="font-semibold text-gray-900 dark:text-white">{{ currency_format(150.50) }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <span class="text-gray-600 dark:text-gray-400">{{ __('Amount') }}: 1250.75</span>
                        <span class="font-semibold text-gray-900 dark:text-white">{{ currency_format(1250.75) }}</span>
                    </div>
                </div>
            </div>

            <!-- Expected Format -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    {{ __('Expected Format') }}
                </h3>
                
                <div class="space-y-4">
                    @if(app()->getLocale() === 'ar')
                        <div class="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                            <p class="text-green-800 dark:text-green-200 font-medium">{{ __('Arabic Format') }}:</p>
                            <p class="text-green-700 dark:text-green-300">40.00 ر.س</p>
                            <p class="text-green-700 dark:text-green-300">150.50 ر.س</p>
                            <p class="text-green-700 dark:text-green-300">1,250.75 ر.س</p>
                            <p class="text-sm text-green-600 dark:text-green-400 mt-2">
                                {{ __('Currency symbol appears after the amount') }}
                            </p>
                        </div>
                    @else
                        <div class="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                            <p class="text-blue-800 dark:text-blue-200 font-medium">{{ __('English Format') }}:</p>
                            <p class="text-blue-700 dark:text-blue-300">ر.س40.00</p>
                            <p class="text-blue-700 dark:text-blue-300">ر.س150.50</p>
                            <p class="text-blue-700 dark:text-blue-300">ر.س1,250.75</p>
                            <p class="text-sm text-blue-600 dark:text-blue-400 mt-2">
                                {{ __('Currency symbol appears before the amount') }}
                            </p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Currency Information -->
        <div class="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                {{ __('Current Currency Settings') }}
            </h3>
            
            @php
                $defaultCurrency = default_currency();
            @endphp
            
            @if($defaultCurrency)
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ __('Currency Name') }}</p>
                        <p class="font-semibold text-gray-900 dark:text-white">{{ $defaultCurrency->name }}</p>
                    </div>
                    
                    <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ __('Currency Code') }}</p>
                        <p class="font-semibold text-gray-900 dark:text-white">{{ $defaultCurrency->code }}</p>
                    </div>
                    
                    <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ __('Currency Symbol') }}</p>
                        <p class="font-semibold text-gray-900 dark:text-white">{{ $defaultCurrency->symbol }}</p>
                    </div>
                </div>
            @else
                <p class="text-gray-600 dark:text-gray-400">{{ __('No default currency set') }}</p>
            @endif
        </div>

        <!-- Back to Dashboard -->
        <div class="mt-8 text-center">
            <a href="{{ route('admin.currencies.index') }}" 
               class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                {{ __('Back to Currencies') }}
            </a>
        </div>
    </div>
</div>
@endsection
