@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                        {{ __('Edit Permission') }}: {{ $permission->name }}
                    </h1>
                    <p class="mt-2 text-gray-600 dark:text-gray-400">
                        {{ __('Modify permission settings and view associated roles') }}
                    </p>
                </div>
                <a href="{{ route('permissions.index') }}"
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                    <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                    {{ __('Back to Permissions') }}
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Edit Form -->
                        <div class="lg:col-span-2">
                            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                                <div class="p-6">
                                    <form method="POST" action="{{ route('permissions.update', $permission) }}">
                                        @csrf
                                        @method('PUT')

                                        <div class="space-y-6">
                                            <!-- Permission Information -->
                                            <div>
                                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                                                    {{ __('Permission Information') }}
                                                </h3>

                                                <!-- Permission Name -->
                                                <div class="mb-4">
                                                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                        {{ __('Permission Name') }} *
                                                    </label>
                                                    <input type="text" name="name" id="name" value="{{ old('name', $permission->name) }}" required
                                                           class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400"
                                                           placeholder="{{ __('Example: users.create, posts.edit, admin.access') }}">
                                                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                                        {{ __('Use "module.action" pattern like users.create or posts.edit') }}
                                                    </p>
                                                    @error('name')
                                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                                    @enderror
                                                </div>

                                                <!-- Guard Name -->
                                                <div class="mb-4">
                                                    <label for="guard_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                        {{ __('Guard Name') }} *
                                                    </label>
                                                    <select name="guard_name" id="guard_name" required
                                                            class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400">
                                                        <option value="web" {{ old('guard_name', $permission->guard_name) == 'web' ? 'selected' : '' }}>Web</option>
                                                        <option value="api" {{ old('guard_name', $permission->guard_name) == 'api' ? 'selected' : '' }}>API</option>
                                                    </select>
                                                    @error('guard_name')
                                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                                    @enderror
                                                </div>
                                            </div>

                                            <!-- Additional Information -->
                                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                                <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                                                    {{ __('Permission Details') }}
                                                </h4>
                                                <dl class="space-y-1 text-sm">
                                                    <div class="flex justify-between">
                                                        <dt class="text-gray-500 dark:text-gray-400">{{ __('Created At') }}:</dt>
                                                        <dd class="text-gray-900 dark:text-gray-100">{{ $permission->created_at->format('Y-m-d H:i') }}</dd>
                                                    </div>
                                                    <div class="flex justify-between">
                                                        <dt class="text-gray-500 dark:text-gray-400">{{ __('Last Updated') }}:</dt>
                                                        <dd class="text-gray-900 dark:text-gray-100">{{ $permission->updated_at->format('Y-m-d H:i') }}</dd>
                                                    </div>
                                                    <div class="flex justify-between">
                                                        <dt class="text-gray-500 dark:text-gray-400">{{ __('Associated Roles') }}:</dt>
                                                        <dd class="text-gray-900 dark:text-gray-100">{{ $permission->roles->count() }} {{ __('roles') }}</dd>
                                                    </div>
                                                </dl>
                                            </div>
                                        </div>

                                        <!-- Action Buttons -->
                                        <div class="flex items-center justify-end mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                                            <a href="{{ route('permissions.index') }}"
                                               class="bg-gray-500 hover:bg-gray-700 dark:bg-gray-600 dark:hover:bg-gray-800 text-white font-medium py-2 px-4 rounded-lg mr-3 transition-colors duration-200">
                                                {{ __('Cancel') }}
                                            </a>
                                            <button type="submit"
                                                    class="bg-blue-600 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                                                <svg class="w-4 h-4 inline-block mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                                                </svg>
                                                {{ __('Update Permission') }}
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Associated Roles -->
                        <div>
                            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                                <div class="p-6">
                                    <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                                        {{ __('Associated Roles') }} ({{ $permission->roles->count() }})
                                    </h4>
                                    <div class="space-y-3">
                                        @forelse($permission->roles as $role)
                                            <div class="flex items-center space-x-3 rtl:space-x-reverse p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                                <div class="flex-shrink-0 h-8 w-8">
                                                    <div class="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                                                        <svg class="h-4 w-4 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                                                        </svg>
                                                    </div>
                                                </div>
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">{{ $role->name }}</p>
                                                    <p class="text-sm text-gray-500 dark:text-gray-400 truncate">{{ $role->guard_name }}</p>
                                                </div>
                                                <div class="flex-shrink-0">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                                                        {{ $role->users->count() }} {{ __('users') }}
                                                    </span>
                                                </div>
                                            </div>
                                        @empty
                                            <div class="text-center py-8">
                                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                                </svg>
                                                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">{{ __('No Associated Roles') }}</h3>
                                                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ __('This permission is not assigned to any roles yet.') }}</p>
                                            </div>
                                        @endforelse
                                    </div>

                                    @if($permission->roles->count() > 0)
                                        <div class="mt-4">
                                            @can('roles.view')
                                                <a href="{{ route('roles.index') }}"
                                                   class="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 text-sm font-medium">
                                                    {{ __('View All Roles') }} →
                                                </a>
                                            @endcan
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Quick Actions -->
                            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mt-6">
                                <div class="p-6">
                                    <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                                        {{ __('Quick Actions') }}
                                    </h4>
                                    <div class="space-y-3">
                                        @can('permissions.delete')
                                            @if($permission->roles->count() == 0)
                                                <form action="{{ route('permissions.destroy', $permission) }}" method="POST"
                                                      onsubmit="return confirm('{{ __('Are you sure you want to delete this permission?') }}')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit"
                                                            class="w-full text-left px-4 py-2 text-sm text-red-700 dark:text-red-300 bg-red-50 dark:bg-red-900/30 hover:bg-red-100 dark:hover:bg-red-900/50 rounded-lg transition-colors duration-200">
                                                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                        </svg>
                                                        {{ __('Delete Permission') }}
                                                    </button>
                                                </form>
                                            @else
                                                <div class="w-full text-left px-4 py-2 text-sm text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                                    </svg>
                                                    {{ __('Cannot delete permission (assigned to roles)') }}
                                                </div>
                                            @endif
                                        @endcan

                                        <a href="{{ route('permissions.create') }}"
                                           class="w-full text-left px-4 py-2 text-sm text-blue-700 dark:text-blue-300 bg-blue-50 dark:bg-blue-900/30 hover:bg-blue-100 dark:hover:bg-blue-900/50 rounded-lg transition-colors duration-200 block">
                                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                            </svg>
                                            {{ __('Create New Permission') }}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
