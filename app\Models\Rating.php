<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Rating extends Model
{
    protected $fillable = [
        'rating_type',
        'rater_id',
        'rated_id',
        'booking_id',
        'star_rating',
        'criteria_ratings',
        'comment',
        'images',
        'rating_date',
        'response',
        'response_date',
    ];

    protected $casts = [
        'criteria_ratings' => 'array',
        'images' => 'array',
        'rating_date' => 'datetime',
        'response_date' => 'datetime',
        'star_rating' => 'integer',
    ];

    public function rater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'rater_id');
    }

    public function booking(): BelongsTo
    {
        return $this->belongsTo(Booking::class);
    }

    public function rated()
    {
        return match($this->rating_type) {
            'worker' => $this->belongsTo(Worker::class, 'rated_id'),
            'company' => $this->belongsTo(Company::class, 'rated_id'),
            'client' => $this->belongsTo(Client::class, 'rated_id'),
            default => null,
        };
    }

    public function getRatingTypeTextAttribute(): string
    {
        return match($this->rating_type) {
            'worker' => __('Worker'),
            'company' => __('Company'),
            'client' => __('Client'),
            default => __('Unknown'),
        };
    }
}
