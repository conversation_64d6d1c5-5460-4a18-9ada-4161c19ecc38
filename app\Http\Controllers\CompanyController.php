<?php

namespace App\Http\Controllers;

use App\Models\Company;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class CompanyController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('companies.view');

        $query = Company::with('user');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('company_name', 'like', "%{$search}%")
                  ->orWhere('commercial_register', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $companies = $query->paginate(15)->withQueryString();

        return view('admin.companies.index', compact('companies'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('companies.create');

        $users = User::whereDoesntHave('company')->get();

        return view('admin.companies.create', compact('users'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('companies.create');

        $validated = $request->validate([
            'user_id' => 'required|exists:users,id|unique:companies,user_id',
            'company_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'commercial_register' => 'required|string|unique:companies,commercial_register',
            'contact_info.phone' => 'nullable|string',
            'contact_info.email' => 'nullable|email',
            'contact_info.address' => 'nullable|string',
            'contact_info.website' => 'nullable|url',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'join_date' => 'required|date',
            'bank_info.bank_name' => 'nullable|string',
            'bank_info.account_number' => 'nullable|string',
            'bank_info.iban' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        // Handle logo upload
        if ($request->hasFile('logo')) {
            $validated['logo'] = $request->file('logo')->store('companies/logos', 'public');
        }

        // Prepare JSON fields
        $validated['contact_info'] = [
            'phone' => $request->input('contact_info.phone'),
            'email' => $request->input('contact_info.email'),
            'address' => $request->input('contact_info.address'),
            'website' => $request->input('contact_info.website'),
        ];

        $validated['bank_info'] = [
            'bank_name' => $request->input('bank_info.bank_name'),
            'account_number' => $request->input('bank_info.account_number'),
            'iban' => $request->input('bank_info.iban'),
        ];

        Company::create($validated);

        return redirect()->route('admin.companies.index')
                        ->with('success', __('Company created successfully.'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Company $company)
    {
        $this->authorize('companies.view');

        $company->load('user');

        return view('admin.companies.show', compact('company'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Company $company)
    {
        $this->authorize('companies.edit');

        $users = User::whereDoesntHave('company')
                    ->orWhere('id', $company->user_id)
                    ->get();

        return view('admin.companies.edit', compact('company', 'users'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Company $company)
    {
        $this->authorize('companies.edit');

        $validated = $request->validate([
            'user_id' => [
                'required',
                'exists:users,id',
                Rule::unique('companies', 'user_id')->ignore($company->id)
            ],
            'company_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'commercial_register' => [
                'required',
                'string',
                Rule::unique('companies', 'commercial_register')->ignore($company->id)
            ],
            'contact_info.phone' => 'nullable|string',
            'contact_info.email' => 'nullable|email',
            'contact_info.address' => 'nullable|string',
            'contact_info.website' => 'nullable|url',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'join_date' => 'required|date',
            'bank_info.bank_name' => 'nullable|string',
            'bank_info.account_number' => 'nullable|string',
            'bank_info.iban' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo
            if ($company->logo) {
                Storage::disk('public')->delete($company->logo);
            }
            $validated['logo'] = $request->file('logo')->store('companies/logos', 'public');
        }

        // Prepare JSON fields
        $validated['contact_info'] = [
            'phone' => $request->input('contact_info.phone'),
            'email' => $request->input('contact_info.email'),
            'address' => $request->input('contact_info.address'),
            'website' => $request->input('contact_info.website'),
        ];

        $validated['bank_info'] = [
            'bank_name' => $request->input('bank_info.bank_name'),
            'account_number' => $request->input('bank_info.account_number'),
            'iban' => $request->input('bank_info.iban'),
        ];

        $company->update($validated);

        return redirect()->route('admin.companies.index')
                        ->with('success', __('Company updated successfully.'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Company $company)
    {
        $this->authorize('companies.delete');

        // Delete logo if exists
        if ($company->logo) {
            Storage::disk('public')->delete($company->logo);
        }

        $company->delete();

        return redirect()->route('admin.companies.index')
                        ->with('success', __('Company deleted successfully.'));
    }
}
