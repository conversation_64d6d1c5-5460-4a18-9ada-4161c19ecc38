<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Spatie\Permission\Models\Permission;

class DemoPermissionControlSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🎯 Permission Control Demo');
        $this->command->info('========================');

        // Find the test user
        $testUser = User::where('email', '<EMAIL>')->first();

        if (!$testUser) {
            $this->command->error('❌ Test user (<EMAIL>) not found!');
            return;
        }

        $this->command->info("👤 Working with user: {$testUser->name} ({$testUser->email})");
        $this->command->info('');

        // Show current permissions
        $this->command->info('📋 Current Permissions:');
        $currentPermissions = $testUser->getAllPermissions();
        foreach ($currentPermissions as $permission) {
            $this->command->info("   ✅ {$permission->name}");
        }
        $this->command->info('');

        // Ask what to do
        $action = $this->command->choice(
            'What would you like to do?',
            [
                'remove_ratings' => 'Remove ratings permissions (disable ratings access)',
                'remove_notifications' => 'Remove notifications permissions (disable notifications access)',
                'remove_both' => 'Remove both ratings and notifications permissions',
                'restore_all' => 'Restore all permissions',
                'show_only' => 'Just show current status'
            ],
            'show_only'
        );

        switch ($action) {
            case 'remove_ratings':
                $this->removeRatingsPermissions($testUser);
                break;
            case 'remove_notifications':
                $this->removeNotificationsPermissions($testUser);
                break;
            case 'remove_both':
                $this->removeRatingsPermissions($testUser);
                $this->removeNotificationsPermissions($testUser);
                break;
            case 'restore_all':
                $this->restoreAllPermissions($testUser);
                break;
            case 'show_only':
                $this->showCurrentStatus($testUser);
                break;
        }

        $this->command->info('');
        $this->command->info('🔗 Test the changes:');
        $this->command->info('   • <NAME_EMAIL>');
        $this->command->info('   • Check navigation bar');
        $this->command->info('   • Try accessing /client/ratings');
        $this->command->info('   • Try accessing /client/notifications');
    }

    private function removeRatingsPermissions($user)
    {
        $permissions = ['ratings.view', 'ratings.create'];
        foreach ($permissions as $permissionName) {
            $user->revokePermissionTo($permissionName);
            $this->command->warn("❌ Removed: {$permissionName}");
        }
        $this->command->info('🚫 Ratings access disabled for this user');
    }

    private function removeNotificationsPermissions($user)
    {
        $permissions = ['notifications.view', 'notifications.create'];
        foreach ($permissions as $permissionName) {
            $user->revokePermissionTo($permissionName);
            $this->command->warn("❌ Removed: {$permissionName}");
        }
        $this->command->info('🚫 Notifications access disabled for this user');
    }

    private function restoreAllPermissions($user)
    {
        $permissions = ['ratings.view', 'ratings.create', 'notifications.view', 'notifications.create'];
        foreach ($permissions as $permissionName) {
            $permission = Permission::where('name', $permissionName)->first();
            if ($permission) {
                $user->givePermissionTo($permission);
                $this->command->info("✅ Restored: {$permissionName}");
            }
        }
        $this->command->info('🔓 All permissions restored for this user');
    }

    private function showCurrentStatus($user)
    {
        $this->command->info('📊 Current Access Status:');

        $canViewRatings = $user->can('ratings.view');
        $canViewNotifications = $user->can('notifications.view');

        $this->command->info("   Ratings Access: " . ($canViewRatings ? '✅ Enabled' : '❌ Disabled'));
        $this->command->info("   Notifications Access: " . ($canViewNotifications ? '✅ Enabled' : '❌ Disabled'));

        $this->command->info('');
        $this->command->info('🔗 What user will see:');
        $this->command->info("   Navigation 'Ratings' link: " . ($canViewRatings ? '✅ Visible' : '❌ Hidden'));
        $this->command->info("   Navigation 'Notifications' link: " . ($canViewNotifications ? '✅ Visible' : '❌ Hidden'));
        $this->command->info("   /client/ratings access: " . ($canViewRatings ? '✅ Allowed' : '❌ 403 Forbidden'));
        $this->command->info("   /client/notifications access: " . ($canViewNotifications ? '✅ Allowed' : '❌ 403 Forbidden'));
    }
}
