<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Spatie\Permission\Models\Permission;

class TestUserPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Find the test user
        $testUser = User::where('email', '<EMAIL>')->first();

        if (!$testUser) {
            $this->command->error('Test user (<EMAIL>) not found!');
            return;
        }

        // Give the user specific permissions
        $permissions = [
            'ratings.view',
            'ratings.create',
            'notifications.view',
            'notifications.create'
        ];

        foreach ($permissions as $permissionName) {
            $permission = Permission::where('name', $permissionName)->first();
            if ($permission) {
                $testUser->givePermissionTo($permission);
                $this->command->info("Granted permission: {$permissionName}");
            } else {
                $this->command->warn("Permission not found: {$permissionName}");
            }
        }

        $this->command->info('Test user permissions updated successfully!');
    }
}
