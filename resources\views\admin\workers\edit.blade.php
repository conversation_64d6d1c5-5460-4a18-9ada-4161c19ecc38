@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Page Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                    {{ __('Edit Worker') }}: {{ $worker->name }}
                </h1>
                <div class="flex space-x-3">
                    <a href="{{ route('admin.workers.index') }}"
                       class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-md transition duration-200">
                        <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        {{ __('Back to Workers') }}
                    </a>
                    <a href="{{ route('admin.workers.show', $worker) }}"
                       class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-200">
                        <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        {{ __('View Worker') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-6xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Success/Error Messages -->
        @if(session('success'))
            <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative dark:bg-green-800 dark:border-green-600 dark:text-green-200" role="alert">
                <span class="block sm:inline">{{ session('success') }}</span>
            </div>
        @endif

        @if(session('error'))
            <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative dark:bg-red-800 dark:border-red-600 dark:text-red-200" role="alert">
                <span class="block sm:inline">{{ session('error') }}</span>
            </div>
        @endif

        @if($errors->any())
            <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative dark:bg-red-800 dark:border-red-600 dark:text-red-200" role="alert">
                <ul class="list-disc list-inside">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6">
                <form method="POST" action="{{ route('admin.workers.update', $worker) }}" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Company Selection -->
                        <div class="md:col-span-2">
                            <label for="company_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Company') }} <span class="text-red-500">*</span>
                            </label>
                            <select name="company_id"
                                    id="company_id"
                                    required
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('company_id') border-red-500 @enderror">
                                <option value="">{{ __('Select Company') }}</option>
                                @foreach($companies as $company)
                                    <option value="{{ $company->id }}" {{ old('company_id', $worker->company_id) == $company->id ? 'selected' : '' }}>
                                        {{ $company->company_name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('company_id')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Worker Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Worker Name') }} <span class="text-red-500">*</span>
                            </label>
                            <input type="text"
                                   name="name"
                                   id="name"
                                   value="{{ old('name', $worker->name) }}"
                                   required
                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('name') border-red-500 @enderror">
                            @error('name')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Age -->
                        <div>
                            <label for="age" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Age') }} <span class="text-red-500">*</span>
                            </label>
                            <input type="number"
                                   name="age"
                                   id="age"
                                   value="{{ old('age', $worker->age) }}"
                                   min="18"
                                   max="65"
                                   required
                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('age') border-red-500 @enderror">
                            @error('age')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Nationality -->
                        <div>
                            <label for="nationality" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Nationality') }} <span class="text-red-500">*</span>
                            </label>
                            <select name="nationality" id="nationality" required
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('nationality') border-red-500 @enderror">
                                <option value="">{{ __('Select Nationality') }}</option>
                                @foreach($nationalities as $nationality)
                                    <option value="{{ $nationality->name_en }}" {{ old('nationality', $worker->nationality) == $nationality->name_en ? 'selected' : '' }}>{{ $nationality->name }}</option>
                                @endforeach
                            </select>
                            @error('nationality')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Category -->
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Category') }} <span class="text-red-500">*</span>
                            </label>
                            <select name="category"
                                    id="category"
                                    required
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('category') border-red-500 @enderror">
                                <option value="">{{ __('Select Category') }}</option>
                                <option value="housemaid" {{ old('category', $worker->category) == 'housemaid' ? 'selected' : '' }}>{{ __('Housemaid') }}</option>
                                <option value="nurse" {{ old('category', $worker->category) == 'nurse' ? 'selected' : '' }}>{{ __('Nurse') }}</option>
                                <option value="nanny" {{ old('category', $worker->category) == 'nanny' ? 'selected' : '' }}>{{ __('Nanny') }}</option>
                                <option value="cook" {{ old('category', $worker->category) == 'cook' ? 'selected' : '' }}>{{ __('Cook') }}</option>
                                <option value="cleaner" {{ old('category', $worker->category) == 'cleaner' ? 'selected' : '' }}>{{ __('Cleaner') }}</option>
                                <option value="elderly_care" {{ old('category', $worker->category) == 'elderly_care' ? 'selected' : '' }}>{{ __('Elderly Care') }}</option>
                            </select>
                            @error('category')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Current Image -->
                        @if($worker->image)
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Current Image') }}
                            </label>
                            <div class="flex items-center space-x-4">
                                <img src="{{ asset('storage/' . $worker->image) }}"
                                     alt="{{ $worker->name }}"
                                     class="w-20 h-20 rounded-lg object-cover border border-gray-300 dark:border-gray-600">
                                <p class="text-sm text-gray-600 dark:text-gray-400">{{ __('Upload a new image to replace the current one') }}</p>
                            </div>
                        </div>
                        @endif

                        <!-- Worker Image -->
                        <div class="md:col-span-2">
                            <label for="image" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Worker Image') }} {{ $worker->image ? '(' . __('Optional - leave empty to keep current') . ')' : '' }}
                            </label>
                            <input type="file"
                                   name="image"
                                   id="image"
                                   accept="image/jpeg,image/png,image/jpg,image/gif"
                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('image') border-red-500 @enderror">
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ __('Max size: 2MB. Supported formats: JPEG, PNG, JPG, GIF') }}</p>
                            @error('image')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Skills -->
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Skills') }}
                            </label>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                                @php
                                    $skills = ['cleaning', 'cooking', 'childcare', 'elderly_care', 'ironing', 'laundry', 'organizing', 'pet_care'];
                                    $workerSkills = old('skills', is_array($worker->skills) ? $worker->skills : []);
                                @endphp
                                @foreach($skills as $skill)
                                    <label class="flex items-center">
                                        <input type="checkbox"
                                               name="skills[]"
                                               value="{{ $skill }}"
                                               {{ in_array($skill, $workerSkills) ? 'checked' : '' }}
                                               class="rounded border-gray-300 dark:border-gray-600 dark:bg-gray-700 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">{{ __(ucfirst(str_replace('_', ' ', $skill))) }}</span>
                                    </label>
                                @endforeach
                            </div>
                            @error('skills')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Languages -->
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Languages') }}
                            </label>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                                @php
                                    $languages = ['arabic', 'english', 'filipino', 'hindi', 'urdu', 'bengali', 'french', 'spanish'];
                                    $workerLanguages = old('languages', is_array($worker->languages) ? $worker->languages : []);
                                @endphp
                                @foreach($languages as $language)
                                    <label class="flex items-center">
                                        <input type="checkbox"
                                               name="languages[]"
                                               value="{{ $language }}"
                                               {{ in_array($language, $workerLanguages) ? 'checked' : '' }}
                                               class="rounded border-gray-300 dark:border-gray-600 dark:bg-gray-700 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">{{ __(ucfirst($language)) }}</span>
                                    </label>
                                @endforeach
                            </div>
                            @error('languages')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Experience -->
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Experience') }}
                            </label>
                            <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                                @php
                                    $experiences = ['0-1_years', '1-3_years', '3-5_years', '5-10_years', '10+_years'];
                                    $workerExperience = old('experience', is_array($worker->experience) ? $worker->experience : []);
                                @endphp
                                @foreach($experiences as $experience)
                                    <label class="flex items-center">
                                        <input type="checkbox"
                                               name="experience[]"
                                               value="{{ $experience }}"
                                               {{ in_array($experience, $workerExperience) ? 'checked' : '' }}
                                               class="rounded border-gray-300 dark:border-gray-600 dark:bg-gray-700 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">{{ __(str_replace('_', ' ', $experience)) }}</span>
                                    </label>
                                @endforeach
                            </div>
                            @error('experience')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Rates Section -->
                        <div class="md:col-span-2">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">{{ __('Rates') }}</h3>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <!-- Hourly Rate -->
                                <div>
                                    <label for="hourly_rate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        {{ __('Hourly Rate') }} <span class="text-red-500">*</span>
                                    </label>
                                    <input type="number"
                                           name="hourly_rate"
                                           id="hourly_rate"
                                           value="{{ old('hourly_rate', $worker->hourly_rate) }}"
                                           step="0.01"
                                           min="0"
                                           required
                                           class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('hourly_rate') border-red-500 @enderror">
                                    @error('hourly_rate')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Daily Rate -->
                                <div>
                                    <label for="daily_rate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        {{ __('Daily Rate') }} <span class="text-red-500">*</span>
                                    </label>
                                    <input type="number"
                                           name="daily_rate"
                                           id="daily_rate"
                                           value="{{ old('daily_rate', $worker->daily_rate) }}"
                                           step="0.01"
                                           min="0"
                                           required
                                           class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('daily_rate') border-red-500 @enderror">
                                    @error('daily_rate')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Monthly Rate -->
                                <div>
                                    <label for="monthly_rate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        {{ __('Monthly Rate') }} <span class="text-red-500">*</span>
                                    </label>
                                    <input type="number"
                                           name="monthly_rate"
                                           id="monthly_rate"
                                           value="{{ old('monthly_rate', $worker->monthly_rate) }}"
                                           step="0.01"
                                           min="0"
                                           required
                                           class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('monthly_rate') border-red-500 @enderror">
                                    @error('monthly_rate')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Yearly Rate -->
                                <div>
                                    <label for="yearly_rate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        {{ __('Yearly Rate') }} <span class="text-red-500">*</span>
                                    </label>
                                    <input type="number"
                                           name="yearly_rate"
                                           id="yearly_rate"
                                           value="{{ old('yearly_rate', $worker->yearly_rate) }}"
                                           step="0.01"
                                           min="0"
                                           required
                                           class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('yearly_rate') border-red-500 @enderror">
                                    @error('yearly_rate')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Status -->
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Status') }} <span class="text-red-500">*</span>
                            </label>
                            <select name="status"
                                    id="status"
                                    required
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('status') border-red-500 @enderror">
                                <option value="">{{ __('Select Status') }}</option>
                                <option value="available" {{ old('status', $worker->status) == 'available' ? 'selected' : '' }}>{{ __('Available') }}</option>
                                <option value="booked" {{ old('status', $worker->status) == 'booked' ? 'selected' : '' }}>{{ __('Booked') }}</option>
                                <option value="unavailable" {{ old('status', $worker->status) == 'unavailable' ? 'selected' : '' }}>{{ __('Unavailable') }}</option>
                            </select>
                            @error('status')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Added Date -->
                        <div>
                            <label for="added_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Added Date') }} <span class="text-red-500">*</span>
                            </label>
                            <input type="date"
                                   name="added_date"
                                   id="added_date"
                                   value="{{ old('added_date', $worker->added_date->format('Y-m-d')) }}"
                                   required
                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('added_date') border-red-500 @enderror">
                            @error('added_date')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Is Active -->
                        <div class="md:col-span-2">
                            <div class="flex items-center">
                                <!-- Hidden input to ensure a value is always sent -->
                                <input type="hidden" name="is_active" value="0">
                                <input type="checkbox"
                                       name="is_active"
                                       id="is_active"
                                       value="1"
                                       {{ old('is_active', $worker->is_active) ? 'checked' : '' }}
                                       class="rounded border-gray-300 dark:border-gray-600 dark:bg-gray-700 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <label for="is_active" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                    {{ __('Active Worker') }}
                                </label>
                            </div>
                            @error('is_active')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="flex items-center justify-end mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                        <a href="{{ route('admin.workers.show', $worker) }}"
                           class="bg-gray-500 hover:bg-gray-700 dark:bg-gray-600 dark:hover:bg-gray-800 text-white font-bold py-2 px-4 rounded-lg mr-3 transition">
                            {{ __('Cancel') }}
                        </a>
                        <button type="submit"
                                class="bg-blue-500 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white font-bold py-2 px-4 rounded-lg transition">
                            <svg class="w-4 h-4 inline-block mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            {{ __('Update Worker') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const imageInput = document.getElementById('image');
    const form = document.querySelector('form');

    // Validate image file before form submission
    form.addEventListener('submit', function(e) {
        const file = imageInput.files[0];

        if (file) {
            // Check file size (2MB = 2 * 1024 * 1024 bytes)
            if (file.size > 2 * 1024 * 1024) {
                e.preventDefault();
                alert('{{ __("Image size must be less than 2MB") }}');
                return false;
            }

            // Check file type
            const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif'];
            if (!allowedTypes.includes(file.type)) {
                e.preventDefault();
                alert('{{ __("Please select a valid image file (JPEG, PNG, JPG, GIF)") }}');
                return false;
            }
        }
    });

    // Clear file input if invalid file is selected
    imageInput.addEventListener('change', function() {
        const file = this.files[0];

        if (file) {
            if (file.size > 2 * 1024 * 1024) {
                this.value = '';
                alert('{{ __("Image size must be less than 2MB") }}');
                return;
            }

            const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif'];
            if (!allowedTypes.includes(file.type)) {
                this.value = '';
                alert('{{ __("Please select a valid image file (JPEG, PNG, JPG, GIF)") }}');
                return;
            }
        }
    });
});
</script>

@endsection
