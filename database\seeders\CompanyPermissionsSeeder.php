<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CompanyPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if permission group already exists
        $existingGroup = DB::table('permission_groups')->where('name', 'companies')->first();

        if ($existingGroup) {
            $groupId = $existingGroup->id;
        } else {
            // Add permission group for companies
            $groupId = DB::table('permission_groups')->insertGetId([
                'name' => 'companies',
                'display_name' => 'Company Management',
                'description' => 'Manage companies and their profiles',
                'icon' => 'building-office',
                'sort_order' => 11,
                'is_protected' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // Add permissions for companies
        $permissionNames = ['companies.view', 'companies.create', 'companies.edit', 'companies.delete'];

        foreach ($permissionNames as $permissionName) {
            $existingPermission = DB::table('permissions')->where('name', $permissionName)->first();

            if (!$existingPermission) {
                DB::table('permissions')->insert([
                    'name' => $permissionName,
                    'group_id' => $groupId,
                    'guard_name' => 'web',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }
}
