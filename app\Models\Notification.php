<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Notification extends Model
{
    protected $fillable = [
        'type',
        'title',
        'message',
        'data',
        'user_id',
        'notifiable_type',
        'notifiable_id',
        'priority',
        'channel',
        'is_read',
        'read_at',
        'action_url',
        'action_text',
        'icon',
        'color',
        'expires_at',
    ];

    protected $casts = [
        'data' => 'array',
        'is_read' => 'boolean',
        'read_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function notifiable(): MorphTo
    {
        return $this->morphTo();
    }

    public function markAsRead(): void
    {
        if (!$this->is_read) {
            $this->update([
                'is_read' => true,
                'read_at' => now(),
            ]);
        }
    }

    public function markAsUnread(): void
    {
        $this->update([
            'is_read' => false,
            'read_at' => null,
        ]);
    }

    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    public function getPriorityColorAttribute(): string
    {
        return match($this->priority) {
            'low' => 'gray',
            'medium' => 'blue',
            'high' => 'yellow',
            'urgent' => 'red',
            default => 'blue',
        };
    }

    public function getPriorityTextAttribute(): string
    {
        return match($this->priority) {
            'low' => __('Low'),
            'medium' => __('Medium'),
            'high' => __('High'),
            'urgent' => __('Urgent'),
            default => __('Medium'),
        };
    }

    public function getTypeTextAttribute(): string
    {
        return match($this->type) {
            'booking_created' => __('Booking Created'),
            'booking_confirmed' => __('Booking Confirmed'),
            'booking_cancelled' => __('Booking Cancelled'),
            'booking_completed' => __('Booking Completed'),
            'payment_received' => __('Payment Received'),
            'payment_failed' => __('Payment Failed'),
            'worker_assigned' => __('Worker Assigned'),
            'worker_available' => __('Worker Available'),
            'schedule_updated' => __('Schedule Updated'),
            'rating_received' => __('Rating Received'),
            'system_maintenance' => __('System Maintenance'),
            'account_updated' => __('Account Updated'),
            default => __('Notification'),
        };
    }

    // Scopes
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    public function scopeRead($query)
    {
        return $query->where('is_read', true);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeNotExpired($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', now());
        });
    }

    public function scopeRecent($query, $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }
}
