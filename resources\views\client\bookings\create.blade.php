@extends('layouts.app')

@section('title', __('Create Booking'))

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ __('Create Booking') }}</h1>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">{{ __('Book a worker for your needs') }}</p>
                </div>
                <a href="{{ route('client.bookings.index') }}" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="w-4 h-4 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    {{ __('Back to Bookings') }}
                </a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Booking Guide -->
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 rounded-lg p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                📋 {{ __('How to Book a Worker') }}
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div class="bg-white dark:bg-gray-800 p-4 rounded-lg">
                    <h3 class="font-medium text-blue-600 dark:text-blue-400 mb-2">1️⃣ {{ __('Choose Worker') }}</h3>
                    <p class="text-gray-600 dark:text-gray-400">{{ __('Select from available workers based on your needs') }}</p>
                </div>
                <div class="bg-white dark:bg-gray-800 p-4 rounded-lg">
                    <h3 class="font-medium text-green-600 dark:text-green-400 mb-2">2️⃣ {{ __('Set Schedule') }}</h3>
                    <p class="text-gray-600 dark:text-gray-400">{{ __('Choose your preferred dates and booking type') }}</p>
                </div>
                <div class="bg-white dark:bg-gray-800 p-4 rounded-lg">
                    <h3 class="font-medium text-purple-600 dark:text-purple-400 mb-2">3️⃣ {{ __('Wait for Approval') }}</h3>
                    <p class="text-gray-600 dark:text-gray-400">{{ __('Company will review and confirm your booking') }}</p>
                </div>
            </div>
        </div>

        <!-- Create Booking Form -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ __('Booking Information') }}</h3>
            </div>

            <form action="{{ route('client.bookings.store') }}" method="POST" class="p-6 space-y-6">
                @csrf

                <!-- Worker Selection -->
                <div>
                    <label for="worker_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {{ __('Worker') }} <span class="text-red-500">*</span>
                    </label>
                    <select name="worker_id" id="worker_id" required
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        <option value="">{{ __('Select Worker') }}</option>
                        @foreach($workers as $worker)
                            <option value="{{ $worker->id }}" 
                                    data-hourly-rate="{{ $worker->hourly_rate ?? 0 }}"
                                    data-daily-rate="{{ $worker->daily_rate ?? 0 }}"
                                    data-monthly-rate="{{ $worker->monthly_rate ?? 0 }}"
                                    data-yearly-rate="{{ $worker->yearly_rate ?? 0 }}"
                                    {{ (old('worker_id', $selectedWorker?->id) == $worker->id) ? 'selected' : '' }}>
                                {{ $worker->name }} - {{ $worker->company->company_name }} ({{ $worker->nationality }})
                            </option>
                        @endforeach
                    </select>
                    @error('worker_id')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Booking Type -->
                <div>
                    <label for="booking_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {{ __('Booking Type') }} <span class="text-red-500">*</span>
                    </label>
                    <select name="booking_type" id="booking_type" required
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        <option value="">{{ __('Select Booking Type') }}</option>
                        <option value="hourly" {{ old('booking_type') == 'hourly' ? 'selected' : '' }}>
                            ⏰ {{ __('Hourly') }}
                        </option>
                        <option value="daily" {{ old('booking_type') == 'daily' ? 'selected' : '' }}>
                            📅 {{ __('Daily') }}
                        </option>
                        <option value="monthly" {{ old('booking_type') == 'monthly' ? 'selected' : '' }}>
                            🗓️ {{ __('Monthly') }}
                        </option>
                        <option value="yearly" {{ old('booking_type') == 'yearly' ? 'selected' : '' }}>
                            📆 {{ __('Yearly') }}
                        </option>
                    </select>
                    @error('booking_type')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Date Range -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {{ __('Start Date') }} <span class="text-red-500">*</span>
                        </label>
                        <input type="datetime-local" name="start_date" id="start_date" required
                               value="{{ old('start_date') }}"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        @error('start_date')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {{ __('End Date') }} <span class="text-red-500">*</span>
                        </label>
                        <input type="datetime-local" name="end_date" id="end_date" required
                               value="{{ old('end_date') }}"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        @error('end_date')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Cost Estimation -->
                <div id="cost_estimation" class="hidden">
                    <div class="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-blue-500 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                            <span class="text-sm font-medium text-blue-800 dark:text-blue-200">
                                {{ __('Estimated Cost') }}: $<span id="estimated_cost">0.00</span>
                            </span>
                        </div>
                        <p class="text-xs text-blue-600 dark:text-blue-300 mt-1">{{ __('Final cost may vary based on company rates') }}</p>
                    </div>
                </div>

                <!-- Notes -->
                <div>
                    <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {{ __('Notes') }}
                    </label>
                    <textarea name="notes" id="notes" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                              placeholder="{{ __('Add any special requirements or notes...') }}">{{ old('notes') }}</textarea>
                    @error('notes')
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Submit Buttons -->
                <div class="flex items-center justify-end space-x-3 rtl:space-x-reverse pt-6 border-t border-gray-200 dark:border-gray-700">
                    <a href="{{ route('client.bookings.index') }}" 
                       class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        {{ __('Cancel') }}
                    </a>
                    <button type="submit" 
                            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        {{ __('Create Booking') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const workerSelect = document.getElementById('worker_id');
    const bookingTypeSelect = document.getElementById('booking_type');
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    const costEstimation = document.getElementById('cost_estimation');
    const estimatedCostSpan = document.getElementById('estimated_cost');

    // Set minimum date to today
    const now = new Date();
    const minDateTime = now.toISOString().slice(0, 16);
    startDateInput.min = minDateTime;
    endDateInput.min = minDateTime;

    // Update end date minimum when start date changes
    startDateInput.addEventListener('change', function() {
        endDateInput.min = this.value;
        calculateCost();
    });

    endDateInput.addEventListener('change', calculateCost);
    workerSelect.addEventListener('change', calculateCost);
    bookingTypeSelect.addEventListener('change', calculateCost);

    function calculateCost() {
        const worker = workerSelect.selectedOptions[0];
        const bookingType = bookingTypeSelect.value;
        const startDate = new Date(startDateInput.value);
        const endDate = new Date(endDateInput.value);

        if (!worker || !worker.value || !bookingType || !startDate || !endDate || endDate <= startDate) {
            costEstimation.classList.add('hidden');
            return;
        }

        let rate = 0;
        let duration = 0;

        switch(bookingType) {
            case 'hourly':
                rate = parseFloat(worker.dataset.hourlyRate) || 0;
                duration = Math.ceil((endDate - startDate) / (1000 * 60 * 60)); // hours
                break;
            case 'daily':
                rate = parseFloat(worker.dataset.dailyRate) || 0;
                duration = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)); // days
                break;
            case 'monthly':
                rate = parseFloat(worker.dataset.monthlyRate) || 0;
                duration = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24 * 30)); // months
                break;
            case 'yearly':
                rate = parseFloat(worker.dataset.yearlyRate) || 0;
                duration = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24 * 365)); // years
                break;
        }

        const totalCost = rate * duration;
        estimatedCostSpan.textContent = totalCost.toFixed(2);
        costEstimation.classList.remove('hidden');
    }
});
</script>
@endsection
