<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\User;
use Illuminate\Http\Request;

class ClientController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('clients.view');

        $query = Client::with('user');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->whereHas('user', function ($userQuery) use ($search) {
                    $userQuery->where('name', 'like', "%{$search}%")
                             ->orWhere('email', 'like', "%{$search}%");
                });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Filter by home size
        if ($request->filled('home_size')) {
            $query->where('home_size', $request->home_size);
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $clients = $query->paginate(15)->withQueryString();

        return view('admin.clients.index', compact('clients'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('clients.create');

        $users = User::whereDoesntHave('client')->get();

        return view('admin.clients.create', compact('users'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('clients.create');

        $validated = $request->validate([
            'user_id' => 'required|exists:users,id|unique:clients,user_id',
            'children_count' => 'required|integer|min:0',
            'home_size' => 'nullable|in:small,medium,large,villa',
            'floors_count' => 'required|integer|min:1',
            'people_count' => 'required|integer|min:1',
            'has_yard' => 'boolean',
            'additional_info' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        Client::create($validated);

        return redirect()->route('admin.clients.index')
                        ->with('success', __('Client created successfully.'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Client $client)
    {
        $this->authorize('clients.view');

        $client->load('user');

        return view('admin.clients.show', compact('client'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Client $client)
    {
        $this->authorize('clients.edit');

        $users = User::whereDoesntHave('client')
                    ->orWhere('id', $client->user_id)
                    ->get();

        return view('admin.clients.edit', compact('client', 'users'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Client $client)
    {
        $this->authorize('clients.edit');

        $validated = $request->validate([
            'user_id' => 'required|exists:users,id|unique:clients,user_id,' . $client->id,
            'children_count' => 'required|integer|min:0',
            'home_size' => 'nullable|in:small,medium,large,villa',
            'floors_count' => 'required|integer|min:1',
            'people_count' => 'required|integer|min:1',
            'has_yard' => 'boolean',
            'additional_info' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        $client->update($validated);

        return redirect()->route('admin.clients.index')
                        ->with('success', __('Client updated successfully.'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Client $client)
    {
        $this->authorize('clients.delete');

        $client->delete();

        return redirect()->route('admin.clients.index')
                        ->with('success', __('Client deleted successfully.'));
    }
}
