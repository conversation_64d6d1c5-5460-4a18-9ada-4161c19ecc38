@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Page Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                    {{ __('Create New Client') }}
                </h1>
                <a href="{{ route('admin.clients.index') }}" 
                   class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-md transition duration-200">
                    <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    {{ __('Back to Clients') }}
                </a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6">
                <form method="POST" action="{{ route('admin.clients.store') }}">
                    @csrf

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- User Selection -->
                        <div class="md:col-span-2">
                            <label for="user_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Select User') }} <span class="text-red-500">*</span>
                            </label>
                            <select name="user_id" 
                                    id="user_id" 
                                    required
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('user_id') border-red-500 @enderror">
                                <option value="">{{ __('Choose a user') }}</option>
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}" {{ old('user_id') == $user->id ? 'selected' : '' }}>
                                        {{ $user->name }} ({{ $user->email }})
                                    </option>
                                @endforeach
                            </select>
                            @error('user_id')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Children Count -->
                        <div>
                            <label for="children_count" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Number of Children') }} <span class="text-red-500">*</span>
                            </label>
                            <input type="number" 
                                   name="children_count" 
                                   id="children_count" 
                                   value="{{ old('children_count', 0) }}" 
                                   min="0"
                                   required
                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('children_count') border-red-500 @enderror">
                            @error('children_count')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- People Count -->
                        <div>
                            <label for="people_count" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Number of People') }} <span class="text-red-500">*</span>
                            </label>
                            <input type="number" 
                                   name="people_count" 
                                   id="people_count" 
                                   value="{{ old('people_count', 1) }}" 
                                   min="1"
                                   required
                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('people_count') border-red-500 @enderror">
                            @error('people_count')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Home Size -->
                        <div>
                            <label for="home_size" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Home Size') }}
                            </label>
                            <select name="home_size" 
                                    id="home_size"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('home_size') border-red-500 @enderror">
                                <option value="">{{ __('Select home size') }}</option>
                                <option value="small" {{ old('home_size') == 'small' ? 'selected' : '' }}>{{ __('Small') }}</option>
                                <option value="medium" {{ old('home_size') == 'medium' ? 'selected' : '' }}>{{ __('Medium') }}</option>
                                <option value="large" {{ old('home_size') == 'large' ? 'selected' : '' }}>{{ __('Large') }}</option>
                                <option value="villa" {{ old('home_size') == 'villa' ? 'selected' : '' }}>{{ __('Villa') }}</option>
                            </select>
                            @error('home_size')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Floors Count -->
                        <div>
                            <label for="floors_count" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Number of Floors') }} <span class="text-red-500">*</span>
                            </label>
                            <input type="number" 
                                   name="floors_count" 
                                   id="floors_count" 
                                   value="{{ old('floors_count', 1) }}" 
                                   min="1"
                                   required
                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('floors_count') border-red-500 @enderror">
                            @error('floors_count')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Has Yard -->
                        <div class="md:col-span-2">
                            <div class="flex items-center">
                                <input type="checkbox" 
                                       name="has_yard" 
                                       id="has_yard" 
                                       value="1"
                                       {{ old('has_yard') ? 'checked' : '' }}
                                       class="rounded border-gray-300 dark:border-gray-600 dark:bg-gray-700 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <label for="has_yard" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                    {{ __('Has Yard') }}
                                </label>
                            </div>
                            @error('has_yard')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Additional Info -->
                        <div class="md:col-span-2">
                            <label for="additional_info" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Additional Information') }}
                            </label>
                            <textarea name="additional_info" 
                                      id="additional_info" 
                                      rows="4"
                                      class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('additional_info') border-red-500 @enderror">{{ old('additional_info') }}</textarea>
                            @error('additional_info')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Is Active -->
                        <div class="md:col-span-2">
                            <div class="flex items-center">
                                <input type="checkbox" 
                                       name="is_active" 
                                       id="is_active" 
                                       value="1"
                                       {{ old('is_active', true) ? 'checked' : '' }}
                                       class="rounded border-gray-300 dark:border-gray-600 dark:bg-gray-700 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <label for="is_active" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                    {{ __('Active Client') }}
                                </label>
                            </div>
                            @error('is_active')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="flex items-center justify-end mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                        <a href="{{ route('admin.clients.index') }}" 
                           class="bg-gray-500 hover:bg-gray-700 dark:bg-gray-600 dark:hover:bg-gray-800 text-white font-bold py-2 px-4 rounded-lg mr-3 transition">
                            {{ __('Cancel') }}
                        </a>
                        <button type="submit" 
                                class="bg-blue-500 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-800 text-white font-bold py-2 px-4 rounded-lg transition">
                            <svg class="w-4 h-4 inline-block mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                            </svg>
                            {{ __('Create Client') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
