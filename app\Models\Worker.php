<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Worker extends Model
{
    protected $fillable = [
        'company_id',
        'name',
        'age',
        'nationality',
        'image',
        'category',
        'skills',
        'languages',
        'experience',
        'hourly_rate',
        'daily_rate',
        'monthly_rate',
        'yearly_rate',
        'rating',
        'status',
        'added_date',
        'is_active',
        'description',
        'passport_photo',
        'cv_file',
    ];

    protected $casts = [
        'skills' => 'array',
        'languages' => 'array',
        'experience' => 'array',
        'hourly_rate' => 'decimal:2',
        'daily_rate' => 'decimal:2',
        'monthly_rate' => 'decimal:2',
        'yearly_rate' => 'decimal:2',
        'rating' => 'decimal:2',
        'added_date' => 'date',
        'is_active' => 'boolean',
    ];

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }

    public function schedules(): HasMany
    {
        return $this->hasMany(Schedule::class);
    }

    public function ratings(): HasMany
    {
        return $this->hasMany(Rating::class, 'rated_id')->where('rating_type', 'worker');
    }

    public function getStatusTextAttribute(): string
    {
        return match($this->status) {
            'available' => __('Available'),
            'booked' => __('Booked'),
            'unavailable' => __('Unavailable'),
            default => __('Unknown'),
        };
    }

    public function getCategoryTextAttribute(): string
    {
        return match($this->category) {
            'housemaid' => __('Housemaid'),
            'nurse' => __('Nurse'),
            'nanny' => __('Nanny'),
            'cook' => __('Cook'),
            'cleaner' => __('Cleaner'),
            'elderly_care' => __('Elderly Care'),
            default => __('Other'),
        };
    }

    // Accessor for experience_years (for backward compatibility)
    public function getExperienceYearsAttribute()
    {
        // If experience is an array, get the first element or return 0
        if (is_array($this->experience)) {
            return count($this->experience) > 0 ? (int)$this->experience[0] : 0;
        }
        // If experience is a number, return it
        if (is_numeric($this->experience)) {
            return (int)$this->experience;
        }
        // Default to 0
        return 0;
    }
}
