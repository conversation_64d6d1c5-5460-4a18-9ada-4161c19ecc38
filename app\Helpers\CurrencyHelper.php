<?php

namespace App\Helpers;

use App\Models\Currency;

class CurrencyHelper
{
    /**
     * Format amount with default currency
     */
    public static function format($amount, $currency = null)
    {
        if (!$currency) {
            $currency = Currency::getDefault();
        }

        if (!$currency) {
            // Fallback formatting based on locale
            $formatted = number_format($amount, 2);
            $currentLocale = app()->getLocale();

            if ($currentLocale === 'ar') {
                return $formatted . ' ر.س'; // Arabic: amount then symbol
            } else {
                return '$' . $formatted; // English: symbol then amount
            }
        }

        return $currency->format($amount);
    }

    /**
     * Get default currency
     */
    public static function getDefault()
    {
        return Currency::getDefault();
    }

    /**
     * Get currency symbol
     */
    public static function getSymbol($currency = null)
    {
        if (!$currency) {
            $currency = Currency::getDefault();
        }

        return $currency ? $currency->symbol : '$';
    }

    /**
     * Convert amount between currencies
     */
    public static function convert($amount, $fromCurrency, $toCurrency)
    {
        if ($fromCurrency->id === $toCurrency->id) {
            return $amount;
        }

        // Convert to base currency first
        $baseAmount = $amount / $fromCurrency->exchange_rate;

        // Convert to target currency
        return $baseAmount * $toCurrency->exchange_rate;
    }

    /**
     * Format amount with locale-aware positioning
     */
    public static function formatWithLocale($amount, $symbol = null)
    {
        $formatted = number_format($amount, 2);
        $currentLocale = app()->getLocale();

        if (!$symbol) {
            $currency = static::getDefault();
            $symbol = $currency ? $currency->symbol : ($currentLocale === 'ar' ? 'ر.س' : '$');
        }

        // Arabic: amount then symbol (right side)
        if ($currentLocale === 'ar') {
            return $formatted . ' ' . $symbol;
        }

        // English and others: symbol then amount (left side)
        return $symbol . $formatted;
    }
}
