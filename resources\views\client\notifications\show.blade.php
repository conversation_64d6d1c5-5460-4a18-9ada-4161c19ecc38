@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ __('Notification Details') }}</h1>
                    <p class="mt-2 text-gray-600 dark:text-gray-400">{{ __('View notification information') }}</p>
                </div>

                <a href="{{ route('client.notifications.index') }}"
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                    <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                    {{ __('Back to Notifications') }}
                </a>
            </div>
        </div>

        <!-- Notification Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
            <div class="p-8">
                <!-- Header with Icon and Status -->
                <div class="flex items-start justify-between mb-6">
                    <div class="flex items-center">
                        <!-- Icon -->
                        <div class="flex-shrink-0">
                            <div class="w-16 h-16 rounded-full flex items-center justify-center {{ !$notification->is_read ? 'bg-blue-100 dark:bg-blue-900' : 'bg-gray-100 dark:bg-gray-700' }}">
                                @switch($notification->type)
                                    @case('booking')
                                        <svg class="w-8 h-8 {{ !$notification->is_read ? 'text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-400' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        @break
                                    @case('payment')
                                        <svg class="w-8 h-8 {{ !$notification->is_read ? 'text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-400' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                        </svg>
                                        @break
                                    @case('rating')
                                        <svg class="w-8 h-8 {{ !$notification->is_read ? 'text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-400' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                        </svg>
                                        @break
                                    @default
                                        <svg class="w-8 h-8 {{ !$notification->is_read ? 'text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-400' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2z"></path>
                                        </svg>
                                @endswitch
                            </div>
                        </div>

                        <!-- Title and Type -->
                        <div class="ml-6">
                            <h2 class="text-2xl font-bold {{ !$notification->is_read ? 'text-gray-900 dark:text-white' : 'text-gray-700 dark:text-gray-300' }}">
                                {{ $notification->title }}
                            </h2>
                            <div class="flex items-center mt-2">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                    @switch($notification->type)
                                        @case('booking') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 @break
                                        @case('payment') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 @break
                                        @case('rating') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 @break
                                        @default bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200
                                    @endswitch">
                                    {{ ucfirst($notification->type) }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Status Badge -->
                    <div>
                        @if(!$notification->is_read)
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                {{ __('New') }}
                            </span>
                        @else
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                {{ __('Read') }}
                            </span>
                        @endif
                    </div>
                </div>

                <!-- Message Content -->
                <div class="mb-8">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Message') }}</h3>
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                        <p class="text-gray-700 dark:text-gray-300 leading-relaxed">
                            {{ $notification->message }}
                        </p>
                    </div>
                </div>

                <!-- Additional Data -->
                @if($notification->data && is_array($notification->data) && count($notification->data) > 0)
                    <div class="mb-8">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Additional Information') }}</h3>
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                @foreach($notification->data as $key => $value)
                                    <div class="border-b border-gray-200 dark:border-gray-600 pb-3 last:border-b-0">
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                                            {{ ucfirst(str_replace('_', ' ', $key)) }}
                                        </dt>
                                        <dd class="text-sm text-gray-900 dark:text-white">
                                            @if(is_array($value))
                                                @if(array_is_list($value))
                                                    <!-- Handle indexed arrays (lists) -->
                                                    <ul class="list-disc list-inside ml-2 space-y-1">
                                                        @foreach($value as $item)
                                                            <li class="text-sm">{{ $item }}</li>
                                                        @endforeach
                                                    </ul>
                                                @else
                                                    <!-- Handle associative arrays -->
                                                    @foreach($value as $subKey => $subValue)
                                                        <div class="ml-2 mb-1">
                                                            <span class="font-medium text-gray-600 dark:text-gray-400">{{ ucfirst(str_replace('_', ' ', $subKey)) }}:</span>
                                                            <span class="ml-1">
                                                                @if($subKey === 'booking_date' || $subKey === 'start_date' || $subKey === 'end_date' || $subKey === 'processed_at' || $subKey === 'service_completed_date' || $subKey === 'rating_deadline' || $subKey === 'start_time' || $subKey === 'end_time' || $subKey === 'release_date')
                                                                    {{ \Carbon\Carbon::parse($subValue)->format('M d, Y \a\t g:i A') }}
                                                                @elseif($subKey === 'amount' || $subKey === 'total_amount' || $subKey === 'processing_fee' || $subKey === 'net_amount')
                                                                    ${{ number_format($subValue, 2) }}
                                                                @elseif($subKey === 'quality_score')
                                                                    {{ number_format($subValue, 1) }}/5.0
                                                                @elseif($subKey === 'update_required')
                                                                    {{ $subValue ? __('Yes') : __('No') }}
                                                                @elseif(is_array($subValue))
                                                                    <ul class="list-disc list-inside ml-2 mt-1">
                                                                        @foreach($subValue as $item)
                                                                            <li class="text-xs">{{ $item }}</li>
                                                                        @endforeach
                                                                    </ul>
                                                                @else
                                                                    {{ is_string($subValue) ? $subValue : json_encode($subValue) }}
                                                                @endif
                                                            </span>
                                                        </div>
                                                    @endforeach
                                                @endif
                                            @elseif($key === 'booking_date' || $key === 'start_date' || $key === 'end_date')
                                                {{ \Carbon\Carbon::parse($value)->format('M d, Y \a\t g:i A') }}
                                            @elseif($key === 'amount' || $key === 'total_amount')
                                                ${{ number_format($value, 2) }}
                                            @elseif(is_string($value) && strlen($value) > 100)
                                                <div class="max-h-32 overflow-y-auto bg-white dark:bg-gray-800 p-3 rounded border">
                                                    {{ $value }}
                                                </div>
                                            @else
                                                {{ is_string($value) ? $value : json_encode($value) }}
                                            @endif
                                        </dd>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Metadata -->
                <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('Details') }}</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Received') }}</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                                {{ $notification->created_at->format('F j, Y \a\t g:i A') }}
                                <span class="text-gray-500 dark:text-gray-400">
                                    ({{ $notification->created_at->diffForHumans() }})
                                </span>
                            </dd>
                        </div>

                        @if($notification->is_read && $notification->read_at)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Read') }}</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                                    {{ $notification->read_at->format('F j, Y \a\t g:i A') }}
                                    <span class="text-gray-500 dark:text-gray-400">
                                        ({{ $notification->read_at->diffForHumans() }})
                                    </span>
                                </dd>
                            </div>
                        @endif

                        @if($notification->priority && $notification->priority !== 'medium')
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Priority') }}</dt>
                                <dd class="mt-1">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        @switch($notification->priority)
                                            @case('urgent') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 @break
                                            @case('high') bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200 @break
                                            @case('low') bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200 @break
                                            @default bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                        @endswitch">
                                        {{ ucfirst($notification->priority) }}
                                    </span>
                                </dd>
                            </div>
                        @endif

                        @if($notification->expires_at)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Expires') }}</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                                    {{ $notification->expires_at->format('F j, Y \a\t g:i A') }}
                                </dd>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="border-t border-gray-200 dark:border-gray-700 pt-6 mt-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            @if($notification->action_url && $notification->action_text)
                                <a href="{{ $notification->action_url }}"
                                   class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200">
                                    {{ $notification->action_text }}
                                </a>
                            @endif
                        </div>

                        <div class="flex items-center space-x-2">
                            @if(!$notification->is_read)
                                <button onclick="markAsRead({{ $notification->id }})"
                                        class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                                    {{ __('Mark as Read') }}
                                </button>
                            @endif

                            <form action="{{ route('client.notifications.destroy', $notification) }}" method="POST" class="inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit"
                                        class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
                                        onclick="return confirm('{{ __('Are you sure you want to delete this notification?') }}')">
                                    {{ __('Delete') }}
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function markAsRead(notificationId) {
    fetch(`/client/notifications/${notificationId}/mark-as-read`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}
</script>
@endsection
