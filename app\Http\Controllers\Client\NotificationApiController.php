<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use App\Traits\CreatesClientProfile;
use Carbon\Carbon;

class NotificationApiController extends Controller
{
    use CreatesClientProfile;

    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            $user = auth()->user();
            if (!$user || (!$user->hasRole('client') && !$user->hasRole('user'))) {
                abort(403, 'Access denied. Client or User role required.');
            }
            return $next($request);
        });
    }

    /**
     * Get unread notifications count for client
     */
    public function unreadCount()
    {
        $user = auth()->user();
        $client = $this->ensureClientProfile($user);

        if (!$client) {
            return response()->json(['count' => 0]);
        }

        $count = Notification::where('user_id', $user->id)
                           ->where('is_read', false)
                           ->count();

        return response()->json(['count' => $count]);
    }

    /**
     * Get recent notifications for client
     */
    public function recent()
    {
        $user = auth()->user();
        $client = $this->ensureClientProfile($user);

        if (!$client) {
            return response()->json(['notifications' => []]);
        }

        $notifications = Notification::where('user_id', $user->id)
                                   ->orderBy('created_at', 'desc')
                                   ->limit(10)
                                   ->get()
                                   ->map(function ($notification) {
                                       return [
                                           'id' => $notification->id,
                                           'title' => $notification->title,
                                           'message' => $notification->message,
                                           'type' => $notification->type,
                                           'priority' => $notification->priority ?? 'medium',
                                           'priority_color' => $this->getPriorityColor($notification->priority ?? 'medium'),
                                           'icon' => $this->getTypeIcon($notification->type),
                                           'is_read' => $notification->is_read,
                                           'action_url' => $notification->action_url,
                                           'created_at' => $notification->created_at->diffForHumans(),
                                       ];
                                   });

        return response()->json(['notifications' => $notifications]);
    }

    /**
     * Mark all notifications as read for client
     */
    public function markAllAsRead()
    {
        $user = auth()->user();
        $client = $this->ensureClientProfile($user);

        if (!$client) {
            return response()->json(['success' => false, 'message' => 'Client profile not found']);
        }

        $updated = Notification::where('user_id', $user->id)
                             ->where('is_read', false)
                             ->update([
                                 'is_read' => true,
                                 'read_at' => Carbon::now()
                             ]);

        return response()->json([
            'success' => true,
            'message' => __('All notifications marked as read'),
            'updated_count' => $updated
        ]);
    }

    /**
     * Get priority color for notification
     */
    private function getPriorityColor($priority)
    {
        switch ($priority) {
            case 'urgent':
                return 'red';
            case 'high':
                return 'orange';
            case 'low':
                return 'gray';
            case 'medium':
            default:
                return 'blue';
        }
    }

    /**
     * Get icon for notification type
     */
    private function getTypeIcon($type)
    {
        switch ($type) {
            case 'booking':
                return 'calendar';
            case 'payment':
                return 'credit-card';
            case 'rating':
                return 'star';
            case 'general':
            default:
                return 'bell';
        }
    }
}
