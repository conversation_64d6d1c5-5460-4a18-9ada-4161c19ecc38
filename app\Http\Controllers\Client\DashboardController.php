<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Worker;
use App\Models\Company;
use App\Models\Rating;
use App\Traits\CreatesClientProfile;
use Illuminate\Http\Request;
use Carbon\Carbon;

class DashboardController extends Controller
{
    use CreatesClientProfile;

    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            $user = auth()->user();
            if (!$user || (!$user->hasRole('client') && !$user->hasRole('user'))) {
                abort(403, 'Access denied. Client or User role required.');
            }
            return $next($request);
        });
    }

    /**
     * Show client dashboard
     */
    public function index()
    {
        $user = auth()->user();
        $client = $this->ensureClientProfile($user);

        if (!$client) {
            return redirect()->route('client.setup')->with('warning', __('Please complete your client profile first.'));
        }

        // Get client statistics
        $stats = $this->getClientStats($client);

        // Get recent activities
        $recentBookings = $this->getRecentBookings($client);
        $availableWorkers = $this->getAvailableWorkers();

        // Get upcoming bookings
        $upcomingBookings = $this->getUpcomingBookings($client);

        return view('client.dashboard', compact(
            'client',
            'stats',
            'recentBookings',
            'availableWorkers',
            'upcomingBookings'
        ));
    }

    /**
     * Get client statistics
     */
    private function getClientStats($client)
    {
        $totalBookings = Booking::where('client_id', $client->id)->count();
        $pendingBookings = Booking::where('client_id', $client->id)->where('status', 'pending')->count();
        $confirmedBookings = Booking::where('client_id', $client->id)->where('status', 'confirmed')->count();
        $completedBookings = Booking::where('client_id', $client->id)->where('status', 'completed')->count();
        $totalSpent = Booking::where('client_id', $client->id)
                           ->where('status', 'completed')
                           ->sum('total_amount');

        return [
            'total_bookings' => $totalBookings,
            'pending_bookings' => $pendingBookings,
            'confirmed_bookings' => $confirmedBookings,
            'completed_bookings' => $completedBookings,
            'total_spent' => $totalSpent,
        ];
    }

    /**
     * Get recent bookings
     */
    private function getRecentBookings($client)
    {
        return Booking::with(['worker', 'company'])
                     ->where('client_id', $client->id)
                     ->orderBy('created_at', 'desc')
                     ->limit(5)
                     ->get();
    }

    /**
     * Get available workers
     */
    private function getAvailableWorkers()
    {
        return Worker::with('company')
                    ->where('status', 'available')
                    ->where('is_active', true)
                    ->limit(6)
                    ->get();
    }

    /**
     * Get upcoming bookings
     */
    private function getUpcomingBookings($client)
    {
        return Booking::with(['worker', 'company'])
                     ->where('client_id', $client->id)
                     ->where('status', 'confirmed')
                     ->where('start_date', '>', now())
                     ->orderBy('start_date', 'asc')
                     ->limit(5)
                     ->get();
    }
}
