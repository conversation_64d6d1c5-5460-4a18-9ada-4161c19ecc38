<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Company extends Model
{
    protected $fillable = [
        'user_id',
        'company_name',
        'description',
        'commercial_register',
        'contact_info',
        'logo',
        'rating',
        'workers_count',
        'join_date',
        'bank_info',
        'is_active',
    ];

    protected $casts = [
        'contact_info' => 'array',
        'bank_info' => 'array',
        'join_date' => 'date',
        'rating' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function workers(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Worker::class);
    }

    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }
}
