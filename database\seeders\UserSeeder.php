<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Permission;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء المستخدم الأول وإعطاؤه دور Super Admin
        $user = User::first();

        if ($user) {
            // إعطاء دور Super Admin للمستخدم الأول
            $user->assignRole('Super Admin');

            // إعطاء جميع الصلاحيات مباشرة للمستخدم
            $permissions = Permission::all();
            $user->givePermissionTo($permissions);

            $this->command->info("تم إعطاء دور Super Admin وجميع الصلاحيات للمستخدم: {$user->name}");
            $this->command->info("عدد الصلاحيات: " . $user->getAllPermissions()->count());
        } else {
            // إنشاء مستخدم جديد إذا لم يوجد
            $user = User::create([
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'status' => true,
            ]);

            $user->assignRole('Super Admin');

            // إعطاء جميع الصلاحيات
            $permissions = Permission::all();
            $user->givePermissionTo($permissions);

            $this->command->info("تم إنشاء المستخدم الأول وإعطاؤه دور Super Admin وجميع الصلاحيات");
        }
    }
}
