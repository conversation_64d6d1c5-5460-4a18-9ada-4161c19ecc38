<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Spatie\Permission\Models\Role;

class TestUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء مستخدم تجريبي بدور "user"
        $testUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'مستخدم تجريبي',
                'password' => bcrypt('password'),
                'status' => true,
            ]
        );

        // التأكد من وجود دور "user"
        $userRole = Role::firstOrCreate(['name' => 'user'], ['guard_name' => 'web']);
        
        // إعطاء الدور للمستخدم
        if (!$testUser->hasRole('user')) {
            $testUser->assignRole('user');
        }

        // إنشاء مستخدم تجريبي آخر بدور "client"
        $testClient = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'عميل تجريبي',
                'password' => bcrypt('password'),
                'status' => true,
            ]
        );

        // التأكد من وجود دور "client"
        $clientRole = Role::firstOrCreate(['name' => 'client'], ['guard_name' => 'web']);
        
        // إعطاء الدور للعميل
        if (!$testClient->hasRole('client')) {
            $testClient->assignRole('client');
        }

        // إنشاء ملف عميل للعميل التجريبي
        if (!$testClient->client) {
            \App\Models\Client::create([
                'user_id' => $testClient->id,
                'children_count' => 2,
                'home_size' => 'large',
                'floors_count' => 2,
                'people_count' => 4,
                'has_yard' => true,
                'additional_info' => 'Test client profile',
                'rating' => 0,
                'is_active' => true,
            ]);
        }

        $this->command->info('تم إنشاء المستخدمين التجريبيين:');
        $this->command->info('- <EMAIL> (دور: user) - كلمة المرور: password');
        $this->command->info('- <EMAIL> (دور: client) - كلمة المرور: password');
    }
}
