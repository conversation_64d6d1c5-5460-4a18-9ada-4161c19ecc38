<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PermissionGroup;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PaymentPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🔐 Creating Payment Permissions and Groups');
        $this->command->info('==============================================');

        // إنشاء مجموعة المدفوعات
        $paymentGroup = PermissionGroup::firstOrCreate([
            'name' => 'payments'
        ], [
            'display_name' => 'Payment Management',
            'description' => 'Manage payments, receipts, and financial transactions',
            'icon' => 'credit-card',
            'sort_order' => 10,
            'is_protected' => false
        ]);

        $this->command->info("✅ Created payment group: {$paymentGroup->display_name}");

        // إنشاء صلاحيات المدفوعات
        $permissions = [
            'payments.view' => 'View Payments',
            'payments.create' => 'Create Payments',
            'payments.edit' => 'Edit Payments',
            'payments.delete' => 'Delete Payments',
            'payments.receipts' => 'View Receipts',
            'payments.reports' => 'Payment Reports'
        ];

        foreach ($permissions as $name => $displayName) {
            $permission = Permission::firstOrCreate([
                'name' => $name
            ], [
                'name' => $name,
                'guard_name' => 'web'
            ]);

            $this->command->info("✅ Created permission: {$displayName}");
        }

        // إعطاء صلاحيات للأدوار الموجودة
        $this->assignPermissionsToRoles();

        $this->command->info('');
        $this->command->info('🎉 Payment permissions created successfully!');
        $this->command->info('📊 Summary:');
        $this->command->info("   • Payment group created");
        $this->command->info("   • " . count($permissions) . " permissions created");
        $this->command->info('');
        $this->command->info('🔗 Next steps:');
        $this->command->info('   • Configure role permissions in admin panel');
        $this->command->info('   • Test payment access for different user roles');
    }

    private function assignPermissionsToRoles()
    {
        // إعطاء جميع صلاحيات المدفوعات للأدمن
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $adminRole->givePermissionTo([
                'payments.view',
                'payments.create',
                'payments.edit',
                'payments.delete',
                'payments.receipts',
                'payments.reports'
            ]);
            $this->command->info("✅ Assigned all payment permissions to admin role");
        }

        // إعطاء صلاحيات محدودة للشركات
        $companyRole = Role::where('name', 'company')->first();
        if ($companyRole) {
            $companyRole->givePermissionTo([
                'payments.view',
                'payments.receipts'
            ]);
            $this->command->info("✅ Assigned view permissions to company role");
        }

        // إعطاء صلاحيات محدودة للعملاء
        $clientRole = Role::where('name', 'client')->first();
        if ($clientRole) {
            $clientRole->givePermissionTo([
                'payments.view',
                'payments.receipts'
            ]);
            $this->command->info("✅ Assigned view permissions to client role");
        }

        $userRole = Role::where('name', 'user')->first();
        if ($userRole) {
            $userRole->givePermissionTo([
                'payments.view',
                'payments.receipts'
            ]);
            $this->command->info("✅ Assigned view permissions to user role");
        }
    }
}
