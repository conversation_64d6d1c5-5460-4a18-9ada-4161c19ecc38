<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Kernel::class);
$kernel->bootstrap();

// Set locale to Arabic
app()->setLocale('ar');

echo "Testing Arabic translations for Company Dashboard:\n\n";

$translations = [
    'Company Dashboard',
    'Welcome back',
    'Total Workers',
    'active',
    'Total Bookings',
    'this month',
    'Pending Approval',
    'Review now',
    'Average Rating',
    'reviews',
    'Quick Actions',
    'Add Worker',
    'Add Schedule',
    'View Bookings',
    'View Ratings',
    'Recent Bookings',
    'View all',
    'No bookings yet',
    'Recent Ratings',
    'Company Rating',
    'Worker Rating',
    'No ratings yet',
    'Pending',
    'Confirmed',
    'Completed',
    'Cancelled'
];

foreach ($translations as $key) {
    $translation = __($key);
    $status = $translation !== $key ? '✅' : '❌';
    echo "{$status} '{$key}' => '{$translation}'\n";
}

echo "\nTranslation test completed!\n";
