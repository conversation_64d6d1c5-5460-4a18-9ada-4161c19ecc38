@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Page Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                عرض الدور: {{ $role->name }}
            </h1>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="py-12">
                <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- معلومات الدور -->
                        <div class="lg:col-span-2">
                            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                                <div class="p-6">
                                    <div class="flex items-center space-x-4 rtl:space-x-reverse mb-6">
                                        <div class="flex-shrink-0 h-16 w-16">
                                            <div class="h-16 w-16 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                                                <svg class="h-8 w-8 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                                                </svg>
                                            </div>
                                        </div>
                                        <div>
                                            <h3 class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $role->name }}</h3>
                                            <p class="text-gray-600 dark:text-gray-400">{{ $role->guard_name }}</p>
                                        </div>
                                    </div>
        
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">معلومات الدور</h4>
                                            <dl class="space-y-2">
                                                <div>
                                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">اسم الدور</dt>
                                                    <dd class="text-sm text-gray-900 dark:text-gray-100">{{ $role->name }}</dd>
                                                </div>
                                                <div>
                                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Guard Name</dt>
                                                    <dd class="text-sm text-gray-900 dark:text-gray-100">{{ $role->guard_name }}</dd>
                                                </div>
                                                <div>
                                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">تاريخ الإنشاء</dt>
                                                    <dd class="text-sm text-gray-900 dark:text-gray-100">{{ $role->created_at->format('Y-m-d H:i') }}</dd>
                                                </div>
                                                <div>
                                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">آخر تحديث</dt>
                                                    <dd class="text-sm text-gray-900 dark:text-gray-100">{{ $role->updated_at->format('Y-m-d H:i') }}</dd>
                                                </div>
                                                <div>
                                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">عدد المستخدمين</dt>
                                                    <dd class="text-sm text-gray-900 dark:text-gray-100">{{ $role->users->count() }} مستخدم</dd>
                                                </div>
                                                <div>
                                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">عدد الصلاحيات</dt>
                                                    <dd class="text-sm text-gray-900 dark:text-gray-100">{{ $role->permissions->count() }} صلاحية</dd>
                                                </div>
                                            </dl>
                                        </div>
        
                                        <div>
                                            <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">الصلاحيات</h4>
                                            <div class="max-h-64 overflow-y-auto">
                                                @php
                                                    $groupedPermissions = $role->permissions->groupBy(function ($permission) {
                                                        return explode('.', $permission->name)[0];
                                                    });
                                                @endphp
        
                                                @forelse($groupedPermissions as $group => $permissions)
                                                    <div class="mb-4">
                                                        <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 capitalize">{{ ucfirst($group) }}</h5>
                                                        <div class="flex flex-wrap gap-1">
                                                            @foreach($permissions as $permission)
                                                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                                                    {{ $permission->name }}
                                                                </span>
                                                            @endforeach
                                                        </div>
                                                    </div>
                                                @empty
                                                    <p class="text-gray-500 dark:text-gray-400 text-sm">لا توجد صلاحيات مخصصة لهذا الدور</p>
                                                @endforelse
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
        
                        <!-- المستخدمون المرتبطون بالدور -->
                        <div>
                            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                                <div class="p-6">
                                    <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">المستخدمون ({{ $role->users->count() }})</h4>
                                    <div class="space-y-3">
                                        @forelse($role->users as $user)
                                            <div class="flex items-center space-x-3 rtl:space-x-reverse">
                                                <img class="h-8 w-8 rounded-full border border-gray-300 dark:border-gray-600" src="{{ $user->avatar_url }}" alt="{{ $user->name }}">
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">{{ $user->name }}</p>
                                                    <p class="text-sm text-gray-500 dark:text-gray-400 truncate">{{ $user->email }}</p>
                                                </div>
                                                <div class="flex-shrink-0">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $user->status ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300' }}">
                                                        {{ $user->status ? 'نشط' : 'غير نشط' }}
                                                    </span>
                                                </div>
                                            </div>
                                        @empty
                                            <p class="text-gray-500 dark:text-gray-400 text-center py-4">لا يوجد مستخدمون مرتبطون بهذا الدور</p>
                                        @endforelse
                                    </div>
        
                                    @if($role->users->count() > 0)
                                        <div class="mt-4">
                                            @can('users.view')
                                                <a href="{{ route('users.index', ['role' => $role->name]) }}" class="text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 text-sm font-medium">
                                                    عرض جميع المستخدمين →
                                                </a>
                                            @endcan
                                        </div>
                                    @endif
                                </div>
                            </div>
        
                            <!-- إجراءات سريعة -->
                            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mt-6">
                                <div class="p-6">
                                    <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">إجراءات سريعة</h4>
                                    <div class="space-y-3">
                                        @can('roles.edit')
                                            <a href="{{ route('roles.edit', $role) }}" class="w-full text-left px-4 py-2 text-sm text-blue-700 dark:text-blue-300 bg-blue-50 dark:bg-blue-900/30 hover:bg-blue-100 dark:hover:bg-blue-900/50 rounded-lg transition block">
                                                تعديل الدور
                                            </a>
                                        @endcan
        
                                        @can('roles.delete')
                                            @if($role->users->count() == 0)
                                                <form action="{{ route('roles.destroy', $role) }}" method="POST"
                                                      onsubmit="return confirm('هل أنت متأكد من حذف هذا الدور؟')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="w-full text-left px-4 py-2 text-sm text-red-700 dark:text-red-300 bg-red-50 dark:bg-red-900/30 hover:bg-red-100 dark:hover:bg-red-900/50 rounded-lg transition">
                                                        حذف الدور
                                                    </button>
                                                </form>
                                            @else
                                                <div class="w-full text-left px-4 py-2 text-sm text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                                    لا يمكن حذف الدور (مرتبط بمستخدمين)
                                                </div>
                                            @endif
                                        @endcan
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </div>
</div>
@endsection
