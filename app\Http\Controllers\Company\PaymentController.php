<?php

namespace App\Http\Controllers\Company;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Company;
use Illuminate\Http\Request;

class PaymentController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (!auth()->user()->hasRole('company')) {
                abort(403, 'Access denied. Company role required.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of company payments.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $company = Company::where('user_id', $user->id)->first();

        if (!$company) {
            return redirect()->route('company.dashboard')->with('error', __('Company profile not found.'));
        }

        $query = Booking::with(['worker', 'client.user'])
                       ->where('company_id', $company->id)
                       ->whereNotNull('transaction_id');

        // Filter by payment status
        if ($request->filled('status')) {
            $query->where('payment_status', $request->status);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('transaction_id', 'like', "%{$search}%")
                  ->orWhere('id', 'like', "%{$search}%")
                  ->orWhereHas('worker', function ($workerQuery) use ($search) {
                      $workerQuery->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('client.user', function ($clientQuery) use ($search) {
                      $clientQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $payments = $query->orderBy('updated_at', 'desc')->paginate(10);

        // Calculate statistics
        $stats = [
            'total_earned' => Booking::where('company_id', $company->id)
                                   ->where('payment_status', 'paid')
                                   ->sum('total_amount'),
            'total_pending' => Booking::where('company_id', $company->id)
                                    ->where('payment_status', 'pending')
                                    ->sum('total_amount'),
            'total_transactions' => Booking::where('company_id', $company->id)
                                          ->whereNotNull('transaction_id')
                                          ->count(),
        ];

        return view('company.payments.index', compact('payments', 'stats'));
    }

    /**
     * Display payment receipt.
     */
    public function receipt($transactionId)
    {
        $user = auth()->user();
        $company = Company::where('user_id', $user->id)->first();

        if (!$company) {
            return redirect()->route('company.dashboard')->with('error', __('Company profile not found.'));
        }

        $booking = Booking::with(['worker', 'client.user', 'company'])
                         ->where('company_id', $company->id)
                         ->where('transaction_id', $transactionId)
                         ->first();

        if (!$booking) {
            abort(404, __('Payment receipt not found.'));
        }

        return view('company.payments.receipt', compact('booking'));
    }

    /**
     * Download payment receipt as PDF.
     */
    public function downloadReceipt($transactionId)
    {
        $user = auth()->user();
        $company = Company::where('user_id', $user->id)->first();

        if (!$company) {
            return redirect()->route('company.dashboard')->with('error', __('Company profile not found.'));
        }

        $booking = Booking::with(['worker', 'client.user', 'company'])
                         ->where('company_id', $company->id)
                         ->where('transaction_id', $transactionId)
                         ->first();

        if (!$booking) {
            abort(404, __('Payment receipt not found.'));
        }

        // Return PDF-optimized view for printing
        return view('company.payments.receipt-pdf', compact('booking'));
    }

    /**
     * Show payment details.
     */
    public function show($bookingId)
    {
        $user = auth()->user();
        $company = Company::where('user_id', $user->id)->first();

        if (!$company) {
            return redirect()->route('company.dashboard')->with('error', __('Company profile not found.'));
        }

        $booking = Booking::with(['worker', 'client.user'])
                         ->where('company_id', $company->id)
                         ->where('id', $bookingId)
                         ->first();

        if (!$booking) {
            abort(404, __('Payment not found.'));
        }

        return view('company.payments.show', compact('booking'));
    }
}
