<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء الصلاحيات
        $permissions = [
            // صلاحيات المستخدمين
            'users.view',
            'users.create',
            'users.edit',
            'users.delete',

            // صلاحيات الأدوار
            'roles.view',
            'roles.create',
            'roles.edit',
            'roles.delete',

            // صلاحيات الصلاحيات
            'permissions.view',
            'permissions.create',
            'permissions.edit',
            'permissions.delete',

            // صلاحيات إدارية
            'admin.access',
            'dashboard.view',
            'settings.edit',
            'reports.view',
            'logs.view',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate([
                'name' => $permission,
                'guard_name' => 'web'
            ]);
        }

        // إنشاء الأدوار الأساسية
        $adminRole = Role::firstOrCreate([
            'name' => 'Super Admin',
            'guard_name' => 'web'
        ]);

        $managerRole = Role::firstOrCreate([
            'name' => 'Manager',
            'guard_name' => 'web'
        ]);

        $userRole = Role::firstOrCreate([
            'name' => 'User',
            'guard_name' => 'web'
        ]);

        // إعطاء جميع الصلاحيات للمدير العام
        $adminRole->givePermissionTo(Permission::all());

        // إعطاء صلاحيات محددة للمدير
        $managerRole->givePermissionTo([
            'users.view',
            'users.create',
            'users.edit',
            'roles.view',
            'dashboard.view',
            'reports.view',
        ]);

        // إعطاء صلاحيات أساسية للمستخدم العادي
        $userRole->givePermissionTo([
            'dashboard.view',
        ]);

        $this->command->info('تم إنشاء الصلاحيات والأدوار بنجاح!');
    }
}
