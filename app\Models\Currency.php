<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Currency extends Model
{
    protected $fillable = [
        'name',
        'code',
        'symbol',
        'exchange_rate',
        'is_active',
        'is_default',
        'position'
    ];

    protected $casts = [
        'exchange_rate' => 'decimal:4',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
    ];

    /**
     * Get the default currency
     */
    public static function getDefault()
    {
        return static::where('is_default', true)->first() ?? static::first();
    }

    /**
     * Set this currency as default
     */
    public function setAsDefault()
    {
        // Remove default from all currencies
        static::where('is_default', true)->update(['is_default' => false]);

        // Set this currency as default
        $this->update(['is_default' => true]);
    }

    /**
     * Format amount with currency
     */
    public function format($amount)
    {
        $formatted = number_format($amount, 2);
        $currentLocale = app()->getLocale();

        // For Arabic language, always put symbol after the amount (right side)
        if ($currentLocale === 'ar') {
            return $formatted . ' ' . $this->symbol;
        }

        // For other languages, use the position setting
        if ($this->position === 'before') {
            return $this->symbol . $formatted;
        } else {
            return $formatted . ' ' . $this->symbol;
        }
    }

    /**
     * Scope for active currencies
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
