<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules;

class CompanyRegistrationController extends Controller
{
    /**
     * Show the company registration form.
     */
    public function create()
    {
        return view('auth.company-register');
    }

    /**
     * Handle company registration request.
     */
    public function store(Request $request)
    {
        $request->validate([
            // Personal Information
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'phone' => ['required', 'string', 'max:20'],
            'national_id' => ['required', 'string', 'max:20', 'unique:users'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            
            // Company Information
            'company_name' => ['required', 'string', 'max:255'],
            'commercial_registration' => ['required', 'string', 'max:50', 'unique:companies'],
            'tax_number' => ['nullable', 'string', 'max:50'],
            'establishment_year' => ['nullable', 'integer', 'min:1900', 'max:' . date('Y')],
            'address' => ['required', 'string'],
            'description' => ['nullable', 'string'],
            
            // Documents
            'commercial_registration_file' => ['required', 'file', 'mimes:pdf,jpg,jpeg,png', 'max:2048'],
            'national_id_file' => ['required', 'file', 'mimes:pdf,jpg,jpeg,png', 'max:2048'],
            
            // Terms
            'terms' => ['required', 'accepted'],
        ]);

        try {
            // Upload documents
            $commercialRegPath = $request->file('commercial_registration_file')->store('company-documents/commercial-registrations', 'public');
            $nationalIdPath = $request->file('national_id_file')->store('company-documents/national-ids', 'public');

            // Create user account (inactive by default)
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'national_id' => $request->national_id,
                'password' => Hash::make($request->password),
                'status' => false, // Inactive until admin approval
                'email_verified_at' => now(), // Auto-verify email for companies
            ]);

            // Create company record
            $company = Company::create([
                'user_id' => $user->id,
                'company_name' => $request->company_name,
                'commercial_registration' => $request->commercial_registration,
                'tax_number' => $request->tax_number,
                'establishment_year' => $request->establishment_year,
                'address' => $request->address,
                'description' => $request->description,
                'commercial_registration_file' => $commercialRegPath,
                'national_id_file' => $nationalIdPath,
                'is_active' => false, // Inactive until admin approval
                'registration_status' => 'pending', // pending, approved, rejected
            ]);

            // Assign company role (but user is still inactive)
            $user->assignRole('company');

            // Send notification to admin (you can implement this later)
            // $this->notifyAdminOfNewRegistration($company);

            return redirect()->route('company.registration.success')
                           ->with('success', __('Registration submitted successfully! Please wait for admin approval.'));

        } catch (\Exception $e) {
            // Clean up uploaded files if something goes wrong
            if (isset($commercialRegPath)) {
                Storage::disk('public')->delete($commercialRegPath);
            }
            if (isset($nationalIdPath)) {
                Storage::disk('public')->delete($nationalIdPath);
            }

            return back()->withInput()->withErrors(['error' => __('Registration failed. Please try again.')]);
        }
    }

    /**
     * Show registration success page.
     */
    public function success()
    {
        return view('auth.company-registration-success');
    }

    /**
     * Show pending registrations for admin approval.
     */
    public function pending()
    {
        $this->authorize('companies.approve');

        $pendingCompanies = Company::with('user')
                                  ->where('registration_status', 'pending')
                                  ->orderBy('created_at', 'desc')
                                  ->paginate(15);

        return view('admin.companies.pending', compact('pendingCompanies'));
    }

    /**
     * Approve company registration.
     */
    public function approve(Company $company)
    {
        $this->authorize('companies.approve');

        if ($company->registration_status !== 'pending') {
            return back()->withErrors(['error' => __('This registration has already been processed.')]);
        }

        try {
            // Activate company
            $company->update([
                'is_active' => true,
                'registration_status' => 'approved',
                'approved_at' => now(),
                'approved_by' => auth()->id(),
            ]);

            // Activate user account
            $company->user->update([
                'status' => true,
            ]);

            // Send approval notification to company owner
            // $this->notifyCompanyOfApproval($company);

            return back()->with('success', __('Company registration approved successfully!'));

        } catch (\Exception $e) {
            return back()->withErrors(['error' => __('Failed to approve registration. Please try again.')]);
        }
    }

    /**
     * Reject company registration.
     */
    public function reject(Request $request, Company $company)
    {
        $this->authorize('companies.approve');

        if ($company->registration_status !== 'pending') {
            return back()->withErrors(['error' => __('This registration has already been processed.')]);
        }

        $request->validate([
            'rejection_reason' => ['required', 'string', 'max:500'],
        ]);

        try {
            // Update company status
            $company->update([
                'registration_status' => 'rejected',
                'rejection_reason' => $request->rejection_reason,
                'rejected_at' => now(),
                'rejected_by' => auth()->id(),
            ]);

            // Send rejection notification to company owner
            // $this->notifyCompanyOfRejection($company);

            return back()->with('success', __('Company registration rejected.'));

        } catch (\Exception $e) {
            return back()->withErrors(['error' => __('Failed to reject registration. Please try again.')]);
        }
    }

    /**
     * Show company registration details for admin review.
     */
    public function show(Company $company)
    {
        $this->authorize('companies.view');

        $company->load('user');

        return view('admin.companies.registration-details', compact('company'));
    }
}
