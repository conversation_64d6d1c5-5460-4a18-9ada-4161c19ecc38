@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">

    <!-- Page Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ __('Create New Booking') }}</h1>
                    <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">{{ __('Add a new booking to the system') }}</p>
                </div>
                <div>
                    <a href="{{ route('admin.bookings.index') }}"
                       class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-md transition duration-200">
                        <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        {{ __('Back to Bookings') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div class="px-6 py-6">
                <form method="POST" action="{{ route('admin.bookings.store') }}" class="space-y-6">
                    @csrf

                    <!-- Client Selection -->
                    <div>
                        <label for="client_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {{ __('Client') }} <span class="text-red-500">*</span>
                        </label>
                        <select name="client_id"
                                id="client_id"
                                required
                                class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('client_id') border-red-500 @enderror">
                            <option value="">{{ __('Select Client') }}</option>
                            @foreach($clients as $client)
                                <option value="{{ $client->id }}" {{ old('client_id') == $client->id ? 'selected' : '' }}>
                                    {{ $client->user->name }} ({{ $client->user->email }})
                                </option>
                            @endforeach
                        </select>
                        @error('client_id')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Worker Selection -->
                    <div>
                        <label for="worker_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {{ __('Worker') }} <span class="text-red-500">*</span>
                        </label>
                        <select name="worker_id"
                                id="worker_id"
                                required
                                class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('worker_id') border-red-500 @enderror">
                            <option value="">{{ __('Select Worker') }}</option>
                            @foreach($workers as $worker)
                                <option value="{{ $worker->id }}"
                                        data-hourly="{{ $worker->hourly_rate }}"
                                        data-daily="{{ $worker->daily_rate }}"
                                        data-monthly="{{ $worker->monthly_rate }}"
                                        data-yearly="{{ $worker->yearly_rate }}"
                                        {{ old('worker_id') == $worker->id ? 'selected' : '' }}>
                                    {{ $worker->name }} - {{ $worker->category_text }}
                                </option>
                            @endforeach
                        </select>
                        @error('worker_id')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Company Selection -->
                    <div>
                        <label for="company_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {{ __('Company') }} <span class="text-red-500">*</span>
                        </label>
                        <select name="company_id"
                                id="company_id"
                                required
                                class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('company_id') border-red-500 @enderror">
                            <option value="">{{ __('Select Company') }}</option>
                            @foreach($companies as $company)
                                <option value="{{ $company->id }}" {{ old('company_id') == $company->id ? 'selected' : '' }}>
                                    {{ $company->company_name }}
                                </option>
                            @endforeach
                        </select>
                        @error('company_id')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Booking Type -->
                    <div>
                        <label for="booking_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {{ __('Booking Type') }} <span class="text-red-500">*</span>
                        </label>
                        <select name="booking_type"
                                id="booking_type"
                                required
                                class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('booking_type') border-red-500 @enderror">
                            <option value="">{{ __('Select Booking Type') }}</option>
                            <option value="hourly" {{ old('booking_type') === 'hourly' ? 'selected' : '' }}>{{ __('Hourly') }}</option>
                            <option value="daily" {{ old('booking_type') === 'daily' ? 'selected' : '' }}>{{ __('Daily') }}</option>
                            <option value="monthly" {{ old('booking_type') === 'monthly' ? 'selected' : '' }}>{{ __('Monthly') }}</option>
                            <option value="yearly" {{ old('booking_type') === 'yearly' ? 'selected' : '' }}>{{ __('Yearly') }}</option>
                        </select>
                        @error('booking_type')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Date Range -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('Start Date') }} <span class="text-red-500">*</span>
                            </label>
                            <input type="datetime-local"
                                   name="start_date"
                                   id="start_date"
                                   value="{{ old('start_date') }}"
                                   required
                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('start_date') border-red-500 @enderror">
                            @error('start_date')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {{ __('End Date') }} <span class="text-red-500">*</span>
                            </label>
                            <input type="datetime-local"
                                   name="end_date"
                                   id="end_date"
                                   value="{{ old('end_date') }}"
                                   required
                                   class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('end_date') border-red-500 @enderror">
                            @error('end_date')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Total Amount -->
                    <div>
                        <label for="total_amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {{ __('Total Amount') }} <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 start-0 ps-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 dark:text-gray-400">$</span>
                            </div>
                            <input type="number"
                                   name="total_amount"
                                   id="total_amount"
                                   value="{{ old('total_amount') }}"
                                   step="0.01"
                                   min="0"
                                   required
                                   readonly
                                   class="w-full ps-8 rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('total_amount') border-red-500 @enderror">
                        </div>
                        @error('total_amount')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ __('Amount will be calculated automatically based on worker rates and duration') }}</p>
                    </div>

                    <!-- Payment Method -->
                    <div>
                        <label for="payment_method" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {{ __('Payment Method') }}
                        </label>
                        <select name="payment_method"
                                id="payment_method"
                                class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('payment_method') border-red-500 @enderror">
                            <option value="">{{ __('Select Payment Method') }}</option>
                            <option value="cash" {{ old('payment_method') === 'cash' ? 'selected' : '' }}>{{ __('Cash') }}</option>
                            <option value="credit_card" {{ old('payment_method') === 'credit_card' ? 'selected' : '' }}>{{ __('Credit Card') }}</option>
                            <option value="bank_transfer" {{ old('payment_method') === 'bank_transfer' ? 'selected' : '' }}>{{ __('Bank Transfer') }}</option>
                            <option value="online" {{ old('payment_method') === 'online' ? 'selected' : '' }}>{{ __('Online Payment') }}</option>
                        </select>
                        @error('payment_method')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Notes -->
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {{ __('Notes') }}
                        </label>
                        <textarea name="notes"
                                  id="notes"
                                  rows="4"
                                  placeholder="{{ __('Additional notes or special requirements...') }}"
                                  class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('notes') border-red-500 @enderror">{{ old('notes') }}</textarea>
                        @error('notes')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Submit Buttons -->
                    <div class="flex justify-end space-x-3 rtl:space-x-reverse pt-6 border-t border-gray-200 dark:border-gray-700">
                        <a href="{{ route('admin.bookings.index') }}"
                           class="inline-flex items-center px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium rounded-md transition duration-200">
                            {{ __('Cancel') }}
                        </a>
                        <button type="submit"
                                class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition duration-200">
                            <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            {{ __('Create Booking') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const workerSelect = document.getElementById('worker_id');
    const bookingTypeSelect = document.getElementById('booking_type');
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    const totalAmountInput = document.getElementById('total_amount');

    function calculateAmount() {
        const selectedWorker = workerSelect.options[workerSelect.selectedIndex];
        const bookingType = bookingTypeSelect.value;
        const startDate = new Date(startDateInput.value);
        const endDate = new Date(endDateInput.value);

        if (!selectedWorker.value || !bookingType || !startDate || !endDate) {
            totalAmountInput.value = '';
            return;
        }

        let rate = 0;
        switch(bookingType) {
            case 'hourly':
                rate = parseFloat(selectedWorker.dataset.hourly) || 0;
                break;
            case 'daily':
                rate = parseFloat(selectedWorker.dataset.daily) || 0;
                break;
            case 'monthly':
                rate = parseFloat(selectedWorker.dataset.monthly) || 0;
                break;
            case 'yearly':
                rate = parseFloat(selectedWorker.dataset.yearly) || 0;
                break;
        }

        // Calculate duration based on booking type
        const diffTime = Math.abs(endDate - startDate);
        let duration = 1;

        switch(bookingType) {
            case 'hourly':
                duration = Math.ceil(diffTime / (1000 * 60 * 60)); // hours
                break;
            case 'daily':
                duration = Math.ceil(diffTime / (1000 * 60 * 60 * 24)); // days
                break;
            case 'monthly':
                duration = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 30)); // months
                break;
            case 'yearly':
                duration = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 365)); // years
                break;
        }

        const totalAmount = rate * duration;
        totalAmountInput.value = totalAmount.toFixed(2);
    }

    workerSelect.addEventListener('change', calculateAmount);
    bookingTypeSelect.addEventListener('change', calculateAmount);
    startDateInput.addEventListener('change', calculateAmount);
    endDateInput.addEventListener('change', calculateAmount);
});
</script>
@endsection
