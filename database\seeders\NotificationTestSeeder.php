<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Notification;
use App\Models\User;

class NotificationTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first user (admin)
        $user = User::first();

        if (!$user) {
            $this->command->info('No users found. Please create a user first.');
            return;
        }

        // Create test notifications
        $notifications = [
            [
                'type' => 'booking_created',
                'title' => 'New Booking Created',
                'message' => 'A new booking #1001 has been created for worker <PERSON>.',
                'priority' => 'high',
                'icon' => 'calendar',
                'color' => 'blue',
                'action_url' => '/admin/bookings/1',
                'action_text' => 'View Booking',
                'is_read' => false,
            ],
            [
                'type' => 'payment_received',
                'title' => 'Payment Received',
                'message' => 'Payment of $500 has been received for booking #1001.',
                'priority' => 'medium',
                'icon' => 'credit-card',
                'color' => 'green',
                'action_url' => '/admin/bookings/1',
                'action_text' => 'View Payment',
                'is_read' => false,
            ],
            [
                'type' => 'rating_received',
                'title' => 'New Rating Received',
                'message' => 'Worker Sarah Ahmed received a 5-star rating from client <PERSON> Doe.',
                'priority' => 'medium',
                'icon' => 'star',
                'color' => 'yellow',
                'action_url' => '/admin/ratings/1',
                'action_text' => 'View Rating',
                'is_read' => true,
            ],
            [
                'type' => 'worker_assigned',
                'title' => 'Worker Assigned',
                'message' => 'Worker Maria Garcia has been assigned to booking #1002.',
                'priority' => 'medium',
                'icon' => 'calendar',
                'color' => 'blue',
                'action_url' => '/admin/bookings/2',
                'action_text' => 'View Booking',
                'is_read' => false,
            ],
            [
                'type' => 'system_maintenance',
                'title' => 'Scheduled Maintenance',
                'message' => 'System maintenance is scheduled for tonight from 2:00 AM to 4:00 AM.',
                'priority' => 'urgent',
                'icon' => 'wrench-screwdriver',
                'color' => 'red',
                'expires_at' => now()->addDays(1),
                'is_read' => false,
            ],
            [
                'type' => 'booking_confirmed',
                'title' => 'Booking Confirmed',
                'message' => 'Booking #1003 has been confirmed and scheduled for tomorrow.',
                'priority' => 'medium',
                'icon' => 'calendar',
                'color' => 'green',
                'action_url' => '/admin/bookings/3',
                'action_text' => 'View Booking',
                'is_read' => true,
            ],
            [
                'type' => 'account_updated',
                'title' => 'Profile Updated',
                'message' => 'Your profile information has been successfully updated.',
                'priority' => 'low',
                'icon' => 'user',
                'color' => 'gray',
                'is_read' => true,
            ],
        ];

        foreach ($notifications as $notificationData) {
            Notification::create([
                'type' => $notificationData['type'],
                'title' => $notificationData['title'],
                'message' => $notificationData['message'],
                'user_id' => $user->id,
                'priority' => $notificationData['priority'],
                'icon' => $notificationData['icon'] ?? null,
                'color' => $notificationData['color'] ?? null,
                'action_url' => $notificationData['action_url'] ?? null,
                'action_text' => $notificationData['action_text'] ?? null,
                'is_read' => $notificationData['is_read'] ?? false,
                'read_at' => $notificationData['is_read'] ? now() : null,
                'expires_at' => $notificationData['expires_at'] ?? null,
                'created_at' => now()->subMinutes(rand(1, 1440)), // Random time in last 24 hours
            ]);
        }

        $this->command->info('Test notifications created successfully!');
    }
}
